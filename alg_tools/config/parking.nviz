Panels:
  - Class: nviz_common/Displays
    Help Height: 78
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /Global Options1
        - /Parking Slot1
        - /Parking Slot1/Namespaces1
        - /uss1
        - /pnc_fsm_text1
      Splitter Ratio: 0.4897494316101074
    Tree Height: 363
  - Class: nviz_common/Selection
    Name: Selection
  - Class: nviz_common/Tool Properties
    Expanded:
      - /2D Goal Pose1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: nviz_common/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: nviz_common/Time
    Experimental: false
    Name: Time
    SyncMode: 0
    SyncSource: Freespace_vru
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.30000001192092896
      Cell Size: 1
      Class: nviz_default_plugins/Grid
      Color: 160; 160; 164
      Enabled: true
      Line Style:
        Line Width: 0.029999999329447746
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 30
      Reference Frame: <Fixed Frame>
      Value: true
    - Class: nviz_default_plugins/Marker
      Enabled: true
      Name: Ego Car
      Namespaces:
        ego: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /ego_car_marker
      Value: true
    - Class: nviz_default_plugins/Marker
      Enabled: true
      Name: trajectory_out occ polygon
      Namespaces:
        {}
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /viz/trajectory_out_occ_polygon
      Value: true
    - Class: nviz_default_plugins/MarkerArray
      Enabled: true
      Name: Ego Car Occ Space
      Namespaces:
        ego: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /viz/ego_occ_space
      Value: true
    - Class: nviz_default_plugins/MarkerArray
      Enabled: true
      Name: Parking Slot
      Namespaces:
        {}
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /viz/fusion/parking_slot
      Value: true
    - Class: nviz_default_plugins/MarkerArray
      Enabled: false
      Name: fsmout Parking Slot
      Namespaces:
        {}
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /vis/fsmout/parking_slots
      Value: false
    - Align Bottom: false
      Background Alpha: 0.800000011920929
      Background Color: 0; 0; 0
      Class: nviz_2d_overlay_plugins/TextOverlay
      Enabled: true
      Foreground Alpha: 1
      Foreground Color: 255; 255; 240
      Invert Shadow: false
      Name: debug_text_right
      Overtake BG Color Properties: false
      Overtake FG Color Properties: true
      Overtake Position Properties: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /debug_info_text_right_up
      Value: true
      font: Ubuntu Light
      height: 2000
      hor_alignment: right
      hor_dist: -195
      line width: 2
      text size: 8
      ver_alignment: top
      ver_dist: 0
      width: 600
    - Align Bottom: false
      Background Alpha: 0.800000011920929
      Background Color: 0; 0; 0
      Class: nviz_2d_overlay_plugins/TextOverlay
      Enabled: true
      Foreground Alpha: 1
      Foreground Color: 255; 255; 240
      Invert Shadow: false
      Name: trigger_event_text
      Overtake BG Color Properties: false
      Overtake FG Color Properties: true
      Overtake Position Properties: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /trigger_event_text
      Value: true
      font: Ubuntu Light
      height: 6000
      hor_alignment: right
      hor_dist: 300
      line width: 2
      text size: 10
      ver_alignment: top
      ver_dist: 38
      width: 400
    - Align Bottom: false
      Background Alpha: 0.800000011920929
      Background Color: 0; 0; 0
      Class: nviz_2d_overlay_plugins/TextOverlay
      Enabled: true
      Foreground Alpha: 1
      Foreground Color: 255; 255; 240
      Invert Shadow: false
      Name: debug_text_left
      Overtake BG Color Properties: false
      Overtake FG Color Properties: true
      Overtake Position Properties: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /debug_info_text_left
      Value: true
      font: Ubuntu Light
      height: 3000
      hor_alignment: left
      hor_dist: 0
      line width: 2
      text size: 8
      ver_alignment: top
      ver_dist: 0
      width: 600
    - Align Bottom: true
      Background Alpha: 0.800000011920929
      Background Color: 0; 0; 0
      Class: nviz_2d_overlay_plugins/TextOverlay
      Enabled: true
      Foreground Alpha: 1
      Foreground Color: 255; 255; 240
      Invert Shadow: false
      Name: debug_text_mid_up
      Overtake BG Color Properties: false
      Overtake FG Color Properties: true
      Overtake Position Properties: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /debug_info_text_mid_up
      Value: true
      font: Ubuntu Mono
      height: 35
      hor_alignment: center
      hor_dist: 185
      line width: 2
      text size: 22
      ver_alignment: top
      ver_dist: 0
      width: 1200
    - Class: nviz_default_plugins/MarkerArray
      Enabled: true
      Name: Speed Bump
      Namespaces:
        {}
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /viz/fusion/speed_bump
      Value: true
    - Class: nviz_default_plugins/MarkerArray
      Enabled: true
      Name: Drain Cover
      Namespaces:
        {}
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /viz/fusion/drain_cover
      Value: true
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: nviz_default_plugins/PointCloud
      Color: 255; 255; 255
      Color Transformer: FlatColor
      Decay Time: 0
      Enabled: false
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 4096
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: Freespace
      Position Transformer: XYZ
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.10000000149011612
      Style: Flat Squares
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /viz/fusion/freespace_points
      Use Fixed Frame: true
      Use rainbow: true
      Value: false
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 0
        Min Value: 0
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: nviz_default_plugins/PointCloud2
      Color: 255; 255; 255
      Color Transformer: RGB8
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 4096
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: FreeSpace2
      Position Transformer: XYZ
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.10000000149011612
      Style: Flat Squares
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /viz/fusion/freespace_points2
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
    - Class: nviz_default_plugins/Image
      Enabled: true
      Max Value: 1
      Median window: 5
      Min Value: 0
      Name: Park Image
      Normalize Range: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /viz/vision/park/image
      Value: true
    - Class: nviz_default_plugins/Image
      Enabled: true
      Max Value: 1
      Median window: 5
      Min Value: 0
      Name: Pld Image
      Normalize Range: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /viz/vision/pld/detect_image
      Value: true
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: nviz_default_plugins/PointCloud
      Color: 255; 0; 255
      Color Transformer: FlatColor
      Decay Time: 0
      Enabled: false
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 4096
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: Freespace_vru
      Position Transformer: XYZ
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.10000000149011612
      Style: Flat Squares
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /viz/fusion/freespace_points_vru
      Use Fixed Frame: true
      Use rainbow: true
      Value: false
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: nviz_default_plugins/PointCloud2
      Color: 255; 255; 255
      Color Transformer: ""
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 4096
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: Freespace2_vru
      Position Transformer: ""
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.10099999606609344
      Style: Flat Squares
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /viz/fusion/freespace_points2_vru
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
    - Class: nviz_default_plugins/TF
      Enabled: true
      Frame Timeout: 15
      Frames:
        All Enabled: true
      Marker Scale: 1
      Name: TF
      Show Arrows: true
      Show Axes: true
      Show Names: false
      Tree:
            {}
      Update Interval: 0
      Value: true
    - Class: nviz_default_plugins/MarkerArray
      Enabled: true
      Name: VehicleSpeed
      Namespaces:
        brake_pedal_objs: true
        vehicle_speed_objs: true
        xoy_objs: true
        xoy_text: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /viz/xyTimeSeqPlot
      Value: true
    - Class: nviz_default_plugins/MarkerArray
      Enabled: true
      Name: SteeringWheel
      Namespaces:
        steering_wheel_angle: true
        steering_wheel_angle_text: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /viz/steeringWheelAngle
      Value: true
    - Class: nviz_default_plugins/MarkerArray
      Enabled: true
      Name: pnc_traj_out
      Namespaces:
        {}
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /viz/pnc/trajectory_out
      Value: true
    - Class: nviz_default_plugins/MarkerArray
      Enabled: false
      Name: fusion_hmi_objects
      Namespaces:
        {}
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /viz/fusion/track/objects_hmi_marker
      Value: false
    - Class: nviz_default_plugins/MarkerArray
      Enabled: false
      Name: plt_traj_out
      Namespaces:
        traj_line_point: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /viz/plt/trajectory_out
      Value: false
    - Class: nviz_default_plugins/Image
      Enabled: true
      Max Value: 1
      Median window: 5
      Min Value: 0
      Name: camera_Image
      Normalize Range: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /camera1/image_raw
      Value: true
    - Class: nviz_default_plugins/MarkerArray
      Enabled: true
      Name: uss_perception
      Namespaces:
        "": true
        uss_objects: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /viz/debug/uss_info
      Value: true
    - Class: nviz_default_plugins/MarkerArray
      Enabled: true
      Name: pillars
      Namespaces:
        {}
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /viz/fusion/pillars
      Value: true
    - Class: nviz_default_plugins/MarkerArray
      Enabled: true
      Name: fusion_obj_marker
      Namespaces:
        {}
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /viz/fusion/track/objects_marker
      Value: true
    - Align Bottom: false
      Background Alpha: 0.800000011920929
      Background Color: 0; 0; 0
      Class: nviz_2d_overlay_plugins/TextOverlay
      Enabled: true
      Foreground Alpha: 0.800000011920929
      Foreground Color: 255; 255; 255
      Invert Shadow: false
      Name: pnc_fsm_text
      Overtake BG Color Properties: false
      Overtake FG Color Properties: true
      Overtake Position Properties: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /debug_fsm_out_text
      Value: true
      font: DejaVu Sans Mono
      height: 128
      hor_alignment: center
      hor_dist: 170
      line width: 2
      text size: 12
      ver_alignment: top
      ver_dist: 50
      width: 520
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: nviz_default_plugins/PointCloud2
      Color: 255; 255; 255
      Color Transformer: Intensity
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 4096
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: uss2
      Position Transformer: XYZ
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.05000000074505806
      Style: Flat Squares
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /viz/uss/lobes
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
    - Class: nviz_default_plugins/MarkerArray
      Enabled: true
      Name: intension
      Namespaces:
        intension: false
        predicted_trajectory: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /viz/fusion/pred/objects_marker
      Value: true
    - Class: nviz_default_plugins/Marker
      Enabled: true
      Name: effective_vision_area
      Namespaces:
        vision_area: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /viz/effective_vision_area_rect
      Value: true
  Enabled: true
  Global Options:
    Background Color: 48; 48; 48
    Fixed Frame: base_link
    Frame Rate: 30
  Name: root
  Tools:
    - Class: nviz_default_plugins/Interact
      Hide Inactive Objects: true
    - Class: nviz_default_plugins/MoveCamera
    - Class: nviz_default_plugins/Select
    - Class: nviz_default_plugins/FocusCamera
    - Class: nviz_default_plugins/Measure
      Line color: 128; 128; 0
    - Class: nviz_default_plugins/SetInitialPose
      Covariance x: 0.25
      Covariance y: 0.25
      Covariance yaw: 0.06853891909122467
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /initialpose
    - Class: nviz_default_plugins/SetGoal
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /goal_pose
    - Class: nviz_default_plugins/PublishPoint
      Single click: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /clicked_point
  Transformation:
    Current:
      Class: nviz_default_plugins/TF
  Value: true
  Views:
    Current:
      Angle: -1.5707900524139404
      Class: nviz_default_plugins/TopDownOrtho
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Scale: 28.033428192138672
      Target Frame: <Fixed Frame>
      Value: TopDownOrtho (nviz_default_plugins)
      X: 3
      Y: -1
    Saved: ~
Window Geometry:
  Displays:
    collapsed: true
  Height: 1016
  Hide Left Dock: true
  Hide Right Dock: false
  Park Image:
    collapsed: false
  Pld Image:
    collapsed: false
  QMainWindow State: 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
  Selection:
    collapsed: true
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: false
  Width: 1848
  X: 72
  Y: 27
  camera_Image:
    collapsed: false
