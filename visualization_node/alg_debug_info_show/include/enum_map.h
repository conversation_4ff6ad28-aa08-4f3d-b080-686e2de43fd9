#include <map>
#include <vector>

namespace NexProject
{
    enum LaneType{
        Invalid = 1,
        Straight= 1<< 1,
        Left    = 1<< 2,
        Right   = 1<< 3,
        LeftTurn= 1<< 4,
        RightTurn = 1<<5,
        Bus = 1<<6,
        None = 1<<7,
        Variable = 1<<8,
        Dedicated = 1<<9,
        Tidal = 1<<10
    };
    static std::map<int,std::vector<LaneType>> GAODEorignlaneconvert = {
        {255,{Invalid}},
        {0  ,{Straight}},
        {1  ,{Left}},
        {2  ,{Left,Straight}},
        {3  ,{Right}},
        {4  ,{Straight,Right}},
        {5  ,{LeftTurn}},
        {6  ,{LeftTurn,RightTurn}},
        {7  ,{Straight,LeftTurn,RightTurn}},
        {8  ,{RightTurn}},
        {9  ,{LeftTurn,Straight}},
        {10 ,{Straight,RightTurn}},
        {11 ,{Left,LeftTurn}},
        {12 ,{Right,RightTurn}},
        {13 ,{Invalid}},
        {14 ,{Invalid}},
        {15 ,{Invalid}},
        {16 ,{Left,LeftTurn,Straight}},
        {17 ,{Right,LeftTurn}},
        {18 ,{Left,Right,LeftTurn}},
        {19 ,{LeftTurn,Straight,Right}},
        {20 ,{Left,RightTurn}},
        {21 ,{Bus}},
        {22 ,{None}},
        {23 ,{Variable}},
        {24 ,{Dedicated}},
        {25 ,{Tidal}},
    };
    static std::map<LaneType,std::string> laneshow ={
        {static_cast<LaneType>(Straight),"↑"},
        {static_cast<LaneType>(Left),"↰"},
        {static_cast<LaneType>(Right),"↱"},
        {static_cast<LaneType>(LeftTurn),"↶"},
        {static_cast<LaneType>(RightTurn),"↷"},
        {static_cast<LaneType>(Bus),"🚌"},
        {static_cast<LaneType>(None),"☐"},
        {static_cast<LaneType>(Variable),"☒"},
        {static_cast<LaneType>(Dedicated),"⇑"},
        {static_cast<LaneType>(Tidal),"↑↓"},
    };


    static std::map<int, std::string> sensor_gnss_flag_pos_map = {
        {0, "0 None"},
        {1, "1 SINGLE"},
        {2, "2 PSRDIFF"},
        {3, "3 L1_FLOAT"},
        {4, "4 IONOFREE_FLOAT"},
        {5, "5 NARROW_FLOAT"},
        {6, "6 L1_INT"},
        {7, "7 WIDE_INT"},
        {8, "8 NARROW_INT"}};

    static std::map<int, std::string> vehicle_long_lat_ctrl_sts_map = {
        {0, "0 Init"},
        {1, "1 StdNoReq"},
        {2, "2 HdReq"},
        {3, "3 HdProgress"},
        {4, "4 HdFail"},
        {5, "5 HdSucceed"},
        {6, "6 HdCancelReq"},
        {7, "7 HdCancelProg"},
        {8, "8 HdCancel"},
        {9, "9 Fault"},
    };

    static std::map<int, std::string> vehicle_door_sts_map = {
        {0, "0 Locked"},
        {1, "1 Unlocked"}
    };

    static std::map<int, std::string> driver_command_spd_set_roller_dir_map = {
        {0, "0 Not Rotated"},
        {1, "1 Forward"},
        {2, "2 Backward"},
        {3, "3 Not Used"}
    };

    static std::map<int, std::string> driver_command_shift_req_map = {
        {0, "0 Shifter position O "},
        {1, "1 Shifter position F1"},
        {2, "2 Shifter position F2"},
        {3, "3 Shifter position B1"},
        {4, "4 Shifter position B2 "},
        {5, "5 Shifter not initialized"},
        {6, "6 Signal not available "}
    };

    static std::map<int, std::string> vehicle_drv_mode_map = {
        {0, "0 Invalid"},
        {1, "1 ECO+ "},
        {2, "2 ECO "},
        {3, "3 Comfort "},
        {4, "4 Driving "},
        {5, "5 SPORT "},
        {6, "6 I-pedal "},
        {7, "7 Individual"}
    };

    static std::map<int, std::string> vehicle_pwr_mode_map = {
        {0, "0 Off"},
        {1, "1 Accessory"},
        {2, "2 Run"},
        {3, "3 Crank Request"}
    };

    static std::map<int, std::string> veh_drv_mode_enum = {
        {0x0, "0 Inv"},
        {0x1, "1 ECO+"},
        {0x2, "2 ECO"},
        {0x3, "3 Com"},
        {0x4, "4 Drv"},
        {0x5, "5 SPO"},
        {0x6, "6 I-ped"},
        {0x7, "7 Ind"},
        {0x8, "8 Snow"},
        {0x9, "9 Mud"},
        {0xA, "A Sand"},
        {0xB, "B 2WD"},
        {0xC, "C 4WD"},
        {0xD, "D Trac"},
        {0xE, "E Auto"},
        {0xF, "F Nor"}
    };

    static std::map<int , std::string> mirror_sts_enum = {
        {0x0, "0 Un"},
        {0x1, "1 Fd"},
        {0x2, "2 Ufd"}
    };

    static std::map<int, std::string> drv_prk_dual_info_map = {
        {0, "0 Off"},
        {1, "1 DrvOnly"},
        {2, "2 PrkOnly"},
        {3, "3 Drv2Prk"},
        {4, "4 Prk2Drv"},
        {5, "5 Reserved"},
    };

    static std::map<int, std::string> fsm_node_sts_map = {
        {0, "0 Off"},
        {1, "1 Init"},
        {2, "2 Standby"},
        {3, "3 Running"},
        {4, "4 Abort"},
        {5, "5 Error"},
        {6, "6 Reserved"}
    };

    static std::map<int, std::string> fsm_driving_mode_map = {
        {0, "0 NoFunc"},
        {1, "1 APA"},
        {2, "2 RPA"},
        {3, "3 AVP"},
        {4, "4 ACC"},
        {5, "5 ICA"},
        {6, "6 NCA"},
        {7, "7 MCA"}
    };

    static std::map<int, std::string> fsm_user_req_lane_change_map = {
        {0, "0 NoReq"},
        {1, "1 Left"},
        {2, "2 Right"},
        {3, "3 Cancel"},
        {4, "4 Reserved1"},
        {5, "5 Reserved2"}
    };

    static std::map<int, std::string> fsm_acc_active_status_map = {
        {0, "0 Off"},
        {1, "1 Passive"},
        {2, "2 Standby"},
        {3, "3 Active"},
        {4, "4 StandstillActive"},
        {5, "5 StandstillWait"},
        {6, "6 Override"},
        {7, "7 BrakeOnly"},
        {8, "8 Failure"}};

    static std::map<int, std::string> fsm_ica_active_status_map = {
        {0, "0 Off"},
        {1, "1 Passive"},
        {2, "2 Standby"},
        {3, "3 Active"},
        {4, "4 Override"},
        {5, "5 Fault"},
        {6, "6 Reserved"}};

    static std::map<int, std::string> fsm_nca_active_status_map = {
        {0, "0 Off"},
        {1, "1 Passive"},
        {2, "2 Standby"},
        {3, "3 ActiveLng"},
        {4, "4 ActiveLng&Lat"},
        {5, "5 Failure"},
        {6, "6 MRC"}
    };

    static std::map<uint8_t, std::string> mca_sts_map = {
        { 0, "0 Off" },
        { 1, "1 Passive" },
        { 2, "2 Standby " },
        { 3, "3 Ready" },
        { 4, "4 Learning" },
        { 5, "5 LearningMapCheck" },
        { 6, "6 LearningCompleted " },
        { 7, "7 Localization" },
        { 8, "8 LocalizationSucceed" },
        { 9, "9 ActiveLng" },
        { 10, "10 ActiveLng&Lat" },
        { 11, "11 MRM" },
        { 12, "12 Failure" },
    };

    static std::map<int, std::string> fsm_p2p_sts_map = {
        {0, "0 Off"},
        {1, "1 Standby"},
        {2, "2 Ready"},
        {3, "3 ApaOut"},
        {4, "4 HpaOut"},
        {5, "5 HpaMncaOverlap"},
        {6, "6 Mnca"},
        {7, "7 MncaHpaOverlap"},
        {8, "8 HpaIn"},
        {9, "9 ApaIn"}
    };

    static std::map<int, std::string> fsm_long_fsm_status_map = {
        {0, "0 Off"},
        {1, "1 Passive"},
        {2, "2 Standby"},
        {3, "3 Active"},
        {4, "4 StandstillActive"},
        {5, "5 StandstillWait"},
        {6, "6 Override"},
        {7, "7 BrakeOnly"},
        {8, "8 Failure"}
    };

    static std::map<int, std::string> fsm_lat_fsm_status_map = {
        {0, "0 Off"},
        {1, "1 Passive"},
        {2, "2 Standby"},
        {3, "3 Active1"},
        {4, "4 Active2"},
        {5, "5 Override"},
        {6, "6 Failure"},
        {7, "7 RampOut"}
    };
    
    static std::map<int, std::string> fsm_acc_dist_status_map = {
        {0, "0"},  // Off
        {1, "1"},  // Shortest
        {2, "2"},  // Short
        {3, "3"},  // Mid
        {4, "4"},  // Long
        {5, "5"}};  // Longest

    static std::map<int, std::string> fsm_trun_light_req_map = {
        {0, "0 NoReq"},
        {1, "1 Off"},
        {2, "2 Left"},
        {3, "3 Right"},
        {4, "4 Hazard"},
        {5, "5 RCWHazard(4HZ)"}
    };

    static std::map<int, std::string> fsm_vcu_handshake_req_map = {
        {0, "0 NoReq"},
        {1, "1 APA"},
        {2, "2 RPA"},
        {3, "3 AVP"},
        {4, "4 ACC"},
    };

    static std::map<int, std::string> fsm_eps_handshake_req_map = {
        {0, "0 NoReq"},
        {1, "1 Low"},
        {2, "2 High"},
        {3, "3 Reserved"},
    };

    static std::map<int, std::string> fsm_hands_on_sts_map = {
        {0, "0 Invalid"},
        {1, "1 HandsOn"},
        {2, "2 HandsOff"}
    };

    static std::map<uint8_t, std::string> apa_sts_map = {
        {0, "0 Off"},
        {1, "1 Standby"},
        {2, "2 Searching"},
        {3, "3 Active"},
        {4, "4 Completed"},
        {5, "5 Failure"},
        {6, "6 Terminated"},
        {7, "7 Pause"},
    };

    static std::map<uint8_t, std::string> hpa_sts_map = {
        {0, "0 Off"},
        {1, "1 Standby"},
        {2, "2 Ready "},
        {3, "3 Learn"},
        {4, "4 LearnMapCheck"},
        {5, "5 LearnCompleted"},
        {6, "6 Localization"},
        {7, "7 Cruising"},
        {8, "8 Parking"},
        {9, "9 Pause"},
        {10, "10 Completed"},
        {11, "11 Terminated"},
        {12, "12 Failure"},
        {13, "13 LocalizationSucceed"},
        {14, "14 LearnMapCheckAndParkIn"},
    };

    static std::map<uint8_t, std::string> parking_disp_info_map = {
        {0, std::string("0")},
        {1, std::string("1 车位搜索中")},
        {2, std::string("2 找到车位, 请停车")},
        {3, std::string("3 车尾泊入, 请点击开始泊入")},
        {4, std::string("4 请松开刹车踏板和方向盘")},
        {6, std::string("6 泊车过程中")},
        {8, std::string("8 泊入已完成")},
        {9, std::string("9 泊出已完成")},
        {11, std::string("11 请选择泊出方向")},
        {12, std::string("12 请关上车门后继续泊车")},
        {13, std::string("13 请关上引擎盖后继续泊车")},
        {14, std::string("14 请关上后备箱后继续泊车")},
        {18, std::string("18 车头泊入, 请点击开始泊入")},
        {19, std::string("19 路径规划中")},
        {20, std::string("20 路径规划失败")},
        {21, std::string("21 请踩住刹车, 并手动调整车位")},
        {101, std::string("101 路线学习条件不满足")},
        {102, std::string("102 路线学习中")},
        {103, std::string("103 请选择目标车位")},
        {104, std::string("104 路线创建中")},
        {105, std::string("105 路线创建完成")},
        {106, std::string("106 定位中")},
        {107, std::string("107 巡航中")},
        {111, std::string("111 定位成功，可以激活记忆泊车功能啦")},
    };

    static std::map<uint8_t, std::string> apa_park_mode_map = {
        {0, "0 Invalid"},
        {1, "1 ParkIn"},
        {2, "2 ParkOut"},
        {3, "3 SelfSelect"},
    };

    static std::map<uint8_t, std::string> parking_abort_reason_map = {
        {0, "0 None"},
        {1, "1 ADC_Failure"},
        {2, "2 EPS_Failure"},
        {3, "3 IPB_Failure"},
        {4, "4 SCU_Failure"},
        {5, "5 VCU_Failure"},
        {6, "6 EPB_Failure"},
        {7, "7 PAD_Failure"},
        {8, "8 PAS_Failure"},
        {9, "9 BCM_Faliure"},
        {10, "10 SteeringOverride"},
        {11, "11 GearOverride"},
        {12, "12 AccPedalOverride"},
        {13, "13 TheVehicleSpeedIsTooHigh"},
        {14, "14 SlopeOverrun"},
        {15, "15 CurrentStepNumberOverThreshold"},
        {16, "16 PauseNumberOverThreshold"},
        {17, "17 ParkingOvertime"},
        {18, "18 RoutePlanningFailure"},
        {19, "19 VehicleBlocked"},
        {100, "100 AvpLocatedFailed"},
        {101, "101 DoorOpen"},
        {102, "102 Gear_R_InMapLearning"},
        {103, "103 HPAInterconnectedSysFail"},
        {104, "104 TargetSlotIsOccupiedPleaseParkManually"},
        {105, "105 LearningConditionIsNotMeet"},
    };

    static std::map<uint8_t, std::string> button_sts_map = {
        {0, "0 NotShow"},
        {1, "1 Available"},
        {2, "2 NotAvailable"},
        {3, "3 Highlight"},
    };

    static std::map<int, std::string> pnc_infer_status_map = {
        {0, "0 NONE"},
        {1, "1 ICA"},
        {2, "2 ROADNET"},
        {3, "3 RDNETAIR"},
        {4, "4 SD"},
        {5, "5 HD"},
        {6, "6 HA"}};

    static std::map<int, std::string> pnc_infer_ego_sts = {
        {0, "0 FAIL"},
        {1, "1 SUCCE"},
        {2, "2 JUNC"},
        {3, "3 JUNCABORT"}};
    
    static std::map<int, std::string> pnc_infer_junction_active = {
        {0, "0"},
        {1, "1"}};

    static std::map<int, std::string> pnc_infer_junction_source = {
        {0, "0 NONE"},
        {1, "1 STOP_LINE"},
        {2, "2 NO_LANE"}};

    static std::map<int, std::string> pnc_infer_stage = {
        {0, "0 KEEP"},
        {1, "1 PREPARE"},
        {2, "2 BUFFER"},
        {3, "3 SMALLJUNC"},
        {4, "4 SEARCH"},
        {5, "5 FOLLOW"},
        {6, "6 MERGE"},
        {7, "7 ABORT"}};

    static std::map<int, std::string> pnc_infer_direction = {
        {0, "0 UNKNOW"},
        {1, "1 STRAI"},
        {2, "2 LEFT"},
        {3, "3 RIGHT"},
        {4, "4 UTURN"},
        {5, "5 ROUND"},
        {6, "6 RESERVED1"}};

    static std::map<int, std::string> pnc_decider_status_map = {
        {0, "0 Off"},
        {1, "1 Init"},
        {2, "2 Standby"},
        {3, "3 Running"},
        {4, "4 Abort"},
        {5, "5 Error"},
        {6, "6 Reserved"}};
    
    static std::map<uint8_t, std::string> pnc_decider_acc_inhibit_reason_map = {
        {0, "0"},
        {1, "1"},
        {2, "2"},
        {3, "3"},
        {4, "4"},
        {5, "5"},
        {6, "6"},
        {7, "7"},
        {8, "8"},
        {9, "9"}};

    static std::map<uint8_t, std::string> pnc_decider_ica_inhibit_reason_map = {
        {0, "0"},
        {1, "1 Park"},
        {2, "2 Infer"},
        {3, "3 Refli"},
        {4, "4 CenFar"},
        {5, "5 HandTor"},
        {6, "6 Infer2"},
        {7, "7 JuncAb"},
        {8, "8 Check"},
        {9, "9 Loc"},
        {10, "10 Pred"},
        {11, "11 FuRoad"},
        {12, "12 Infer3"},
        {13, "13 Chassis"},
        {14, "14 FSM"},
        {30, "30"},
        {31, "31"}};

    static std::map<uint8_t, std::string> pnc_decider_macro_scene_enum = {
        {0, "NONE"},
        {1, "S_CURVE"},
        {2, "JUNCTION"},
        {3, "MERGE"},
        {4, "SPLIT"},
        {5, "RESERVED"}};

    static std::map<uint8_t, std::string> pnc_decider_function_mode_map = {
        {0, "0"},
        {1, "1"},
        {2, "2"},
        {3, "3"},
        {4, "4"},
        {5, "5"},
        {6, "6"},
        {7, "7"},
        {8, "8"},
        {9, "9"}};

    static std::map<int, std::string> pnc_decider_scene_map = {
        {0, "0 UNKNOWN"},
        {1, "1 FOLLOW"},
        {2, "2 CHANGE"},
        {3, "3 MERGE"},
        {4, "4 SPLIT"},
        {5, "5 TJP"},
        {6, "6 RESV1"},
        {7, "7 RESV2"},
        {8, "8 RESV3"}};

    static std::map<int, std::string> pnc_decider_scene_status_map = {
        {0, "0 UNKNOWN"},
        {1, "1 SWITCHABLE"},
        {2, "2 PROCESSING"},
        {3, "3 FINISHED"},
        {4, "4 RESERVED"}};

    static std::map<uint8_t, std::string> pnc_decider_detailed_maneuver_map = {
        {0, "FL"}, {1, "LC"}, {2, "RC"}, {3, "LN"}, {4, "RN"}, {5, "LB"}, {6, "RB"},
    };

    static std::map<int, std::string> pnc_decider_lane_follow_type_map = {
        {0, "0 UNKNOWN"}, {1, "1 KEEP"}, {2, "2 ACC"}, {3, "3 MERGE"}, {4, "4 SPLIT"}};

    static std::map<int, std::string> pnc_decider_shift_reason_map = {
        {0, "NONE"},
        {1, "CURB"},
        {2, "LARGE"},
        {3, "INVADE"},
        {4, "VRU"},
        {5, "5 RESERVED1"},
        {6, "6 RESERVED2"}};

    static std::map<int, std::string> pnc_decider_shift_direction_map  = {
        {0, "N"},
        {1, "L"},
        {2, "R"},
        {3, "3 BOTH"},
        {4, "4 RESERVED"}};

    static std::map<int, std::string> pnc_decider_passing_crosswalk_stage_map = {
        {0, "0 NOT_ACTIVE"},
        {1, "1 SLOWDOWN_TO_PREPARE"},
        {2, "2 STOP_BEFORE_CROSSWALK"},
        {3, "3 NORMALLY_PASSING_CROSSWALK"},
        {4, "4 SLOWLY_PASSING_CROSSWALK"},
        {5, "5 STOP_ON_CROSSWALK"},
        {6, "6 RESERVED"}};

    static std::map<int, std::string> pnc_decider_passing_crosswalk_stop_reason_map = {
        {0, "0 NOT_STOPPED"},
        {1, "1 PED_CROSSING"},
        {2, "2 PED_STOPPED_ON_CROSSWALK"},
        {3, "3 PED_STOPPED_ON_EDGE"},
        {4, "4 VRU_CROSSING"},
        {5, "5 VRU_STOPPED_ON_CROSSWALK"},
        {6, "6 VRU_STOPPED_ON_EDGE"},
        {7, "7 PED_WALKING_ALONG"},
        {8, "8 VRU_PASSING_ALONG"},
        {9, "9 RESERVED"}};

    static std::map<int, std::string> pnc_decider_lane_follow_status_map = {
        {0, "0 NOT_VALID"},
        {1, "1 VALID_STABLE"},
        {2, "2 VALID_UNSTABLE"}};

    // PNC DECIDER LANE FOLLOW MAP END

    // PNC DECIDER LANE CHANGE MAP START

    static std::map<int, std::string> pnc_decider_lane_change_source_map = {
        {0, "0 NONE"},
        {1, "1 DRIV"},
        {2, "2 NAVI"},
        {3, "3 OVER"},
        {4, "4 CONE"},
        {5, "5 EMERG"},
        {6, "6 ESCAPE"},
        {7, "7 FAST"},
        {8, "8 F-MERGE"},
    };

    static std::map<int, std::string> pnc_decider_lane_change_dir_map = {
        {0, "0 N"},
        {1, "1 L"},
        {2, "2 R"}};

    static std::map<int, std::string> pnc_decider_lane_change_stage_map = {
        {0, "0 NONE"},
        {1, "1 PREPARE"},
        {2, "2 ACCL"},
        {3, "3 DCCL"},
        {4, "4 EGO"},
        {5, "5 TARGET"},
        {6, "6 HOLD"},
        {7, "7 CANCEL"},
        {8, "8 FINISH"},
        {9, "9 SEARCH"}};

    static std::map<int, std::string> pnc_decider_lane_change_prepare_reason_map = {
        {0, "0 NONE"},
        {1, "1 ROUTE_SUPP"},
        {2, "2 CONE_SUPP"},
        {3, "3 JUNC_SUPP"},
        {4, "4 SUPP"},
        {5, "5 LC_CD"},
        {6, "6 NOT_CTR"},
        {7, "7 NO_GAP"},
        {8, "8 NO_LANE"},
        {9, "9 WIDTH"},
        {10, "10 OFFSET"},
        {11, "11 LENGTH"},
        {12, "12 FAR"},
        {13, "13 HARD_B"},
        {14, "14 SPEED"},
        {15, "15 TIT"},
        {16, "16 SOLID"},
        {17, "17 NO_CRIT"}};

    static std::map<int, std::string> pnc_decider_lane_change_cancel_reason_map = {
        {0, "0 NONE"},
        {1, "1 USER_CANCEL"},
        {2, "2 CONE_CANCEL"},
        {3, "3 INT_CANCEL"},
        {4, "4 NO_LANE"},
        {5, "5 LINE_SOLID"},
        {6, "6 LANE_NARROW"},
        {7, "7 LANE_FAR"},
        {8, "8 HARD_BOUND"},
        {9, "9 NO GAP"},
        {10, "10 FRONT RISK"},
        {11, "11 BACK RISK"},
        {12, "12 RESERVED1"}};

    static std::map<int, std::string> pnc_decider_lane_change_status_map = {
        {0, "0 NOT_VALID"},
        {1, "1 VALID_STABLE"},
        {2, "2 VALID_UNSTABLE"}};

    // PNC DECIDER LANE CHANGE MAP END

    // PNC DECIDER LANE MERGE MAP START
    static std::map<int, std::string> pnc_decider_lane_merge_type_map = {
        {0, "0 NO_MERGE"},
        {1, "1 PARALLEL_LEFT"},
        {2, "2 PARALLEL_RIGHT"},
        {3, "3 DIRECT_LEFT"},
        {4, "4 DIRECT_RIGHT"},
        {5, "5 RESERVED1"},
        {6, "6 RESERVED2"},
        {7, "7 RESERVED3"}};

    static std::map<int, std::string> pnc_decider_lane_merge_stage_map = {
        {0, "0 NO_MERGE"},
        {1, "1 PREPARE"},
        {2, "2 PREPARE_DCCL"},
        {3, "3 STOP_WAIT"},
        {4, "4 MERGING"},
        {5, "5 HOLD"},
        {6, "6 FINISH"},
        {7, "7 JUSTPASS"},
        {8, "8 RESERVED"}};

    static std::map<int, std::string> pnc_decider_lane_merge_status_map = {
        {0, "0 NOT_VALID"},
        {1, "1 VALID STABLE"},
        {2, "2 VALID UNSTABLE"},
    };

    // PNC DECIDER LANE MERGE MAP END

    // PNC DECIDER LANE SPLIT MAP START
    static std::map<int, std::string> pnc_decider_lane_split_type_map = {
        {0, "0 NO_SPLIT"},
        {1, "1 PARALLEL_LEFT"},
        {2, "2 PARALLEL_RIGHT"},
        {3, "3 DIRECT_LEFT"},
        {4, "4 DIRECT_RIGHT"},
        {5, "5 RESERVED1"},
        {6, "6 RESERVED2"},
        {7, "7 RESERVED3"}};

    static std::map<int, std::string> pnc_decider_lane_split_stage_map = {
        {0, "0 NO_SPLIT"},
        {1, "1 PREPARE"},
        {2, "2 IN_PROCESS"},
        {3, "3 FINISH"},
        {4, "4 JUSTPASS"},
        {5, "5 RESERVED"}};

    static std::map<int, std::string> pnc_decider_lane_split_status_map = {
        {0, "0 NOT_VALID"},
        {1, "1 VALID STABLE"},
        {2, "2 VALID UNSTABLE"},
    };
    // PNC DECIDER LANE SPLIT MAP END

    // PNC DECIDER TRAFFIC JUNCTION MAP START

    static std::map<int, std::string> pnc_decider_traffic_light_status_map = {
        {0, "0 NONE"},
        {1, "1 IN_USE"},
        {2, "2 FAULT"},
        {3, "3 RESERVED"},
    };

    static std::map<int, std::string> pnc_decider_traffic_light_color_map = {
        {0, "0 NONE"},
        {1, "1 RED"},
        {2, "2 YELLOW"},
        {3, "3 GREEN"},
        {4, "4 BLACK"},
        {5, "5 WHITE"},
        {6, "6 RESERVED"},
    };

    static std::map<int, std::string> pnc_decider_traffic_light_dir_map = {
        {0, "0 NONE"},
        {1, "1 STRAI"},
        {2, "2 LEFT"},
        {3, "3 RIGHT"},
        {4, "4 UTURN"},
    };

    static std::map<int, std::string> pnc_decider_traffic_light_countdown_status_map = {
        {0, "0 NO_COUNTDOWN"},
        {1, "1 COUNTDOWN"},
    };

    static std::map<int, std::string> pnc_decider_traffic_junction_direction_map = {
        {0, "0 NONE"},
        {1, "1 STRAI"},
        {2, "2 LEFT"},
        {3, "3 RIGHT"},
        {4, "4 UTURN"},
        {5, "5 ROUND"},
        {6, "6 RESERVED"},
    };

    static std::map<uint8_t, std::string> pnc_decider_junction_pass_stage_map = {
        {0, "0 NONE"},
        {1, "1 PREPARE"},
        {2, "2 SWITCH"},
        {3, "3 PASS"},
        {4, "4 MERGE"},
        {5, "5 FOLLOW"},
        {6, "6 SMALL"},
        {7, "7 FINISH"},
        {8, "8 ABORT"},
        {9, "9 INPASS"},
        // {0, "0 NO_JUNCTION"},
        // {1, "1 LANE_DECIDE"},
        // {2, "2 TRAFFIC_LIGHT_DECIDE"},
        // {3, "3 WAIT_AT_STOP_LINE"},
        // {4, "4 WAIT_AT_BUFFER_AREA"},
        // {5, "5 JUNCTION_PASSING"},
        // {6, "6 PRESELECT_TARGET_LANE"},
        // {7, "7 MERGING_TO_TARGET_LANE"},
        // {8, "8 JUNCTION_PASS_FINISH"},
        // {9, "9 RESERVED"},
    };

    static std::map<uint8_t, std::string> pnc_decider_refline_opt_status_map = {
        {0, "0 optN"}, // WithoutOptimizer
        {1, "1 hist"}, // UseHistoryResult
        {2, "2 preF"}, // PreProcessFailed
        {3, "3 setF"}, // SetSolverInput
        {4, "4 slv2"}, // SolveTwice
        {5, "5 bnd"}, // SolveWithBoundExpand
        {6, "6 qpF"}, // QPAbnormal
        {7, "7 chkF"}, // PostCheckFailed
        {8, "8 sqpF"}, // SQPAbnormal
        {9, "9 optS"}, // OptimizeSuccess
        {10, "10 samp"}, // Reserve
    };

    // PNC DECIDER TRAFFIC JUNCTION MAP END

    // PNC DECIDER SPEED LIMIT MAP START

    static std::map<int, std::string> pnc_decider_speed_limit_validity_map = {
        {0, "0 NotValid"},
        {1, "1 UpperLimitOnly"},
        {2, "2 LowerLimitOnly"},
        {3, "3 BothLimits"},
        {4, "4 Unstable"},
        {5, "5 Reserved"}};

    static std::map<int, std::string> pnc_decider_speed_limit_source_map = {
        {0, "0 NO_SPEED_LIMIT"},
        {1, "1 USER_SET_LIMIT"},
        {2, "2 HDMAP_LIMIT"},
        {3, "3 SDMAP_LIMIT"},
        {4, "4 TSR_LIMIT"},
        {5, "5 FUSED_CURVATURE_LIMIT"},
        {6, "6 FUSED_PRIORI_LIMIT"},
        {7, "7 RESERVED"}};

    static std::map<int, std::string> pnc_decider_speed_adjust_recommend_map = {
        {0, "0 No Adjust"},
        {1, "1 Accel Soft"},
        {2, "2 Decel Soft"},
        {3, "3 Accel Hard"},
        {4, "4 Decel Hard"},
        {5, "5 Reserved"}};

    static std::map<int, std::string> pnc_decider_speed_adjust_reason_map = {
        {0, "0 No Adjust"},
        {1, "1 Change Resv1"},
        {2, "2 Change Resv2"},
        {3, "3 Change Resv3"},
        {4, "4 Merge Resv1"},
        {5, "5 Merge Resv2"},
        {6, "6 Merge Resv3"},
        {7, "7 Split Resv1"},
        {8, "8 Split Resv2"},
        {9, "9 Split Resv3"},
    };

    // PNC DECIDER SPEED LIMIT MAP END

    // PNC DECIDER REFLINE MAP END

    static std::map<int, std::string> pnc_decider_refline_validity_map = {
        {0, "0 Invalid"},
        {1, "1 Valid New"},
        {2, "2 Valid Stable"},
        {3, "3 Reserved"}};

    static std::map<int, std::string> pnc_decider_refline_position_map = {
        {0, "0 Ego"},
        {1, "1 Left"},
        {2, "2 Right"},
        {3, "3 Adjusted Ego"},
        {4, "4 Left Merge"},
        {5, "5 Right Merge"},
        {6, "6 Left Split"},
        {7, "7 Right Split"},
        {8, "8 Reserved"}};

    static std::map<uint8_t, std::string> pnc_decider_primary_maneuver_map = {
        {0, "0 LF"},
        {1, "1 LC"},
        {2, "2 LH"},
    };

    static std::map<int, std::string> pnc_decider_ref_line_type_map = {
        {0, "0 Unknown"},
        {1, "1 Regular Type"},
        {2, "2 Target Refline"},
    };

    static std::map<int, std::string> pnc_decider_boundary_type_map = {
        {0, "0 UNKNOWN"},
        {1, "1 SOLID_LINE_NO_CURB"},
        {2, "2 DASH_LINE_NO_CURB"},
        {3, "3 SOLID_LINE_WITH_CURB"},
        {4, "4 DASH_LINE_WITH_CURB"},
        {5, "5 NO_LINE_NO_CURB"},
        {6, "6 NO_LINE_WITH_CURB"},
        {7, "7 DOUBLE_SOLID_LINE"},
        {8, "8 RESERVED"}
    };

    static std::map<int, std::string> pnc_plan_status_map = {
        {0, "0 Standby"},
        {1, "1 Active"},
        {2, "2 Interrupt"},
        {3, "3 Failed"},
        {4, "4 Reserved"}
    };

    static std::map<int, std::string> pnc_plan_fail_reason_map = {
        {0, "0 No_Err"},
        {1, "1 No_FSM"},
        {2, "2 No_Loc"},
        {3, "3 No_Decider"},
        {4, "4 No_Obj"},
        {5, "5 No_Veh10ms"},
        {6, "6 Lat_Err"},
        {7, "7 Lon_Err"},
        {8, "8 FSM_Status_Park"}
    };

    static std::map<int, std::string> pnc_plan_lat_state_map = {
        {0, "0 Nomal"},
        {1, "1 Replan"},
        {2, "2 Ref_Line_GeneRate_Failed"},
        {3, "3 Path_Boundary_GeneRate_Failed"},
        {4, "4 Path_Opt_Failed"},
        {5, "5 Decider_Avoid"},
        {6, "6 Static_Obs_Avoid"},
        {7, "7 Use_History_Path"},
        {8, "8 Fallback_Path"},
        {9, "9 Downgrade_ACC"},
        {30,"30 ApaNomal "},
        {31,"31 ApaReplan"},
        {32,"32 ApaPlanfailed"},
        {33,"33 ApaOptfailed"},
        {34,"34 ApaFinished"},
        {35,"35 ApaWaitFinish"},
        {50,"50 HpaNomal"},
        {51,"51 HpaReplan "},
        {52,"52 HpaRefLineFailed"},
        {53,"53 HpaPlanFailed"},
        {54,"54 HpaOptFailed"},
        {55,"55 UseHistoryPath"}
    };

    static std::map<int, std::string> pnc_plan_lon_state_map = {
        {0, "0 Nomal"},
        {1, "1 RePlan"},
        {2, "2 Stop"},
        {3, "3 Start"},
        {4, "4 Cruise"},
        {5, "5 LoseCipv"},
        {30,"30 ApaNomal "},
        {31,"31 ApaReplan"},
        {32,"32 ApaPlanfailed"},
        {33,"33 ApaOptfailed"},
        {34,"34 ApaFinished"},
        {35,"35 ApaWaitFinish"},
        {50,"50 HpaNomal"},
        {51,"51 HpaRePlan"},
        {52,"52 HpaPlanFailed"},
        {53,"53 HpaOptFailed"}
    };
    static std::map<int, std::string> plan_function_mode = {
        {0,"Unknown"},
        {1,"HPA"},
        {2,"APA"},
        {3,"RPA"},
        {4,"ACC"},
        {5,"ICA"},
        {6,"NCA"},
        {7,"MCA"}
    };
    static std::map<int, std::string> pnc_router_status_map = {
        {0, "0 ROUTER_STATUS_REFLINE_SUCCESS"},
        {1, "1 ROUTER_STATUS_REFLINE_UNSUCCESS"},
        {2, "2 ROUTER_STATUS_PATH_UNSUCCESS"},
        {3, "3 ROUTER_STATUS_NO_NAVIINFO"},
        {4, "4 ROUTER_STATUS_NO_HDMAP"}
    };

    static std::map<int, std::string> pnc_router_change_type_map = {
        {0, "0 FORWARD"},
        {1, "1 LEFT CHANGE"},
        {2, "2 RIGHT CHANGE"},
        {3, "3 LEFT MERGE"},
        {4, "4 RIGHT MERGE"},
        {5, "5 LEFT SPLIT"},
        {6, "6 RIGHT SPLIT"},
        {8, "8 FRONT MERGE LEFT CHANGE"},
        {9, "9 FRONT MERGE RIGHT CHANGE"}
    };

    static std::map<int, std::string> pnc_control_status_map = {
        {0, "0 Off"},
        {1, "1 Init"},
        {2, "2 Standby"},
        {3, "3 Running"},
        {4, "4 Failed"},
        {5, "5 Reserved"}
    };

    // static std::map<int, std::string> pnc_decider_lane_change_status_map = {

    // };
    static std::map<int, std::string> map_build_mode_enum = {
        {0,"0 Unknown"},
        {1,"1 Memory_Avp"},
        {2,"2 Memory_Commute"}
    };

    static std::map<int, std::string> map_stage_enum = {
        {0,"0 Unknown"},
        {1,"1 在线记忆阶段"},
        {2,"2 定位地图离线构建"},
        {3,"3 定位地图离线构建失败"},
        {4,"4 矢量地图（参考线）离线构建"},
        {5,"5 矢量地图（参考线）离线构建失败"},
        {6,"6 地图校验中"},
        {7,"7 地图校验失败"},
        {8,"8 记忆路线生成成功"},
        {11,"11 巡航阶段"}
    };
    static std::map<int, std::string> pnc_trajectory_primary_decision_map = {
        {0, "0 Unknown"},
        {1, "1 Follow"},
        {2, "2 Grab"},
        {3, "3 Yield"},
        {4, "4 PassOnLeft"},
        {5, "5 PassOnRight"},
        {6, "6 Passby"},
        {7, "7 PreSlowDown"},
        {8, "8 EmergencyStop"},
        {9, "9 Reserved"}};
    static std::map<int, std::string> avp_located_sts_enum ={
        {0,"0 Unknown"},
        {1,"1 LocSucInMap"},
        {2,"2 LocSucOutMap"},
        {3,"3 LocFaiMapMatchedFail"},
        {4,"4 LocSucLess10m"},
    };
    static std::map<int, std::string> avp_floor_type_enum ={
        {0,"0 Up"},
        {1,"1 Down"},
        {2,"2 Flat"},
    };
    static std::map<int, std::string> avp_relocated_sts_enum ={
        {0,"0 Unknown"},
        {1,"1 ReLocSucInMap"},
        {2,"2 ReLocFail"},
        {3,"3 ReLocSucOutMap"},
        {4,"4 LoadingMap"},
        {5,"5 LoadMapSuc"},
        {6,"56ReLocFailRTKNotOk"},
    };
    static std::map<int, std::string> avp_located_precondition_sts_enum = {
        {0,"0 Unknown"},
        {1,"1 PrecdOk"},
        {2,"2 PrecdNotOkCalFail"}
    };
    static std::map<int, std::string> fail_state_fusion_pose_enum = {
        {0, "0 Normal"},
        {1, "1 Init"},
        {2, "2 Exit"},
        {3, "3 NoMap"},
        {4, "4 NoLocalMap"},
        {5, "5 LocError"},
        {6, "6 RoToPr"},
        {7, "7 Predict"},
        {8, "8 DrError"},
        {9, "9 LowConf"},
    };
    static std::map<int,std::string> slot_matched_sts_enum = {
        {0,"0 Unknown"},
        {1,"1 MatchSucc"},
        {2,"2 MatchSclected"},
        {3,"3 MatchSclectedNoSel"},
        {4,"4 MatchFailed"}
    };
    static std::map<int,std::string>turn_light_status = {
        {0,"0 nonsteering"},
        {1,"1 Left turn to light"},
        {2,"2 Turn right to light"},
        {3,"3 Detour"}
    };
    static std::map<int, std::string> pnc_fsm_abort_reason_bit_map = {
        {0, "Power Off"},
        {1, "Chassis Msg Timeout"},
        {2, "Control Msg Timeout"},
        {3, "Planner Msg Timeout"},
        {4, "Decider Msg Timeout"},
        {5, "Control Status Abnormal"},
        {6, "Planner Status Abnormal"},
        {7, "Decider Status Abnormal"},
        {8, "Active Safety Function (AEB/MEB) Activated"},
        {9, "Parking Function (APA/HPA) Activated"},
        {10, "Long Controller Fault (Fault/Overtime)"},
        {11, "Long Controller Handshake Failed (Init/Failed)"},
        {12, "Brake Pedal Pressed"},
        {13, "Driver Req Exit"},
        {14, "Any Door Opened"},
        {15, "Seatbelt Not Fastened"},
        {16, "EPB Engaged"},
        {17, "Not in D-Gear"},
        {18, "Overspeed at 135"},
        {19, "Standstill Wait Timeout"},
        {20, "Accelerator Pedal Pressed Timeout"},
        {21, "Takeover Reminder Timeout"},
        {22, "ABS Activated"},
        {23, "TCS Activated"},
        {24, "HDC Activated"},
        {25, "ESP/DWT Activated"},
        {26, "CDD Activated"},
        {27, "Reserved"},
        {28, "Reserved"},
        {29, "Reserved"},
        {30, "Reserved"},
        {31, "Decider Internal Abnormality"},
        {32, "Planner Internal Abnormality"},
        {33, "Control Internal Abnormality"},
        {34, "Reserved"},
        {35, "Lat Controller Fault (Fault/Overtime)"},
        {36, "Lat Controller Handshake Failed (Init/Failed)"},
        {37, "User Switches ACC (Chery)"},
        {38, "Hands-off Alarm Level Not Zero"},
        {39, "Reserved"},
        {40, "Reserved"},
        {41, "Reserved"},
        {42, "Reserved"},
        {43, "Reserved"},
        {44, "Reserved"},
        {45, "Decider Proh ICA"},
        {46, "Driver Hand Torque Too High"},
        {47, "Yaw Rate Too High"},
        {48, "Lateral Optimization Failure"},
        {49, "Planner Req Acc Mode"},
        {50, "Reserved"},
        {51, "NCA-Infer Msg Timeout"},
        {52, "NCA-Loc Pose Msg Timeout"},
        {53, "NCA-Infer Status Abnormal"},
        {54, "NCA-Localization Failure"},
        {55, "NCA-Navigation Turned Off"},
        {56, "NCA-Route Reference Line Generation Failure"},
        {57, "NCA-Currently in UnODD"},
        {58, "NCA-Too Close to UnODD Ahead, NCA Passive"},
        {59, "NCA-Too Close to UnODD Ahead, NCA Exit"},
        {60, "Reserved"},
        {61, "Reserved"},
        {62, "Reserved"},
        {63, "Reserved"}
    };

    static std::map<int, std::string> pnc_fsm_takeover_remind_reason_map = {
        {0, "0 NotTor"},
        {6, "6 Decider-TakeoverToLeftChange"},
        {7, "7 Decider-TakeoverToRightChange"},
        {8, "8 Decider-TakeoverToDec"},
        {9, "9 Decider-TakeoverToCaution"},
        {10, "10 Infer-ComplicatedNoTakeover"},
        {11, "11 Infer-ComplicatedTaskOver"},
        {12, "12 Infer-NextUnoddTollgate"},
        {13, "13 Infer-NextUnoddRoundCircle"},
        {14, "14 Infer-NextUnoddTOR"},
        {15, "15 Infer-InUnoddTOR"},
        {16, "16 Planner-CIPVMissingEarlyStage"},
        {17, "17 Planner-CutIn"},
        {18, "18 Planner-CanNotIndetifyScene"},
    };

    static std::map<int,std::string> router_status_enum = {
        { 0 , "0 RefTrue"},
        { 1 , "1 RefFail"},
        { 2 , "2 PathFail"},
        { 3 , "3 NoNaviInfo"},
        { 4 , "4 NoHd"},
        { 5 , "5 HaInvalid"},
        { 6 , "6 ERROR"},
    };

    static std::map<int,std::string> router_failed_reason_enum = {
        {0 , "0 True" },
        {1 , "1 Loc"  },
        {2 , "2 Ehr"  },
        {3 , "3 SearPath"  },
        {4 , "4 Refl"   },
        {5 , "5 Park"    }
        };

    static std::map<int,std::string> pnc_map_status_enum = {
        {0,"0 MapTrue"},
        {1,"1 MapOverTime"},
        {2,"2 MapFail"}
        };

      static std::map<int,std::string> pnc_map_failed_reason_enum = {
        {0,"0 FsmFail"},
        {1,"1 FusRoadFail"},
        {2,"2 InferFail"}
      };
      static std::map<int,std::string> speed_limit_source_enum = {
        {0,"0 NoSpdLim"},
        {1,"1 UerSetLim"},
        {2,"2 HdmapLim"},
        {3,"3 SdmapLim"},
        {4,"4 TsrLim"},
        {5,"5 FusedCurLim"},
        {6,"6 FusedPriorLim"},
        {7,"7 Re"}
      };
      static std::map<int,std::string> front_lane_change_enum_list = {
        {0,"0 F"},
        {1,"1 L"},
        {2,"2 R"},
        {3,"3 LM"},
        {4,"4 RM"},
        {5,"5 LS"},
        {6,"6 RS"},
        {7,"7 Cancle"},
        {8,"8 FM"},
        {9,"9 FM"}
      };

    static std::map<uint64_t,std::string> lane_type_enum_list = {
       {0 ,"Unknow"},
       {1 ,"Normal"},
       {2 ,"Accele"},
       {3 ,"Decele"},
       {4 ,"Lturn"},
       {5 ,"Rturn"},
       {6 ,"Uturn"},
       {7 ,"Lturnwait"},
       {8 ,"Swait"},
       {9 ,"Rturnwait"},
       {10,"ReveLturn"},
       {11,"virconnect"},
       {12,"uturnwait"},
       {13,"Emergency"},
       {14,"Shoulder"},
       {15,"Sidway"},
       {16,"Forbid"},
       {17,"ManualToll"},
       {18,"ETC"},
       {19,"Bus"},
       {20,"BusWithTime"},
       {21,"BusBay"},
       {22,"StreetRailWay"},
       {23,"Taxi"},
       {24,"Truck"},
       {25,"DangerOnly"},
       {26,"SpecialOnly"},
       {27,"Motor"},
       {28,"Bike"},
       {29,"ReverBike"},
       {30,"Tide"},
       {31,"DirecChange"},
       {32,"RAMP"},
       {33,"RoundCircle"}
      };
    static std::map<int,std::string> plan_status = {
        {0,"STANDBY"},
        {1,"ACTIVE"},
        {2,"INTERRUPT"},
        {3,"FAILED"},
        {4,"RESERVED"}
    };
    static std::map<uint64_t,std::string> plan_failed_reason = {
        {0  ,"NoErr"},
        {1  ,"InputNoFSM"},
        {2  ,"InputNoLoc"},
        {3  ,"InputNoDecider"},
        {4  ,"InputNoObj"},
        {5  ,"InputNoVeh10ms"},
        {6  ,"Lat_Err"},
        {7  ,"Lon_Err"},
        {30 ,"ApaNoErr"},
        {31 ,"ApaNoFSM"},
        {32 ,"ApaNoLoc"},
        {33 ,"ApaNoVeh10ms"},
        {34 ,"ApaNoStaticElement"},
        {35 ,"ApaNoLocalMap"},
        {36 ,"ApaNoSlotList"},
        {37 ,"ApaNoContrOut"},
        {38 ,"ApaLatErr"},
        {39 ,"ApaLonErr"}
    };

    static std::map<uint64_t,std::string> plan_scene = {
        {0 ,"Unknown"},
        {30,"DeadEnd"},
        {31,"Mechanical"},
        {32,"SelfSelect"},
        {33,"Narrow"},
        {34,"PerpendicularLine"},
        {35,"PerpendicularSingleborder"},
        {36,"PerpendicularDoubleborder"},
        {37,"ParallelLine"},
        {38,"ParallelSingleborder"},
        {39,"ParallelDoubleborder"}
    };
    static std::map<int,std::string> collision_reason_enum = {
        {0,"None"},
        {1,"StaticObstacle"},
        {2,"DynamicObsta"}
    };

    static std::map<int,std::string> apa_park_in_req_enum = {
        {0, "NoReq"},
        {1, "Req"},
        {2, "ReqLeft"},
        {3, "ReqRight"}
    };

    static std::map<int,std::string> apa_park_out_direction_req_enum = {
        {0,  "None"},
        {1,  "FronLCross"},
        {2,  "FronLParallel"},
        {3,  "FronOut"},
        {4,  "FronRCross"},
        {5,  "FrontRParallel"},
        {6,  "BackOut"},
        {7,  "BackLCross"},
        {8,  "BackRCross"}
    };

    // static std::map<int, std::string> pnc_fsm_abort_reason_bit_map = {
    //     {0, "控制节点msg超时"},
    //     {1, "规划节点msg超时"},
    //     {2, "决策节点msg超时"},
    //     {3, "路由节点msg超时"},
    //     {4, "控制节点状态not_success"},
    //     {5, "规划节点状态not_success"},
    //     {6, "决策节点状态not_succe"},
    //     {10, "纵向控制器失效"},
    //     {11, "纵向控制器握手失败"},
    //     {12, "踩下刹车"},
    //     {13, "epb拉起"},
    //     {14, "四门两盖打开"},
    //     {15, "安全带未系"},
    //     {16, "非D档"},
    //     {17, "超速135"},
    //     {18, "加速踏板被踩下(brake only状态下"},
    //     {19, "车辆静止(brake only状态下"},
    //     {20, "拨杆退出(brake only状态下"},
    //     {21, "brake only超时(brake only状态下)"},
    //     {22, "主动拨杆退出"},
    //     {23, "standstill_wait超时"},
    //     {24, "用户接管油门踏板"},
    //     {30, "横向退出原因"},
    //     {30, "横向控制器失效"},
    //     {31, "横向控制器握手失败"},
    //     {32, "ABS激活"},
    //     {33, "TCS激活"},
    //     {34, "HDC激活"},
    //     {35, "脱手报警等级二级"},
    //     {36, "脱手报警等级非0级"},
    //     {37, "纵向未激活"},
    //     {40, "nca-导航关闭"},
    //     {41, "nca-loc发出Lane级定位失败标志位"},
    //     {42, "nca-router参考线生成失败"},
    //     {43, "nca-横向状态机等于Standby"},
    //     {44, "nca-纵向状态机等于Standby"},
    //     {45, "nca-横向状态机等于Override"}};

    static std::map<int, std::string> map_navi_path_trafficlight_type_map = {
        {0, "Unknown"},
        {1, "Red"},
        {10, "Green"},
        {20, "NearRed"}
    };

    static std::map<int,std::string> safety_sts_enum = {
        {0x0, "Off"},
        {0x1, "Passive"},
        {0x2, "Standby"},
        {0x3, "Active"},
        {0x4, "Failure"}
    };

    static std::map<uint8_t, std::string> pnc_decider_multimodal = {
        {0, "kLaneFollow"},
        {1, "kLaneChange"},
        {2, "kLaneHold"}
    };

    static std::map<uint8_t, std::string> pnc_decider_idm_stag = {
        {0, "kIgnore"},
        {1, "kCaution"},
        {2, "kCIPV"},
        {3, "kHighRiskBack"},
        {4, "kSide_Follow"},
        {5, "kSide_Overtake"},
        {6, "kYielding"},
        {7, "kEncroaching"},
        {8, "kHighRiskOnComing"},
    };

    static std::map<uint8_t, std::string> pnc_decider_idm_ltag = {
        {0, "kIgnore"},
        {1, "kCaution"}
    };

    static std::map<uint16_t, std::string> pnc_decider_lane_select_reason_map = {
        {0, "0"},
        {1, "Infer1"},
        {2, "Infer2"},
        {3, "Infer3"},
        {4, "Infer4"},
        {5, "Infer5"},
        {6, "Infer6"},
        {7, "Infer7"},
        {8, "Sel8"},
        {9, "Sel9"},
        {10, "Sel10"},
        {11, "Sel11"},
        {12, "Sel12"},
    };
    
}