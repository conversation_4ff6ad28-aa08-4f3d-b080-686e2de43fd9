#include "adapter.h"
#include "key_catch.h"
#include "linux_net_monitor.h"
#include <alg_nviz_utils/custom_env.hpp>
#include <bits/stdint-intn.h>
#include <bits/stdint-uintn.h>
#include <camera_msgs/msg/detail/camera_yuv1_m__struct.hpp>
#include <chrono>
#include <cmath>
#include <cstddef>
#include <iomanip>
#include <limits>
#include <loc_msgs/msg/detail/loc_pose__struct.hpp>
#include <memory>
#include <nlibcpp/node.hpp>
#include <nlibcpp/qos.hpp>
#include <sstream>
#include <string>
#include <cstdlib>
#include <strstream>
#include <unordered_map>
#include <uss_msgs/msg/detail/uss_original_info__struct.hpp>
#include <vector>
#include <algorithm>
#include <camera_msgs/msg/camera_yuv1_m.hpp>

namespace NexProject
{
    AdapterDebugInfoShowNode::AdapterDebugInfoShowNode()
        : Node("alg_debug_info_show", NEX_NODE_VERSION)
    {
    }

    bool AdapterDebugInfoShowNode::Init()
    {
        _is_node_initialized = false;
        initialize_nex_sub_pub();

        map_show = std::make_shared<Map::MapWriter>();
        // ALG_DEBUG_INFO_SHOW::KeyCatch::GetInstance().SetStrMode(config_str_mode);
        // ALG_DEBUG_INFO_SHOW::KeyCatch::GetInstance().Start();

        return true;
    }

    void AdapterDebugInfoShowNode::Start()
    {
        std::cout << "startProc " << std::endl;
    }

    void AdapterDebugInfoShowNode::publish_debug_info()
    {
        // ============================================================================================================= //
        // Initialization DebugInfoText
        // ============================================================================================================= //

        std::stringstream alg_debug_msg_right_up_str_, alg_debug_msg_right_down_str_, alg_debug_msg_left_str_, debug_msg_center_up_str_, alg_debug_traffic_light_ss,
        alg_debug_traffic_light_e2e,alg_debug_info_error_str_,alg_debug_prk_sts_str_, alg_debug_fsm_out_str_, alg_debug_msg_left_second_str_;

        alg_debug_msg_left_second_str_.str("");
        alg_debug_msg_right_up_str_.str("");
        alg_debug_msg_right_down_str_.str("");
        alg_debug_msg_left_str_.str("");
        debug_msg_center_up_str_.str("");
        alg_debug_info_error_str_.str("");
        alg_debug_prk_sts_str_.str("");
        alg_debug_fsm_out_str_.str("");

        alg_debug_msg_left_second_str_ << std::fixed << std::setprecision(2);
        alg_debug_msg_right_up_str_ << std::fixed << std::setprecision(2);
        alg_debug_msg_right_down_str_ << std::fixed << std::setprecision(2);
        alg_debug_msg_left_str_ << std::fixed << std::setprecision(2);
        debug_msg_center_up_str_ << std::fixed << std::setprecision(2);
        alg_debug_info_error_str_ << std::fixed << std::setprecision(2);
        alg_debug_prk_sts_str_ << std::fixed << std::setprecision(2);
        alg_debug_fsm_out_str_ << std::fixed << std::setprecision(2);

        auto is_p2p = alg_nviz_utils::CustomEnv::IsP2p() || alg_nviz_utils::CustomEnv::IsP2p7v();
        if(is_p2p){
                // all show
                AlgIpcTimeShow(alg_debug_msg_left_str_);
                AlgNetMonitorAdapter(alg_debug_msg_left_str_);
                AlgCalibInfo(alg_debug_msg_left_str_);
                if(0x2 != getFsmMode() ){ //driving
                  AlgTlrPostpro(alg_debug_msg_left_str_);
                }
                AlgVehicleStatusAdapter(alg_debug_msg_left_str_);
                AlgSafetyCtrlAdapter(alg_debug_msg_left_str_);
                AlgGnssAdapter(alg_debug_msg_left_str_);
                // AlgInsAdapter(alg_debug_msg_left_str_);
                AlgImuAdapter(alg_debug_msg_left_str_);
                AlgLocSuperOdomAdapter(alg_debug_msg_left_str_);
                AlgLocMapMatchAdapter(alg_debug_msg_left_str_);
                AlgMapEngineServer(alg_debug_msg_left_str_);
                AlgSDDebugAdapter(alg_debug_msg_left_str_);
                // AlgRawMapDataAdapter(alg_debug_msg_left_str_);
                AlgDrAdapter(alg_debug_msg_left_str_);
                AlgVisionFusionAdapter(alg_debug_msg_left_str_);
                AlgFusionOdAdapter(alg_debug_msg_left_str_);
                AlgFrontRadarFreqAdapter(alg_debug_msg_left_str_);
                AlgFusionOdPredAdapter(alg_debug_msg_left_str_);
                if(0x2 != getFsmMode() ){ //driving
                    AlgFusionMonomapAdapter(alg_debug_msg_left_str_);
                    AlgFusionRoadAdapter(alg_debug_msg_left_str_);
                }
                AlgUniinferAdapter(alg_debug_msg_left_str_);
                if(0x2 != getFsmMode() ){ //driving
                    AlgSdNaviPathAdapter(alg_debug_msg_left_str_);
                }else{
                    AddFixedLocalMapLegend(alg_debug_msg_left_str_);
                    AddUssDiagInfo(alg_debug_msg_left_str_);
                }
                GetSvpVersion(alg_debug_msg_left_str_);
                AlgParameterConfig(alg_debug_msg_right_up_str_);
                AlgHealthMonitorAdapter(alg_debug_msg_right_up_str_);
                AlgVehicleChassisAdapter(alg_debug_msg_right_up_str_);
                AlgVehicleControllerAdapter(alg_debug_msg_right_up_str_);
                AlgPnCFsmAdapterShow(alg_debug_msg_right_up_str_);
                if(0x2 != getFsmMode() ){ //driving
                    AlgPncInferAdapter(alg_debug_msg_right_up_str_);
                    AlgPncDeciderAdapter(alg_debug_msg_right_up_str_);
                    AlgPncDeciderIDM(alg_debug_msg_left_second_str_);
                    AlgPnCTrajectoryAdapter(alg_debug_msg_right_up_str_);
                    AlgTfLightSSAdapter(alg_debug_traffic_light_ss);
                    AlgTfLightE2EAdapter(alg_debug_traffic_light_e2e);
                }
                AlgPncControlAdapter(alg_debug_msg_right_up_str_);
                AlgPncDebugAdapter(alg_debug_msg_right_up_str_);
                AlgBasicHodAdapter(alg_debug_msg_right_down_str_);
                if(0x2 == getFsmMode()){// AvpShow
                    AlgAvpAdapter(alg_debug_msg_right_up_str_);
                    AlgAvpParkingPanelStatus(alg_debug_prk_sts_str_);
                }
        }else{
            AlgIpcTimeShow(alg_debug_msg_left_str_);
            AlgNetMonitorAdapter(alg_debug_msg_left_str_);
            AlgCalibInfo(alg_debug_msg_left_str_);
            // ============================================================================================================= //
            // Sensor Info Part
            // ============================================================================================================= //
            if(0x2 != getFsmMode() ){ //driving
                AlgTlrPostpro(alg_debug_msg_left_str_);
              }
            AlgVehicleStatusAdapter(alg_debug_msg_left_str_);
            AlgSafetyCtrlAdapter(alg_debug_msg_left_str_);
            AlgGnssAdapter(alg_debug_msg_left_str_);
            // AlgInsAdapter(alg_debug_msg_left_str_);
            AlgImuAdapter(alg_debug_msg_left_str_);
            // Left Up
            // ============================================================================================================= //
            // Map & Loc Info Part
            // ============================================================================================================= //
            if (config_b_map_and_localization_show)
            {
                AlgLocSuperOdomAdapter(alg_debug_msg_left_str_);
                AlgLocMapMatchAdapter(alg_debug_msg_left_str_);
                AlgMapEngineServer(alg_debug_msg_left_str_);
                AlgSDDebugAdapter(alg_debug_msg_left_str_);
                // AlgRawMapDataAdapter(alg_debug_msg_left_str_);
            }
            AlgDrAdapter(alg_debug_msg_left_str_);
            if (config_b_vision_fusion_show)
            {
                AlgVisionFusionAdapter(alg_debug_msg_left_str_);
                AlgMonoMvTimeAdapter(alg_debug_msg_left_str_);
            }
            if (config_b_fusion_od_show)
            {
                AlgFusionOdAdapter(alg_debug_msg_left_str_);
                AlgFrontRadarFreqAdapter(alg_debug_msg_left_str_);
            }
            AlgFusionOdPredAdapter(alg_debug_msg_left_str_);
            if (config_b_fusion_road_show)
            {
                AlgFusionMonomapAdapter(alg_debug_msg_left_str_);
                AlgFusionRoadAdapter(alg_debug_msg_left_str_);
            }
            // Right Up Part
            AlgParameterConfig(alg_debug_msg_right_up_str_);
            // ============================================================================================================= //
            // Health Monitor Info Part
            // ============================================================================================================= //
            AlgHealthMonitorAdapter(alg_debug_msg_right_up_str_);
            // ============================================================================================================= //
            // Vehicle
            // ============================================================================================================= //
            AlgVehicleChassisAdapter(alg_debug_msg_right_up_str_);
            AlgVehicleControllerAdapter(alg_debug_msg_right_up_str_);
            // ============================================================================================================= //
            // Pnc
            // ============================================================================================================= //
            AlgPnCFsmAdapterShow(alg_debug_msg_right_up_str_);

            AlgPncInferAdapter(alg_debug_msg_right_up_str_);
    
            if(0x2 != getFsmMode()) {
                AlgPncDeciderAdapter(alg_debug_msg_right_up_str_);
                AlgPncDeciderIDM(alg_debug_msg_left_second_str_);
            }
    
            AlgPnCTrajectoryAdapter(alg_debug_msg_right_up_str_);

            AlgPncControlAdapter(alg_debug_msg_right_up_str_);

            // AlgPncRouterAdapter(alg_debug_msg_right_up_str_);

            AlgPncDebugAdapter(alg_debug_msg_right_up_str_);

            ParkingBuilderStateAdapter(alg_debug_msg_right_up_str_);
            // AvpShow
            AlgAvpAdapter(alg_debug_msg_right_up_str_);
            AlgAvpParkingPanelStatus(alg_debug_prk_sts_str_);
            // MID UP

            // ============================================================================================================= //
            // Basic HOD Ⓝ Ⓞ Ⓘ
            // ⇒ ⇐ ⇢ ⇠
            // ============================================================================================================= //
            AlgBasicHodAdapter(alg_debug_msg_right_down_str_);

            AlgUniinferAdapter(alg_debug_msg_left_str_);

            AddFixedLocalMapLegend(alg_debug_msg_left_str_);
            AddUssDiagInfo(alg_debug_msg_left_str_);
            AlgVisionFisheyeStitch(alg_debug_msg_left_str_);
            AlgSdNaviPathAdapter(alg_debug_msg_left_str_);
            GetSvpVersion(alg_debug_msg_left_str_);
            AlgTfLightSSAdapter(alg_debug_traffic_light_ss);

            AlgTfLightE2EAdapter(alg_debug_traffic_light_e2e);

        }


        AlgICAVoiceReminder(alg_debug_fsm_out_str_);
        PubText(alg_debug_info_left_pub_second , alg_debug_msg_left_second_str_);
        PubText(alg_debug_info_left_pub , alg_debug_msg_left_str_);
        PubText(alg_debug_info_right_up_pub , alg_debug_msg_right_up_str_);
        PubText(alg_debug_info_mid_up_pub , alg_debug_msg_right_down_str_);
        PubText(alg_debug_traffic_light_pub , alg_debug_traffic_light_ss);
        PubText(alg_e2e_traffic_light_pub , alg_debug_traffic_light_e2e);
        PubText(alg_prk_panel_sts_pub , alg_debug_prk_sts_str_);
        PubText(alg_fsm_out_pub , alg_debug_fsm_out_str_);

        nviz_2d_overlay_msgs::msg::OverlayText algErrorLogText;
        algErrorLogText.action = nviz_2d_overlay_msgs::msg::OverlayText::ADD;
        auto outputRight = extractLines(alg_debug_msg_right_up_str_.str());
        auto outputLeft = extractLines(alg_debug_msg_left_str_.str());
        algErrorLogText.text = outputLeft.str() + outputRight.str();
        alg_debug_info_error_pub->publish(algErrorLogText);
        publish_steering_wheel();
        publish_plot_xy();
        publish_plot_xy_steering_wheel();
    }

#define FOUND_MAP_RETURN_STRING(flag, enum_to_string)             \
    auto value = enum_to_string.find(flag);                       \
    std::string str = std::to_string(int(flag)) + " NOT support"; \
    if (enum_to_string.end() != value)                            \
    {                                                             \
        str = std::to_string(value->first) + " " + value->second; \
    }                                                             \
    return str;

    std::string AdapterDebugInfoShowNode::ButtonEnumToString(uint8_t flag)
    {
        const static std::map<uint8_t, std::string> enum_to_string = {
            {0x0, "NotShow"},
            {0x1, "Available"},
            {0x2, "NotAvailable"},
            {0x3, "Highlight"},
        };
        FOUND_MAP_RETURN_STRING(flag, enum_to_string);
    }
    visualization_msgs::msg::MarkerArray AdapterDebugInfoShowNode:: createRectangleWithTextMarker(
        const std::string& text,
        const RectangPosition& rectanglePosition,
        const RectangSize& rectangleSize,
        const RectangColor& rectangleColor,
        const RectangPosition& textPosition,
        const float textSize,
        const RectangColor& textColor,
        int marker_index
    ){
        visualization_msgs::msg::MarkerArray markerArray;
        // 创建矩形框标记
        visualization_msgs::msg::Marker rectangle_marker;
        rectangle_marker.header.frame_id = "base_link";
        rectangle_marker.header.stamp = nlibcpp::Time();
        rectangle_marker.ns = "rectangle" + std::to_string(marker_index);
        rectangle_marker.id = 0;
        rectangle_marker.type = visualization_msgs::msg::Marker::CUBE;
        rectangle_marker.action =visualization_msgs::msg::Marker::ADD;

        // 设置矩形框的位置
        rectangle_marker.pose.position.x = rectanglePosition.x;
        rectangle_marker.pose.position.y = rectanglePosition.y;
        rectangle_marker.pose.position.z = rectanglePosition.z;
        rectangle_marker.pose.orientation.x = 0.0;
        rectangle_marker.pose.orientation.y = 0.0;
        rectangle_marker.pose.orientation.z = 0.0;
        rectangle_marker.pose.orientation.w = 1.0;

        // 设置矩形框的大小
        rectangle_marker.scale.x = rectangleSize.x;
        rectangle_marker.scale.y = rectangleSize.y;
        rectangle_marker.scale.z = rectangleSize.z;

        // 设置矩形框的颜色
        rectangle_marker.color.r = rectangleColor.r;
        rectangle_marker.color.g = rectangleColor.g;
        rectangle_marker.color.b = rectangleColor.b;
        rectangle_marker.color.a = rectangleColor.a;

        markerArray.markers.push_back(rectangle_marker);

        // 创建文本标记
        visualization_msgs::msg::Marker text_marker;
        text_marker.header.frame_id = "base_link";
        text_marker.header.stamp = nlibcpp::Time();
        text_marker.ns = "text_" + std::to_string(marker_index);
        text_marker.id = 1;
        text_marker.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
        text_marker.action = visualization_msgs::msg::Marker::ADD;

        // 设置文本的位置
        text_marker.pose.position.x = textPosition.x;
        text_marker.pose.position.y = textPosition.y;
        text_marker.pose.position.z = textPosition.z;
        // text_marker.pose.orientation.x = 0.0;
        // text_marker.pose.orientation.y = 0.0;
        // text_marker.pose.orientation.z = 0.0;
        // text_marker.pose.orientation.w = 1.0;

        // 设置文本的大小
        // text_marker.scale.x = textSize;
        // text_marker.scale.y = textSize;
        text_marker.scale.z = textSize;


        // 设置文本的颜色
        text_marker.color.r = textColor.r;
        text_marker.color.g = textColor.g;
        text_marker.color.b = textColor.b;
        text_marker.color.a = textColor.a;
        // 设置文本内容
        text_marker.text = text;
        markerArray.markers.push_back(text_marker);

        return markerArray;
    }
    void AdapterDebugInfoShowNode::AlgAvpParkingPanelStatus(std::stringstream &stream)
    {
        visualization_msgs::msg::MarkerArray combined_marker_array; //TO DO  combind
        float pnc_fsm_out_frequency = 0.0;
        //alg_prk_panel_sts_pub
        std::map<bool,std::string> circle = {
            {true,"📀"},
            {false,"💿"}
        };
        std::string drv_30m = "行驶距离30m";
        std::string park_choice = "选择泊入车位";
        std::string learn_finish = "完成学习";
        std::string route_create = "路线创建完成";
        std::string learn_finish2 = "完成学习";
        std::string learn_finish_park = "完成学习并泊入";
        GetValueFromTopic(topic_name_pnc_fsm_out, topic_name_pnc_fsm_out_zero, topic_ptr_pnc_fsm_out, topic_ptr_pnc_fsm_out_zero, topic_ptr_pnc_fsm_out_tmp,
            is_ptr_pnc_fsm_out_got, is_ptr_pnc_fsm_out_zero_got, pnc_fsm_out_frequency);
        if(is_ptr_pnc_fsm_out_got || is_ptr_pnc_fsm_out_zero_got){
            //矩形共有属性
            RectangSize rectangleSize = {1, 6, 0.1};
            //文本共有属性
            float textSize = 1.0;
            RectangColor textColor = {1.0, 1.0, 1.0, 1.0};
            //======配置文本1 & 矩形1
            std::string finish_text = "learn_finish" ;
            // RectangPosition textPosition = {config_p_box_pos_x+10, config_p_box_pos_y, config_p_box_pos_z};
            RectangPosition textPosition = {config_p_box_pos_x+9, config_p_box_pos_y, config_p_box_pos_z};
            RectangPosition rectanglePosition = {config_p_box_pos_x+9, config_p_box_pos_y, config_p_box_pos_z};
            RectangColor rectangleColor = {1.0, 0.57, 0.0, 0.5}; //yellow
            // bool is_pub = false;
            //======配置文本2 & 矩形2
            std::string finish_text2 = "study_parking_finish" ;
            // RectangPosition textPosition2 = {config_p_box_pos_x+10, config_p_box_pos_y+10, config_p_box_pos_z};
            RectangPosition textPosition2 = {config_p_box_pos_x+9, config_p_box_pos_y+10, config_p_box_pos_z};
            RectangPosition rectanglePosition2 = {config_p_box_pos_x+9, config_p_box_pos_y+10, config_p_box_pos_z};
            RectangColor rectanglecolor_green = {1.0, 0.57, 0.0, 0.5}; //yellow
            uint8_t hpa_sts = topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_sts.hpa_sts_enum ;
            if( hpa_sts == 3 || hpa_sts == 4 || hpa_sts ==5){
                auto cir1 = topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_icon.hpa_learning_dist_m > 30 ? true : false;
                auto cir2 =  topic_ptr_pnc_fsm_out_tmp->prk_signals.parking_slot_id != 0 ? true : false;
                auto cir3 = hpa_sts == 4 ? true : false;
                auto cir4 = hpa_sts == 5 ? true : false;
                stream<<circle[cir1]+ "---------------------" + circle[cir2] + "---------------------" + circle[cir3] + "---------------------" +circle[cir4]<<std::endl;
                stream<<drv_30m + " | " + park_choice+" | " + learn_finish+ " | " +route_create<<std::endl;
                if(topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_icon.hpa_map_build_confirm_button_enum == 1){
                    rectangleColor = {1.0, 0.57, 0.0, 0.5}; //yellow
                    // is_pub = true;
                }
                else if(topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_icon.hpa_map_build_confirm_button_enum == 2){
                    rectangleColor = {0.5, 0.5, 0.5, 0.5}; //grey
                    // is_pub = true;
                }
                else{
                    // is_pub = false;
                }

                auto text1 = createRectangleWithTextMarker(finish_text, rectanglePosition,
                    rectangleSize,
                    rectangleColor,
                    textPosition,
                    textSize,
                    textColor,1);
                combined_marker_array.markers.insert(combined_marker_array.markers.end(), text1.markers.begin(), text1.markers.end());
                // park_status_box_pub->publish(text1);

                if(topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_icon.apa_start_park_in_button_enum == 1){
                    rectanglecolor_green = {1.0, 0.57, 0.0, 0.5}; //yellow
                    // is_pub = true;
                }
                else if(topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_icon.apa_start_park_in_button_enum == 2){
                    rectanglecolor_green = {0.5, 0.5, 0.5, 0.5}; //grey
                    // is_pub = true;
                }
                else{
                    // is_pub = false;
                }
                auto text2 = createRectangleWithTextMarker(finish_text2, rectanglePosition2,
                    rectangleSize,
                    rectanglecolor_green,
                    textPosition2,
                    textSize,
                    textColor,2);
                // 将 text2 中的所有 marker 加入到 combined_marker_array 中
                combined_marker_array.markers.insert(combined_marker_array.markers.end(), text2.markers.begin(), text2.markers.end());
                // park_status_box_pub->publish(text2);

                // 发布合并后的 MarkerArray
                if(!combined_marker_array.markers.empty()){
                    park_status_box_pub->publish(combined_marker_array);
                }
                }
            else if(hpa_sts == 13) {
                std::string finish_text3 = "start_parking" ;
                // RectangPosition textPosition2 = {config_p_box_pos_x+10, config_p_box_pos_y+10, config_p_box_pos_z};
                RectangPosition textPosition3 = {config_p_box_pos_x+7, config_p_box_pos_y+6, config_p_box_pos_z};
                RectangPosition rectanglePosition3 = {config_p_box_pos_x+7, config_p_box_pos_y+6, config_p_box_pos_z};
                RectangColor rectangleColor3 = {1.0, 0.57, 0.0, 0.5}; //yellow
                if(topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_icon.hpa_park_in_button_enum == 1){
                    rectangleColor3 = {1.0, 0.57, 0.0, 0.5}; //yellow
                }
                else if(topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_icon.hpa_park_in_button_enum == 2){
                    rectangleColor3 = {0.5, 0.5, 0.5, 0.5}; //grey
                }
                else{
                }
                auto text3 = createRectangleWithTextMarker(finish_text3, rectanglePosition3,
                    rectangleSize,
                    rectangleColor3,
                    textPosition3,
                    textSize,
                    textColor,3);
                combined_marker_array.markers.insert(combined_marker_array.markers.end(), text3.markers.begin(), text3.markers.end());
                if(!combined_marker_array.markers.empty()){
                    park_status_box_pub->publish(combined_marker_array);
                }
            }
            else{
                    // 清空可视化
                    visualization_msgs::msg::Marker clear_marker;
                    clear_marker.header.stamp = nlibcpp::Time();
                    clear_marker.ns = "clear";
                    clear_marker.id = 0;
                    clear_marker.action = visualization_msgs::msg::Marker::DELETEALL;
                    combined_marker_array.markers.emplace_back(clear_marker);
                    park_status_box_pub->publish(combined_marker_array);
            }
            }
        else{
            return;
        }
    }

    // Pnc FSM Show
    void AdapterDebugInfoShowNode::AlgPnCFsmAdapter(
        std::stringstream& stream, bool is_ptr_pnc_fsm_out_got, bool is_ptr_pnc_fsm_out_zero_got,
        float pnc_fsm_out_frequency, std::shared_ptr<fsm_msgs::msg::FSMOut const> topic_ptr_pnc_fsm_out_tmp) {
        TopicInfo topic_info;
        topic_info.head_name = "PnC FSM";
        topic_info.topic_name = topic_name_pnc_fsm_out;
        topic_info.node_name = "alg_pnc_fsm";
        stream << InitTopicHeader(topic_info, topic_ptr_pnc_fsm_out_tmp) << std::endl;
        if (!topic_ptr_pnc_fsm_out_tmp) {
            return;
        }
        else {
            // common
            if (topic_ptr_pnc_fsm_out_tmp->drv_prk_dual_info_enum == 0 ||
                topic_ptr_pnc_fsm_out_tmp->fsm_node_info.fsm_node_sts == 5) {
                stream << ADD_TEXT_COLOR_LEFT()
                       << "DrvPrkInfo : " << drv_prk_dual_info_map[topic_ptr_pnc_fsm_out_tmp->drv_prk_dual_info_enum]
                       << " | FsmSts : " << fsm_node_sts_map[topic_ptr_pnc_fsm_out_tmp->fsm_node_info.fsm_node_sts]
                       << color3;
            }
            else {
                stream << "DrvPrkInfo : " << drv_prk_dual_info_map[topic_ptr_pnc_fsm_out_tmp->drv_prk_dual_info_enum]
                       << " | FsmSts : " << fsm_node_sts_map[topic_ptr_pnc_fsm_out_tmp->fsm_node_info.fsm_node_sts];
            }
            stream << " | FuncMode : "
                   << fsm_driving_mode_map[topic_ptr_pnc_fsm_out_tmp->fsm_node_info.function_mode_enum];
            stream << std::endl;
            stream << "P2PSts: "
                   << fsm_p2p_sts_map[topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel
                                          .driving_cruising_func_sts.p2p_sts_enum];
            stream << " | LonHands: "
                   << fsm_vcu_handshake_req_map[topic_ptr_pnc_fsm_out_tmp->veh_signals.vcu_vlc_override_req_enum];
            stream << " | LatHands: "
                   << fsm_eps_handshake_req_map[topic_ptr_pnc_fsm_out_tmp->veh_signals.eps_handshake_req_enum];
            stream << std::endl;
            // driving
            if (0x01 == topic_ptr_pnc_fsm_out_tmp->drv_prk_dual_info_enum) {
                AddTextFromFsmoutForDriving(stream, topic_ptr_pnc_fsm_out_tmp);
            }
            // parking
            else if (0x02 == topic_ptr_pnc_fsm_out_tmp->drv_prk_dual_info_enum) {
                AddTextFromFsmoutForParking(stream, topic_ptr_pnc_fsm_out_tmp);
                AddApaTimeFromFsmout(stream, topic_ptr_pnc_fsm_out_tmp);
                stream << "规划耗时:0 | " << "启动耗时：" << vehicle_start_time_ << " | " << "泊入耗时："
                       << parking_in_time_ << " | " << "P+EPB:" << p_epb_time_ << " | " << "总时长:" << apa_total_time_
                       << std::endl;
            }
            last_apa_state_ = topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_sts.apa_sts_enum;
            if (topic_ptr_vehicle_chassis_10ms_tmp) {
                last_vehicle_state_ = topic_ptr_vehicle_chassis_10ms_tmp->vehdyn.movgsts.vehstst_sts_enum;
            }
        }
    }
    void AdapterDebugInfoShowNode::AddApaTimeFromFsmout(std::stringstream& stream,
                                                        std::shared_ptr<fsm_msgs::msg::FSMOut const> msg) {
        static bool is_pnc_planner_got = false;
        static bool is_pnc_planner_zero_got = false;
        static int8_t apa_active_step = 0;
        std::shared_ptr<planning_msgs::msg::TrajectoryOut const> topic_ptr_prk_trajectory_out_tmp;
        float pnc_planner_frequency = 0.0;
        if (getValue(topic_name_prk_trajectory_out, topic_ptr_prk_trajectory_out)) {
            is_pnc_planner_got = true;
            topic_ptr_prk_trajectory_out_tmp = topic_ptr_prk_trajectory_out;
            pnc_planner_frequency = getFrequency(topic_name_prk_trajectory_out, topic_ptr_prk_trajectory_out);
        }
        else {
            is_pnc_planner_got = false;
        }
        if (getValue(topic_name_prk_trajectory_out_zero, topic_ptr_prk_trajectory_out_zero)) {
            is_pnc_planner_zero_got = true;
            topic_ptr_prk_trajectory_out_tmp = topic_ptr_prk_trajectory_out_zero;
            pnc_planner_frequency = getFrequency(topic_name_prk_trajectory_out_zero, topic_ptr_prk_trajectory_out_zero);
        }
        else {
            is_pnc_planner_zero_got = false;
        }
        if ((is_pnc_planner_zero_got || is_pnc_planner_got) && topic_ptr_vehicle_chassis_10ms_tmp != nullptr &&
            msg->panel_signals.parking_panel.parking_func_sts.apa_sts_enum == 0x03) {
            switch (apa_active_step) {
                case 0:
                    if (last_apa_state_ != 0x03 &&
                        msg->panel_signals.parking_panel.parking_func_sts.apa_sts_enum == 0x03) {
                        apa_active_step = 1;
                        apa_time_ = std::chrono::steady_clock::now();
                    }
                    break;
                case 1:
                    if (last_vehicle_state_ == 0x01 &&
                        topic_ptr_vehicle_chassis_10ms_tmp->vehdyn.movgsts.vehstst_sts_enum != 0x01) {
                        apa_active_step = 2;
                        apa_time_ = std::chrono::steady_clock::now();
                    }
                    break;
                case 2:
                    if (topic_ptr_prk_trajectory_out_tmp->plan_node_info.lon_plan_status == 0x23) {
                        apa_active_step = 3;
                        apa_time_ = std::chrono::steady_clock::now();
                    }
                    break;
                case 3:
                    if (topic_ptr_prk_trajectory_out_tmp->plan_node_info.lon_plan_status == 0x22) {
                        apa_active_step = 0;
                        apa_time_ = std::chrono::steady_clock::now();
                    }
                    break;
                default:
                    break;
            }
            if (apa_active_step == 1) {
                vehicle_start_time_ =
                    std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - apa_time_)
                        .count();
            }
            else if (apa_active_step == 2) {
                parking_in_time_ =
                    std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - apa_time_)
                        .count();
            }
            else if (apa_active_step == 3) {
                p_epb_time_ =
                    std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - apa_time_)
                        .count();
            }
            // if (msg->panel_signals.parking_panel.parking_func_sts.apa_sts_enum == 0x02) {
            // }
        }
        else {
            vehicle_start_time_ = 0;
            parking_in_time_ = 0;
            p_epb_time_ = 0;
            apa_total_time_ = 0;
            apa_active_step = 0;
            apa_time_ = std::chrono::steady_clock::now();
        }
        apa_total_time_ = vehicle_start_time_ + parking_in_time_ + p_epb_time_;
    }

    void AdapterDebugInfoShowNode::AddTextFromFsmoutForDriving(std::stringstream& stream,
                                                               std::shared_ptr<fsm_msgs::msg::FSMOut const> msg) {
        static uint16_t function_mode_last = 0u;
        static uint16_t lat_fsm_sts_last = 0u;
        if (topic_ptr_pnc_fsm_out_tmp->veh_signals.turn_light_req_enum != 0 &&
            topic_ptr_pnc_fsm_out_tmp->veh_signals.turn_light_req_enum != 1) {
            stream << ADD_TEXT_COLOR_LEFT() << "TurnLightReq: "
                   << fsm_trun_light_req_map[topic_ptr_pnc_fsm_out_tmp->veh_signals.turn_light_req_enum] << color6;
        }
        else {
            stream << "TurnLightReq: "
                   << fsm_trun_light_req_map[topic_ptr_pnc_fsm_out_tmp->veh_signals.turn_light_req_enum];
        }
        stream << " | UserReqLC: "
               << fsm_user_req_lane_change_map[topic_ptr_pnc_fsm_out_tmp->drv_signals.user_req_lane_change_enum]
               << " | FolDist: "
               << fsm_acc_dist_status_map[topic_ptr_pnc_fsm_out_tmp->drv_signals.acc_dist_status_enum];
        stream << std::endl;
        stream << "LongSts: " << fsm_long_fsm_status_map[topic_ptr_pnc_fsm_out_tmp->drv_signals.long_fsm_sts_enum]
               << " | LatSts: " << fsm_lat_fsm_status_map[topic_ptr_pnc_fsm_out_tmp->drv_signals.lat_fsm_sts_enum]
               << " | NcaSts: "
               << fsm_nca_active_status_map[topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel
                                                .driving_cruising_func_sts.nca_sts_enum];
        stream << std::endl;

        stream << "McaSts: "
               << mca_sts_map[topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_sts
                                  .mca_sts_enum]
               << " | McaMapId: " << topic_ptr_pnc_fsm_out_tmp->drv_signals.mca_map_id;
        stream << " | HpaSts: "
               << static_cast<int>(
                      topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_sts.hpa_sts_enum);
        stream << " | ApaSts: "
               << static_cast<int>(
                      topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_sts.apa_sts_enum);
        stream << std::endl;

        const auto driving_disp_info_enum = topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel
                                                .driving_cruising_func_txt.driving_disp_info_enum;
        const static std::vector<int> need_red_highlight = {216, 217, 218};
        stream << ADD_TEXT_COLOR_LEFT() << "DispInfo: " << driving_disp_info_enum;
        if (std::find(need_red_highlight.begin(), need_red_highlight.end(), driving_disp_info_enum) !=
            need_red_highlight.end()) {
            stream << color3;
        }
        else {
            stream << color_default;
        }
        stream << " | Passive: "
               << static_cast<int>(topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel
                                       .driving_cruising_func_txt.driving_passive_reason_enum);
        stream << " | Fault: "
               << static_cast<int>(topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel
                                       .driving_cruising_func_txt.driving_failure_reason_enum);
        stream << " | Abort: "
               << static_cast<int>(topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel
                                       .driving_cruising_func_txt.driving_abort_reason_enum);
        stream << " | Audio: "
               << static_cast<int>(topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel
                                       .driving_cruising_func_voice.audio_warning_req_enum);
        stream << " | Voice: "
               << static_cast<int>(topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel
                                       .driving_cruising_func_voice.voice_reminder_req_enum);
        stream << std::endl;
        // temp tor reason
        if (0u != topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_switch
                      .driving_voice_broadcast_type_resp_enum) {
            stream << ADD_TEXT_COLOR_LEFT() << "TakeoverReason: "
                   << pnc_fsm_takeover_remind_reason_map[topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel
                                                             .driving_cruising_func_switch
                                                             .driving_voice_broadcast_type_resp_enum]
                   << color3 << std::endl;
        }
        std::bitset<32> quit_reason_high(topic_ptr_pnc_fsm_out_tmp->fsm_node_info.function_abort_reason_bit >> 32);
        std::bitset<32> quit_reason_low(topic_ptr_pnc_fsm_out_tmp->fsm_node_info.function_abort_reason_bit);
        stream << quit_reason_high.to_string() << std::endl;
        stream << "=======3=======2=======1=======0" << std::endl;
        stream << quit_reason_low.to_string() << std::endl;
        std::bitset<64> quit_reason(topic_ptr_pnc_fsm_out_tmp->fsm_node_info.function_abort_reason_bit);

        static bool fsm_abort_flag = false;
        static bool need_update_error_msg = false;
        static std::string quit_reason_str;

        const auto function_mode = topic_ptr_pnc_fsm_out_tmp->fsm_node_info.function_mode_enum;
        const auto lat_fsm_sts = topic_ptr_pnc_fsm_out_tmp->drv_signals.lat_fsm_sts_enum;

        // mca/nca -> ica -> acc
        if ((7u != lat_fsm_sts_last && 7u == lat_fsm_sts) ||
            (7u == function_mode_last && 6u > function_mode && 7u != lat_fsm_sts_last) ||
            (6u == function_mode_last && 6u > function_mode && 7u != lat_fsm_sts_last) ||
            (5u == function_mode_last && 5u > function_mode && 7u != lat_fsm_sts_last) ||
            (4u == function_mode_last && 4u > function_mode)) {
            fsm_abort_flag = true;
            need_update_error_msg = true;
        }
        // reset flag
        if (0u == function_mode_last && 4u <= function_mode) {
            fsm_abort_flag = false;
            need_update_error_msg = false;
        }
        function_mode_last = function_mode;
        lat_fsm_sts_last = lat_fsm_sts;
        // update error msg
        if (fsm_abort_flag && need_update_error_msg) {
            std::stringstream ss;
            for (int i = 0; i < (int)quit_reason.size(); i++) {
                if (quit_reason[i]) {
                    ss << ADD_TEXT_COLOR_LEFT() << i << ":" << pnc_fsm_abort_reason_bit_map[i]
                       << ((45 <= i && i <= 50) ? color_orange : color3) << std::endl;
                }
            }
            quit_reason_str = ss.str();
            need_update_error_msg = false;
        }
        // clear
        if (!fsm_abort_flag) {
            quit_reason_str.clear();
        }

        stream << quit_reason_str;
    }

    void AdapterDebugInfoShowNode::AddTextFromFsmoutForParking(std::stringstream& stream,
                                                               std::shared_ptr<fsm_msgs::msg::FSMOut const> msg) {
        // by sibangli
        stream << "ApaSts: " << apa_sts_map[msg->panel_signals.parking_panel.parking_func_sts.apa_sts_enum];
        stream << " | SlotId: " << int(msg->prk_signals.parking_slot_id);
        stream << " | ApaParkMode: "
               << apa_park_mode_map[topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_icon
                                        .apa_park_mode_enum];
        stream << std::endl;

        stream << "ApaRemainDistM: " << msg->panel_signals.parking_panel.parking_func_icon.apa_remain_dist_m;
        stream << " | ApaSpdType: " << int(msg->prk_signals.apa_spd_type_req_enum);
        stream << std::endl;

        stream << "HpaSts: " << hpa_sts_map[msg->panel_signals.parking_panel.parking_func_sts.hpa_sts_enum];
        stream << " | HpaMapId: " << int(msg->prk_signals.hpa_map_id);
        stream << " | LearnDistM: " << msg->panel_signals.parking_panel.parking_func_icon.hpa_learning_dist_m;
        stream << std::endl;

        stream << "HpaRemainPathDistM: " << msg->panel_signals.parking_panel.parking_func_icon.hpa_remain_path_dist_m;
        stream << std::endl;

        stream << "ParkDispInfo: "
               << parking_disp_info_map[msg->panel_signals.parking_panel.parking_func_txt.parking_disp_info_enum];
        stream << std::endl;

        stream << "ParkAbortRes: "
               << parking_abort_reason_map[topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_txt
                                               .parking_abort_reason_enum];
        stream << std::endl;
    }

    void AdapterDebugInfoShowNode::ParkingBuilderStateAdapter(std::stringstream &stream)
    {
    }

    void AdapterDebugInfoShowNode::AlgHmiHmiReqAdapter(std::stringstream &stream)
    {
        const bool has_value = getValue(topic_name_hmi_hmi_req,
                                        topic_ptr_hmi_hmi_req);
        if (!has_value)
        {
            stream << ADD_TEXT_COLOR_LEFT() << "=== HmiReq ===" << color3 << std::endl;
            return;
        }
        const auto frequency = getFrequency(topic_name_hmi_hmi_req, topic_ptr_hmi_hmi_req);
        stream << ADD_TEXT_COLOR_LEFT() << "=== HmiReq " << "| " << frequency << " Hz "
               << " ===" << color_green << std::endl;
        auto ReqEnumToString = [](uint8_t flag) -> std::string
        {
            const static std::map<uint8_t, std::string> enum_to_string = {
                {0x0, "No Request"},
                {0x1, "Request"},
            };
            FOUND_MAP_RETURN_STRING(flag, enum_to_string);
        };

        auto msg = topic_ptr_hmi_hmi_req;
        stream << "RoamParkReq : " << ReqEnumToString(msg->parking_pad_req.hpa_roam_park_req_enum) << " | "
               << "ExitReq : " << ReqEnumToString(msg->parking_pad_req.hpa_exit_req_enum) << std::endl;
        stream << "ParkInMapBuildReq : " << ReqEnumToString(msg->parking_pad_req.hpa_park_in_map_build_req_enum) << std::endl;
        stream << "MapBuildConfirmReq : " << ReqEnumToString(msg->parking_pad_req.hpa_map_build_confirm_req_enum) << std::endl;
        stream << "ParkInReq : " << ReqEnumToString(msg->parking_pad_req.hpa_park_in_req_enum) << " | "
               << "PauseReq : " << ReqEnumToString(msg->parking_pad_req.hpa_pause_req_enum) << std::endl;
        stream << "ContinueReq : " << ReqEnumToString(msg->parking_pad_req.hpa_continue_req_enum) << " | ";
        stream << "ApaPkReq : " << apa_park_in_req_enum[msg->parking_pad_req.apa_park_in_req_enum] << std::endl;
        stream << "ApaPkType : " << ReqEnumToString(msg->parking_pad_req.apa_park_type_req_enum) << " | ";
        stream << "ApaPkOutDirReq : " << apa_park_out_direction_req_enum[msg->parking_pad_req.apa_park_out_direction_req_enum] << std::endl;
        stream << std::fixed << std::setprecision(2);
        stream << "p0 : " << "(" << msg->parking_pad_req.apa_self_select_point0.x_m << "," << msg->parking_pad_req.apa_self_select_point0.y_m << ") ";
        stream << "p1 : " << "(" << msg->parking_pad_req.apa_self_select_point1.x_m << "," << msg->parking_pad_req.apa_self_select_point1.y_m << ") ";
        stream << "p2 : " << "(" << msg->parking_pad_req.apa_self_select_point2.x_m << "," << msg->parking_pad_req.apa_self_select_point2.y_m << ") ";
        stream << "p3 : " << "(" << msg->parking_pad_req.apa_self_select_point3.x_m << "," << msg->parking_pad_req.apa_self_select_point3.y_m << ")" << std::endl;
    }
    void AdapterDebugInfoShowNode::AlgIpcTimeShow(std::stringstream &stream)
    {
        if (getValue(topic_name_node_version, topic_ptr_node_version))
        {
            find_alg_node_version_info(topic_ptr_node_version->data.c_str());
        }

        time_t curr_time_in_pc;
        curr_time_in_pc = this->get_clock()->now().seconds();
        stream << "IPC System time: " << ctime(&curr_time_in_pc);

        GetValueFromTopic(topic_name_vehicle_chassis_10ms, topic_name_vehicle_chassis_10ms_zero, topic_ptr_vehicle_chassis_10ms, topic_ptr_vehicle_chassis_10ms_zero,
                          topic_ptr_vehicle_chassis_10ms_tmp, is_vehicle_chassis_10ms_got, is_vehicle_chassis_10ms_zero_got, vehicle_chassis_10ms_frequency);
        if (is_vehicle_chassis_10ms_got || is_vehicle_chassis_10ms_zero_got)
        {
            is_ipu_8775 = is_vehicle_chassis_10ms_zero_got;
            std::string gearsString = "";
            int64_t timestamep_ns_in_orin = topic_ptr_vehicle_chassis_10ms_tmp->std_header.timestamp_ns;
            time_t timestamp_s_in_orin = static_cast<long int>(timestamep_ns_in_orin * 1e-9);
            // ALG_DEBUG_INFO_SHOW::KeyCatch::GetInstance().SetSocTime(timestamp_s_in_orin);
            if (abs(curr_time_in_pc - timestamp_s_in_orin) > config_f_ipu_ipc_delay_time_s)
            {
                stream << ADD_TEXT_COLOR_LEFT() << "IPU System time: " << color_red << ctime(&timestamp_s_in_orin);
                stream << ADD_TEXT_COLOR_LEFT() << "IPU timestamp_ns: " << timestamep_ns_in_orin << color_red << std::endl;
            }
            else
            {
                stream << "IPU System time: " << std::string(ctime(&timestamp_s_in_orin));
                stream << "IPU timestamp_ns: " << timestamep_ns_in_orin << std::endl;
            }
        }
        else
        {
            stream << ADD_TEXT_COLOR_LEFT() << "IPU System time: NULL(Chassis_10ms Invalid)" << color3 << std::endl;
        }
    }
    void AdapterDebugInfoShowNode::AlgNetMonitorAdapter(std::stringstream &stream)
    {
        // network monitor
        static LinuxNetMonitor linux_net_monitor_;
        static std::string network_info;
        static int64_t ts0 = 0;

        int64_t ts1 = std::chrono::duration_cast<std::chrono::milliseconds>(
                          std::chrono::high_resolution_clock::now().time_since_epoch())
                          .count();
        const int network_speed_update_time_ms = 1000;
        if (ts1 - ts0 >= network_speed_update_time_ms)
        {
            ts0 = ts1;
            const auto net_status = linux_net_monitor_.GetStatus();
            std::ostringstream oss;
            for (const auto &one : net_status)
            {
                auto IsRxValid = [](double rx) -> bool
                {
                    static const double epsilon = 1e-9;
                    static const double min_speed = 0.001; // MB
                    return (rx > 0) && (std::fabs(rx - min_speed) > epsilon);
                };
                if (!IsRxValid(one.rx_bps_mb))
                {
                    continue;
                }
                oss << std::fixed << std::setprecision(3) << "Network name: " << one.name << " | RX: " << one.rx_bps_mb << " MB | TX: " << one.tx_bps_mb << " MB\n";
            }
            if (!oss.str().empty())
            {
                network_info = oss.str();
            }
        }
        stream << network_info;
    }

    void AdapterDebugInfoShowNode::AlgCalibInfo(std::stringstream& stream) {
        const char* const ENV_CALIB_VERSION = "NVIZ_ENV_CALIB_VERSION";
        const char* calib_version = std::getenv(ENV_CALIB_VERSION);
        if(calib_version) {
            stream << "calib_version: " << calib_version;
        } else {
            stream << ADD_TEXT_COLOR_LEFT() << "calib_version: NOT FOUND, 请检查标定" << color_red;
        }
        stream << std::endl;
    }

    void AdapterDebugInfoShowNode::AlgVehicleStatusAdapter(std::stringstream &stream)
    {
        std::shared_ptr<vehicle_msgs::msg::VehicleStatus const> topic_ptr_vehicle_status_50ms_tmp;

        if (config_b_vehicle_status_50ms_show)
        {
            TopicInfo topic_info;
            topic_info.head_name = "Vehicle Status";
            topic_info.topic_name = topic_name_vehicle_status_50ms;
            topic_info.node_name = "alg_adapter_vehicle_pub";
            stream << InitTopicHeader(topic_info,topic_ptr_vehicle_status_50ms_tmp) << std::endl;
            if(!topic_ptr_vehicle_status_50ms_tmp){
                return;
            }
            else{
                stream << "Door : FL " << topic_ptr_vehicle_status_50ms_tmp->vehbody.door_sts.driver_door_sts_enum
                       << " FR " << topic_ptr_vehicle_status_50ms_tmp->vehbody.door_sts.front_nondriver_door_sts_enum
                       << " RL " << topic_ptr_vehicle_status_50ms_tmp->vehbody.door_sts.rl_door_sts_enum
                       << " RR " << topic_ptr_vehicle_status_50ms_tmp->vehbody.door_sts.rr_door_sts_enum
                       << " (0=Locked) "
                       << "| ";

                stream << "BeltFst : ";
                for (size_t i = 0; i < 5; i++)
                {
                    stream << topic_ptr_vehicle_status_50ms_tmp->vehbody.is_seatbelt_fastened[i] << " ";
                }

                stream << " SeatOccpd : ";
                for (size_t i = 0; i < 5; i++)
                {
                    stream << topic_ptr_vehicle_status_50ms_tmp->vehbody.is_seat_occpd[i] << " ";
                }
                stream << std::endl;

                stream << "RearLoadSpace : " << topic_ptr_vehicle_status_50ms_tmp->vehbody.door_sts.loadspace_hood_sts_enum
                       << " Bonnet : " << topic_ptr_vehicle_status_50ms_tmp->vehbody.door_sts.bonnet_sts_enum << " | ";
                stream << "DirctLampSwitSts : " << topic_ptr_vehicle_status_50ms_tmp->vehbody.light_sts.dircn_ind_lamp_sw_sts_enum ;
                stream << " | DrvMd : " << veh_drv_mode_enum[topic_ptr_vehicle_status_50ms_tmp->veh_state.veh_drv_mode_enum];
                stream << " | MirSts : "<<" L "<<mirror_sts_enum[topic_ptr_vehicle_status_50ms_tmp->vehbody.mirror_sts.left_mirror_sts_enum]<< " | ";
                stream << "R " <<mirror_sts_enum[topic_ptr_vehicle_status_50ms_tmp->vehbody.mirror_sts.right_mirror_sts_enum]<<std::endl;
                stream << "=== Driver Command ===" << std::endl;
                if (topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.drv_req_shift_enum <= 6)
                {
                    if (topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.drv_req_shift_enum == 0)
                    {
                        stream << "DrvShiftComd : " << driver_command_shift_req_map[topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.drv_req_shift_enum];
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "DrvShiftComd : " << driver_command_shift_req_map[topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.drv_req_shift_enum] << color_green;
                    }
                }
                else
                {
                    stream << "DrvShiftComd : Invalid";
                }
                stream << " | ";
                if (topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.cruising_mode_act_req_enum == 1 || topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.spd_set_roller_okbutton_enum == 1)
                {
                    stream << ADD_TEXT_COLOR_LEFT()
                           << "ICAButn : " << static_cast<int>(topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.cruising_mode_act_req_enum)
                           << " | OkButn : " << static_cast<int>(topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.spd_set_roller_okbutton_enum)
                           << color_green;
                }
                else
                {
                    stream << "ICAButn : " << static_cast<int>(topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.cruising_mode_act_req_enum)
                           << " | OkButn : " << static_cast<int>(topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.spd_set_roller_okbutton_enum);
                }
                stream << " | ";

                if (topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.cruising_spd_increase_req_enum == 1 || topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.cruising_spd_decrease_req_enum == 1)
                {
                    stream << ADD_TEXT_COLOR_LEFT()
                           << "SpdButn : Incres " << static_cast<int>(topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.cruising_spd_increase_req_enum)
                           << " | Decres : " << static_cast<int>(topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.cruising_spd_decrease_req_enum)
                           << color_green
                           << std::endl;
                }
                else
                {
                    stream << "SpdButn : Incres " << static_cast<int>(topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.cruising_spd_increase_req_enum)
                           << " | Decres : " << static_cast<int>(topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.cruising_spd_decrease_req_enum)
                           << std::endl;
                }

                if (topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.spd_set_roller_dir_enum != 0 || topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.spd_set_roller_spd > 0.0)
                {
                    stream << ADD_TEXT_COLOR_LEFT()
                           << "SpdSetDir : " << driver_command_spd_set_roller_dir_map[topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.spd_set_roller_dir_enum]
                           << " Spd : " << topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.spd_set_roller_spd
                           << color_green;
                }
                else
                {
                    stream << "SpdSetDir : " << driver_command_spd_set_roller_dir_map[topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.spd_set_roller_dir_enum]
                           << " Spd : " << topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.spd_set_roller_spd;
                }
                stream << " | ";

                if (topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.fol_distance_incrs_enum == 1 || topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.fol_distance_decrs_enum == 1)
                {
                    stream << ADD_TEXT_COLOR_LEFT()
                           << "FolDist : Incres " << static_cast<int>(topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.fol_distance_incrs_enum)
                           << " Decres " << static_cast<int>(topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.fol_distance_decrs_enum)
                           << color_green;
                }
                else
                {
                    stream << "FolDist : Incres " << static_cast<int>(topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.fol_distance_incrs_enum)
                           << " Decres " << static_cast<int>(topic_ptr_vehicle_status_50ms_tmp->drvin.acc_swicth.fol_distance_decrs_enum);
                }
                stream << std::endl;
            }
        }
    }
    void AdapterDebugInfoShowNode::AlgGnssAdapter(std::stringstream &stream)
    {
        if (getValue(topic_name_sensor_pos_p100hz_zero, topic_ptr_sensor_pos_p100hz_zero))
        {
            topic_ptr_sensor_pos_p100hz_tmp = topic_ptr_sensor_pos_p100hz_zero;
        }
        else if (getValue(topic_name_sensor_pos_p100hz, topic_ptr_sensor_pos_p100hz))
        {
            topic_ptr_sensor_pos_p100hz_tmp = topic_ptr_sensor_pos_p100hz;
        } else {
            topic_ptr_sensor_pos_p100hz_tmp.reset();
        }

        if (config_b_sensor_p10hz_show)
        {
          TopicInfo topic_info;
          topic_info.head_name = "GNSS";
          topic_info.topic_name = topic_name_sensor_pos_p10hz;
          topic_info.node_name = "alg_adapter_ap_pub";
          stream << InitTopicHeader(topic_info,topic_ptr_sensor_pos_p10hz) << std::endl;
          if(topic_ptr_sensor_pos_p10hz){
            if (topic_ptr_sensor_pos_p10hz->gnssflag_pos_enum == 6)
            {
                stream << "GnssFlag : " << sensor_gnss_flag_pos_map[static_cast<uint32_t>(topic_ptr_sensor_pos_p10hz->gnssflag_pos_enum)] << " | INS Head : " << topic_ptr_sensor_pos_p100hz_tmp->ins_yaw_deg << " deg " << std::endl;
            }
            else
            {
                stream << ADD_TEXT_COLOR_LEFT() << "GnssFlag : " << sensor_gnss_flag_pos_map[static_cast<uint32_t>(topic_ptr_sensor_pos_p10hz->gnssflag_pos_enum)]
                       << color3;
                if(topic_ptr_sensor_pos_p100hz_tmp) {
                    stream << " | INS Head : " << topic_ptr_sensor_pos_p100hz_tmp->ins_yaw_deg << " ° ";
                }
                stream << std::endl;
            }
            stream << std::fixed << std::setprecision(7);
            double longitude = topic_ptr_sensor_pos_p10hz->gnss_longtitude_deg;
            double latitude = topic_ptr_sensor_pos_p10hz->gnss_latitude_deg;
            stream << "Gnss Lon " << longitude << " Lat " << latitude ;
            stream << std::fixed << std::setprecision(2);
            stream << " Alt " << topic_ptr_sensor_pos_p10hz->gnss_locatheight_m << " m " << " Heading " << topic_ptr_sensor_pos_p10hz->gnss_heading_deg << " ° " << std::endl;
          }
          else{
            return;
          }

        }
        else{
            return;
        }
    }
    void AdapterDebugInfoShowNode::AlgInsAdapter(std::stringstream &stream)
    {
        if (config_b_sensor_p100hz_show){
            TopicInfo topic_info;
            topic_info.head_name = "INS";
            topic_info.topic_name = topic_name_sensor_pos_p100hz;
            topic_info.node_name = "alg_adapter_ap_pub";
            stream << InitTopicHeader(topic_info,topic_ptr_sensor_pos_p100hz) << std::endl;
                // stream << std::fixed << std::setprecision(7);
                // double longitude = topic_ptr_sensor_pos_p100hz_tmp->ins_longd_deg;
                // double latitude = topic_ptr_sensor_pos_p100hz_tmp->ins_latd_deg;
                // stream << "Ins Lon : " << longitude << " Lat : " << latitude << std::endl;
                // stream << std::fixed << std::setprecision(2);
                // stream << "Ins Alt : " << topic_ptr_sensor_pos_p100hz_tmp->ins_hght_m << " m " << " Heading : " << topic_ptr_sensor_pos_p100hz_tmp->ins_yaw_deg << " deg "<< std::endl;
                // stream << "Ins Pitch : " << topic_ptr_sensor_pos_p100hz_tmp->ins_pitch_deg << " deg " << " Roll : " << topic_ptr_sensor_pos_p100hz_tmp->ins_roll_deg << " deg "<< std::endl;
                // stream << "Ins Conf : " << static_cast<int>(topic_ptr_sensor_pos_p100hz_tmp->ins_confidence_enum)
                //     << " ImuSyncStatus : " << static_cast<int>(topic_ptr_sensor_pos_p100hz_tmp->imu_syncstatus_enum) << std::endl;
                // stream << std::fixed << std::setprecision(6);
                // stream<<"std_ins_latd | longd : "<< topic_ptr_sensor_pos_p100hz_tmp -> std_ins_latd_deg<<" | "<<topic_ptr_sensor_pos_p100hz_tmp ->std_ins_longd_deg<<std::endl;
                // stream << std::fixed << std::setprecision(2);
        }
        else{
            return;
        }
    }
    void AdapterDebugInfoShowNode::AlgMonoMvTimeAdapter(std::stringstream &stream)
    {
        int64_t mono_ts = 0;
        static int mono_overtime_count = 0;
        static bool is_mono_got = false;
        if (getValue(topic_name_vision_monood_objects, topic_ptr_vision_monood_objects))
        {
            is_mono_got = true;
            float frequency = getFrequency(topic_name_vision_monood_objects, topic_ptr_vision_monood_objects);
            mono_ts = topic_ptr_vision_monood_objects->std_header.timestamp_ns;

            // if(frequency > config_f_vision_fusion_normal_freq * 1.2 || frequency < config_f_vision_fusion_normal_freq * 0.8) {
            //     alg_debug_msg_left_str_ << ADD_TEXT_COLOR_LEFT() << "MonoODFreq: " << " " << frequency << " Hz | " << color_orange;
            // } else {
            //     alg_debug_msg_left_str_ << "MonoODFreq: " << " " << frequency << " Hz | ";
            // }

            // if (is_loc_dr_zero_got || is_loc_dr_got) {
            //     float time_offset = static_cast<double>(loc_dr_ts - mono_ts) / 1000000.f;

            //     if (time_offset > config_f_vision_decode_normal_delay_time_ms) {
            //         mono_overtime_count++;
            //     } else {
            //         mono_overtime_count = 0;
            //     }

            //     if (mono_overtime_count > config_f_vision_decode_normal_delay_max_count) {
            //         mono_overtime_count = config_f_vision_decode_normal_delay_max_count + 1;
            //         alg_debug_msg_left_str_ << ADD_TEXT_COLOR_LEFT() << "TimeDelay: " << " " << time_offset << " ms " << color_orange << std::endl;
            //     } else {
            //         alg_debug_msg_left_str_ << "TimeDelay: " << " " << time_offset << " ms " << std::endl;
            //     }
            // } else {
            //     alg_debug_msg_left_str_ << std::endl;
            // }
        }
        else
        {
            is_mono_got = false;
        }

        int64_t mv_ts = 0;
        static int mv_overtime_count = 0;
        static bool is_mv_got = false;
        if (getValue(topic_name_vision_mvod_objects, topic_ptr_vision_mvod_objects))
        {
            is_mv_got = true;
            float frequency = getFrequency(topic_name_vision_mvod_objects, topic_ptr_vision_mvod_objects);
            mv_ts = topic_ptr_vision_mvod_objects->std_header.timestamp_ns;

            // if(frequency > config_f_vision_fusion_normal_freq * 1.2 || frequency < config_f_vision_fusion_normal_freq * 0.8) {
            //     alg_debug_msg_left_str_ << ADD_TEXT_COLOR_LEFT() << "MvODFreq: " << " " << frequency << " Hz | " << color_orange;
            // } else {
            //     alg_debug_msg_left_str_ << "MvODFreq: " << " " << frequency << " Hz | ";
            // }

            // if (is_loc_dr_zero_got || is_loc_dr_got) {
            //     float time_offset = static_cast<double>(loc_dr_ts - mv_ts) / 1000000.f;

            //     if (time_offset > config_f_vision_decode_normal_delay_time_ms) {
            //         mv_overtime_count++;
            //     } else {
            //         mv_overtime_count = 0;
            //     }

            //     if (mv_overtime_count > config_f_vision_decode_normal_delay_max_count) {
            //         mv_overtime_count = config_f_vision_decode_normal_delay_max_count + 1;
            //         alg_debug_msg_left_str_ << ADD_TEXT_COLOR_LEFT() << "TimeDelay: " << " " << time_offset << " ms " << color_orange << std::endl;
            //     } else {
            //         alg_debug_msg_left_str_ << "TimeDelay: " << " " << time_offset << " ms " << std::endl;
            //     }
            // } else {
            //     alg_debug_msg_left_str_ << std::endl;
            // }
        }
        else
        {
            is_mv_got = false;
        }

        static int drop_count = 0;
        if (is_mono_got && is_mv_got)
        {
            float time_offset = fabs(mv_ts - mono_ts) / 1000000.f;

            if (time_offset > config_f_vision_decode_normal_time_offset_ms)
            {
                drop_count++;
            }
            else
            {
                drop_count = 0;
            }

            if (drop_count > config_f_vision_decode_normal_delay_max_count)
            {
                drop_count = config_f_vision_decode_normal_delay_max_count + 1;
                stream << ADD_TEXT_COLOR_LEFT() << "Mono-Mv-TimeOffset: " << " " << time_offset << " ms " << color3<< std::endl;
            }
            else if (drop_count > 0)
            {
                stream << ADD_TEXT_COLOR_LEFT() << "Mono-Mv-TimeOffset: " << " " << time_offset << " ms " << color_orange << std::endl;
            }
            else
            {
                stream << "Mono-Mv-TimeOffset: " << " " << time_offset << " ms " << std::endl;
            }
        }
    }
    void AdapterDebugInfoShowNode::AlgVisionFusionAdapter(std::stringstream &stream)
    {

        std::shared_ptr<vision_msgs::msg::PerceptionObjects const> topic_ptr_vision_fusion_objects_tmp;
        TopicInfo topic_info;
        topic_info.head_name = "Vision Fusion OD" ;
        topic_info.topic_name = topic_name_vision_fusion_objects ;
        topic_info.node_name = "alg_vision_fusion_od" ; 
        stream << InitTopicHeader(topic_info,topic_ptr_vision_fusion_objects_tmp) << std::endl;
    }
    void AdapterDebugInfoShowNode::AlgFusionOdPredAdapter(std::stringstream &stream)
    {

        std::shared_ptr<fusion_msgs::msg::FusionObjects const> topic_ptr_fusion_objects_prediction_tmp;
        TopicInfo topic_info;
        topic_info.head_name = "Fusion OD Pred";
        topic_info.topic_name = topic_name_fusion_objects_prediction;
        topic_info.node_name = "alg_prediction_objects";
        stream << InitTopicHeader(topic_info,topic_ptr_fusion_objects_prediction_tmp) << std::endl;
    }
    void AdapterDebugInfoShowNode::AlgTfLightE2EAdapter(std::stringstream &stream)
    {
        bool flag = getValue(topic_name_e2e_traffic_light, topic_ptr_e2e_traffic_light_);
        if (getValue(topic_name_e2e_traffic_light, topic_ptr_e2e_traffic_light_))
        {
            monood_tlr_cls_decoder_t *topic_ptr_e2e_traffic_light = (monood_tlr_cls_decoder_t *)(topic_ptr_e2e_traffic_light_->data.data());
            if (topic_ptr_e2e_traffic_light->object_num == 0)
            {
                stream << ADD_TEXT_COLOR_LEFT() << " ↶ " << color_grey; // grey
                stream << ADD_TEXT_COLOR_LEFT() << " ← " << color_grey; // grey
                stream << ADD_TEXT_COLOR_LEFT() << " ↑ " << color_grey; // grey
                stream << ADD_TEXT_COLOR_LEFT() << " → " << color_grey; // grey
            }
            else
            {
                // uturn
                stream << ADD_TEXT_COLOR_LEFT() << " ↶ " << color_grey; // grey
                // left
                if (int(topic_ptr_e2e_traffic_light->traffic_light[0].colorid) == 2) // red
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " ← " << color_red; // red
                }
                else if (int(topic_ptr_e2e_traffic_light->traffic_light[0].colorid) == 1) // yellow
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " ← " << color6; // yellow
                }
                else if (int(topic_ptr_e2e_traffic_light->traffic_light[0].colorid) == 0) // green
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " ← " << color_green; // green
                }
                else
                {                                                                                         // dark
                    stream << ADD_TEXT_COLOR_LEFT() << " ← " << color_grey; // grey
                }
                // up
                if (int(topic_ptr_e2e_traffic_light->traffic_light[1].colorid) == 2) // red
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " ↑ " << color_red; // red
                }
                else if (int(topic_ptr_e2e_traffic_light->traffic_light[1].colorid) == 1) // yellow
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " ↑ " << color6; // yellow
                }
                else if (int(topic_ptr_e2e_traffic_light->traffic_light[1].colorid) == 0) // green
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " ↑ " << color_green; // green
                }
                else
                {                                                                                         // dark
                    stream << ADD_TEXT_COLOR_LEFT() << " ↑ " << color_grey; // grey
                }
                // right
                if (int(topic_ptr_e2e_traffic_light->traffic_light[2].colorid) == 2) // red
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " → " << color_red; // red
                }
                else if (int(topic_ptr_e2e_traffic_light->traffic_light[2].colorid) == 1) // yellow
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " → " << color6; // yellow
                }
                else if (int(topic_ptr_e2e_traffic_light->traffic_light[2].colorid) == 0) // green
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " → " << color_green; // green
                }
                else
                {                                                                                         // dark
                    stream << ADD_TEXT_COLOR_LEFT() << " → " << color_grey; // grey
                }
            }
        }
    }
    void AdapterDebugInfoShowNode::AlgTfLightSSAdapter(std::stringstream &stream)
    {
        if (getValue(topic_name_fusion_traffic_light, topic_ptr_fusion_traffic_light))
        {
            if (topic_ptr_fusion_traffic_light->traffic_lights_list.size() == 0)
            {
                stream << " 🚥 | ";
                stream << ADD_TEXT_COLOR_LEFT() << " ↶ " << color_grey; // grey
                stream << ADD_TEXT_COLOR_LEFT() << " ← " << color_grey; // grey
                stream << ADD_TEXT_COLOR_LEFT() << " ↑ " << color_grey; // grey
                stream << ADD_TEXT_COLOR_LEFT() << " → " << color_grey; // grey
            }
            else
            {
                // uturn
                if (topic_ptr_fusion_traffic_light->traffic_lights_list.size() == 4)
                {
                    stream << " 🚥 | ";
                    if (int(topic_ptr_fusion_traffic_light->traffic_lights_list[3].color_enum) == 1) // red
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << " ↶ " << color_red; // red
                    }
                    else if (int(topic_ptr_fusion_traffic_light->traffic_lights_list[3].color_enum) == 2) // yellow
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << " ↶ " << color6; // yellow
                    }
                    else if (int(topic_ptr_fusion_traffic_light->traffic_lights_list[3].color_enum) == 3) // green
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << " ↶ " << color_green; // green
                    }
                    else
                    {                                                                                         // dark
                        stream << ADD_TEXT_COLOR_LEFT() << " ↶ " << color_grey; // greyf
                    }
                }
                // left
                if (int(topic_ptr_fusion_traffic_light->traffic_lights_list[1].color_enum) == 1) // red
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " ← " << color_red; // red
                }
                else if (int(topic_ptr_fusion_traffic_light->traffic_lights_list[1].color_enum) == 2) // yellow
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " ← " << color6; // yellow
                }
                else if (int(topic_ptr_fusion_traffic_light->traffic_lights_list[1].color_enum) == 3) // green
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " ← " << color_green; // green
                }
                else
                {                                                                                         // dark
                    stream << ADD_TEXT_COLOR_LEFT() << " ← " << color_grey; // grey
                }
                // up
                if (int(topic_ptr_fusion_traffic_light->traffic_lights_list[0].color_enum) == 1) // red
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " ↑ " << color_red; // red
                }
                else if (int(topic_ptr_fusion_traffic_light->traffic_lights_list[0].color_enum) == 2) // yellow
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " ↑ " << color6; // yellow
                }
                else if (int(topic_ptr_fusion_traffic_light->traffic_lights_list[0].color_enum) == 3) // green
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " ↑ " << color_green; // green
                }
                else
                {                                                                                         // dark
                    stream << ADD_TEXT_COLOR_LEFT() << " ↑ " << color_grey; // grey
                }
                // right
                if (int(topic_ptr_fusion_traffic_light->traffic_lights_list[2].color_enum) == 1) // red
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " → " << color_red; // red
                }
                else if (int(topic_ptr_fusion_traffic_light->traffic_lights_list[2].color_enum) == 2) // yellow
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " → " << color6; // yellow
                }
                else if (int(topic_ptr_fusion_traffic_light->traffic_lights_list[2].color_enum) == 3) // green
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " → " << color_green; // green
                }
                else
                {                                                                                         // dark
                    stream << ADD_TEXT_COLOR_LEFT() << " → " << color_grey; // grey
                }
                stream << " | ";
                std::string SDKTrafficlightSensorString = "Ⓜ", VisionTrafficlightSensorString = "Ⓥ", NoneTrafficlightSensorString = "Ⓝ";
                if (topic_ptr_fusion_traffic_light->traffic_lights_list.size() == 0)
                {
                    stream << ADD_TEXT_COLOR_LEFT() << NoneTrafficlightSensorString << ADD_TEXT_COLOR_RIGHT(1.0, 1.0, 1.0, 1.0); // white
                }
                else
                {
                    bool light_contributing_bit_1 = false;
                    // maybe traffic_lights_list size over 4, but only first 4 lights used.
                    constexpr int max_lights = 4;
                    int i = 0;
                    for(const auto& light : topic_ptr_fusion_traffic_light->traffic_lights_list) {
                        if(++i > max_lights) {
                            break;
                        }
                        if(1 == light.contributing_sensors_bit){
                            light_contributing_bit_1 = true;
                            break;
                        }
                    }
                    if(light_contributing_bit_1) {
                        stream << ADD_TEXT_COLOR_LEFT() << SDKTrafficlightSensorString << color_green; // green
                    } else {
                        stream << ADD_TEXT_COLOR_LEFT() << VisionTrafficlightSensorString << color_red; // red
                    }
                    stream << " | ";
                }
                stream << std::fixed << std::setprecision(2) << topic_ptr_fusion_traffic_light->traffic_lights_list[0].position.z_m << " m "; // grey
            }
        }
    }

    //后面要特殊处理没有导航的时候直接全部不显示
    void AdapterDebugInfoShowNode::AlgSdNaviPathAdapter(std::stringstream &stream)
    {
        static float map_navi_path_frequency = 0.0;
        TopicInfo topic_info;
        topic_info.head_name = "SD NaviPath";
        topic_info.topic_name = topic_name_map_navi_path;
        stream << InitTopicHeader(topic_info,topic_ptr_map_navi_path) << std::endl;
        if(!topic_ptr_map_navi_path){
            return;
        }
        else{
            time_t header_time = static_cast<long int>(topic_ptr_map_navi_path->std_header.timestamp_ns * 1e-9);
            time_t andriod_time = static_cast<long int>(topic_ptr_map_navi_path->android_sys_time.timestamp_ns * 1e-9);
            stream << "AndroidTime : " << topic_ptr_map_navi_path->android_sys_time.timestamp_ns << "(" << std::string(ctime(&andriod_time)).substr(11, 8) << ")" << std::endl;
            stream << "CurLink : SegIdx " << topic_ptr_map_navi_path->ego_pose.cur_seg_idx
                   << " | LinkIdx " << topic_ptr_map_navi_path->ego_pose.cur_link_idx
                   << " | ReminDist : " << topic_ptr_map_navi_path->ego_pose.link_remain_dist << " m " << std::endl;


            bool found_current = false;
            size_t current_seg_index = 0;
            size_t current_link_index = 0;

            // 第一步：找到当前的 link_info
            for (size_t seg_idx = 0; seg_idx < topic_ptr_map_navi_path->segment_info_list.size(); ++seg_idx) {
                const auto& segment_info = topic_ptr_map_navi_path->segment_info_list[seg_idx];
                for (size_t link_idx = 0; link_idx < segment_info.link_list.size(); ++link_idx) {
                    const auto& link_info = segment_info.link_list[link_idx];
                    if (link_info.link_index == topic_ptr_map_navi_path->ego_pose.cur_link_idx &&
                        link_info.related_segement_index == topic_ptr_map_navi_path->ego_pose.cur_seg_idx) {
                        found_current = true;
                        current_seg_index = seg_idx;
                        current_link_index = link_idx;
                        break;
                    }
                }
                if (found_current) break;
            }

            if (found_current) {
                // 显示当前的 link_info
                std::stringstream length, speed_limit_kph, has_light;
                const auto& current_segment = topic_ptr_map_navi_path->segment_info_list[current_seg_index];
                const auto& current_link = current_segment.link_list[current_link_index];
                length << current_link.length_m;
                speed_limit_kph << static_cast<int>(topic_ptr_map_navi_path->ego_pose.speed_limit_kph);
                has_light << static_cast<int>(current_link.has_trafficlight);

                // 显示下一个 segment_info 数组中的前两个 link_info
                size_t next_seg_index = current_seg_index + 1;
                if (next_seg_index < topic_ptr_map_navi_path->segment_info_list.size()) {
                    const auto& next_segment = topic_ptr_map_navi_path->segment_info_list[next_seg_index];
                    int count = 0;
                    for (const auto& next_link : next_segment.link_list) {
                        if (count >= 2) break;
                        length << " " << next_link.length_m;
                        speed_limit_kph << " " << static_cast<int>(topic_ptr_map_navi_path->ego_pose.speed_limit_kph);
                        has_light << " " << static_cast<int>(next_link.has_trafficlight);
                        ++count;
                    }
                }
                stream << "Link: " << length.str() << " m | " << speed_limit_kph.str() << " kph | Light: " << has_light.str() << std::endl;
            }
            stream << "SegIdx : " << topic_ptr_map_navi_path->lane_info.segment_idx;
            stream << " LinkIdx : " << topic_ptr_map_navi_path->lane_info.link_idx << " | ";

            stream << "FrontLane : ";
            for (const auto &laneid : topic_ptr_map_navi_path->lane_info.front_lane)
            {
                stream << laneid << " ";
            }
            stream << "BackLane : ";
            for (const auto &laneid : topic_ptr_map_navi_path->lane_info.back_lane)
            {
                stream << laneid << " ";
            }
            stream << std::endl;
            std::vector<int> back_lane_result;
            std::vector<int> front_lane_result;
            extractLaneData(topic_ptr_map_navi_path->lane_info.back_lane, topic_ptr_map_navi_path->lane_info.front_lane,
                            stream);
            if (topic_ptr_map_navi_path->ego_pose.navi_info_data_list.size())
            {
                std::stringstream ss_hex;
                ss_hex << std::setfill('0') << std::setw(2) << std::hex << std::uppercase << topic_ptr_map_navi_path->ego_pose.navi_info_data_list[0].maneuverl_id_enum;
                std::string hex_str = "0x" + ss_hex.str();
                stream <<" Icon :"<< " [ " << hex_str << "  " << topic_ptr_map_navi_path->ego_pose.navi_info_data_list[0].segment_remain.dist << "m "
                        << topic_ptr_map_navi_path->ego_pose.navi_info_data_list[0].segment_remain.time << "s]";
                if(topic_ptr_map_navi_path->ego_pose.navi_info_data_list.size()>=2)
                {
                    std::stringstream ss_hex2;
                    ss_hex2 << std::setfill('0') << std::setw(2) << std::hex << std::uppercase << topic_ptr_map_navi_path->ego_pose.navi_info_data_list[1].maneuverl_id_enum;
                    std::string hex_str2 = "0x" + ss_hex2.str();
                    stream<<"->"<<"["<< hex_str2 << "  " << topic_ptr_map_navi_path->ego_pose.navi_info_data_list[1].segment_remain.dist << "m "
                        << topic_ptr_map_navi_path->ego_pose.navi_info_data_list[1].segment_remain.time << "s]";
                }
                stream << std::endl;
            }

            stream << "TraficLight : SegIdx " << topic_ptr_map_navi_path->traffic_light_countdown.segment_index
                   << " | LinkIdx " << topic_ptr_map_navi_path->traffic_light_countdown.link_index
                   << " | Dir " << topic_ptr_map_navi_path->traffic_light_countdown.light_info.dir
                   << " | Desc " << topic_ptr_map_navi_path->traffic_light_countdown.light_info.desc
                   << std::endl;
            const size_t min_size = min(3, topic_ptr_map_navi_path->traffic_light_countdown.light_info.light_states.size());

            for (size_t i = 0; i < min_size; i++)
            {
                const auto &light = topic_ptr_map_navi_path->traffic_light_countdown.light_info.light_states[i];
                const int diff = light.etime - light.time;
                time_t start_time = static_cast<long int>(light.time);
                time_t end_time = static_cast<long int>(light.etime);
                stream << "[" << i << "]" << "Type : " << map_navi_path_trafficlight_type_map[light.type]
                       << " | From : " << light.time << "(" << std::string(ctime(&start_time)).substr(11, 8) << ")"
                       << " To " << light.etime << "(" << std::string(ctime(&end_time)).substr(11, 8) << ")"
                       << " | Diff " << diff
                       << std::endl;
            }
        }
        // stream << "=== Trigger Event ===" << std::endl;
        // for (auto &event_str : ALG_DEBUG_INFO_SHOW::KeyCatch::GetInstance().GetEventList())
        // {
        //     stream << event_str << std::endl;
        // }
    }
    void AdapterDebugInfoShowNode::AlgUniinferAdapter(std::stringstream &stream)
    {
        if (config_b_uniinfer_delaytime_show)
        {
            str_alg_uniinfer_version = get_alg_node_version("alg_uniinfer");
            if (str_alg_uniinfer_version.length() < 1)
            {
                stream << ADD_TEXT_COLOR_LEFT() << "=== Uniinfer ===" << color_red << std::endl;
            }
            else
            {
                stream << ADD_TEXT_COLOR_LEFT() << "=== Uniinfer | " << str_alg_uniinfer_version << " ===" << color_green << std::endl;
                if (is_driving_mode() && is_ipu_8775)
                {
                    static float adapter_f120_freq = 0.0, adapter_f120_delay_s = 0.0;
                    static std::string adapter_f120_color = color_grey;
                    static bool is_adapter_f120_got = false;
                    static float adapter_mv_freq = 0.0, adapter_mv_delay_s = 0.0;
                    static std::string adapter_mv_color = color_grey;
                    static bool is_adapter_mv_got = false;
                    static float adapter_mvf_freq = 0.0, adapter_mvf_delay_s = 0.0;
                    static std::string adapter_mvf_color = color_grey;
                    static bool is_adapter_mvf_got = false;
                    static float tsr_freq = 0.0, tsr_delay_s = 0.0;
                    static std::string tsr_color = color_grey;
                    static bool is_tsr_got = false;
                    static float tlr_freq = 0.0, tlr_delay_s = 0.0;
                    static std::string tlr_color = color_grey;
                    static bool is_tlr_got = false;
                    static float full_obj_freq = 0.0, full_obj_delay_s = 0.0;
                    static std::string full_obj_color = color_grey;
                    static bool is_full_obj_got = false;
                    static float lm_freq = 0.0, lm_delay_s = 0.0;
                    static std::string lm_color = color_grey;
                    static bool is_lm_got = false;
                    static float bev_lane_freq = 0.0, bev_lane_delay_s = 0.0;
                    static std::string bev_lane_color = color_grey;
                    static bool is_bev_lane_got = false;
                    static float cone_obj_freq = 0.0, cone_obj_delay_s = 0.0;
                    static std::string cone_obj_color = color_grey;
                    static bool is_cone_obj_got = false;
                    static float mvod_freq = 0.0, mvod_delay_s = 0.0;
                    static std::string mvod_color = color_grey;
                    static bool is_mvod_got = false;
                    static float mvfod_freq = 0.0, mvfod_delay_s = 0.0;
                    static std::string mvfod_color = color_grey;
                    static bool is_mvfod_got = false;

                    if (getValue(topic_name_adapter_f120, topic_ptr_vision_adapter_f120))
                    {
                        adapter_f120_freq = getFrequency(topic_name_adapter_f120, topic_ptr_vision_adapter_f120);
                        adapter_f120_delay_s = topic_ptr_vision_adapter_f120->data / 1000000000.0;
                        is_adapter_f120_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(adapter_f120_freq), DelaySeverity(adapter_f120_delay_s));
                        adapter_f120_color = SeverityColor(sev);
                    }
                    else
                    {
                        adapter_f120_freq = -1.0;
                        adapter_f120_delay_s = -1.0;
                        is_adapter_f120_got = false;
                        adapter_f120_color = color_grey;
                    }

                    // mv adapter
                    if (getValue(topic_name_adapter_mv, topic_ptr_vision_adapter_mv))
                    {
                        adapter_mv_freq = getFrequency(topic_name_adapter_mv, topic_ptr_vision_adapter_mv);
                        adapter_mv_delay_s = topic_ptr_vision_adapter_mv->data / 1000000000.0;
                        is_adapter_mv_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(adapter_mv_freq), DelaySeverity(adapter_mv_delay_s));
                        adapter_mv_color = SeverityColor(sev);
                    }
                    else
                    {
                        adapter_mv_freq = -1.0f;
                        adapter_mv_delay_s = -1.0;
                        is_adapter_mv_got = false;
                        adapter_mv_color = color_grey;
                    }
                    // mvf adapter
                    if (getValue(topic_name_adapter_mvf, topic_ptr_vision_adapter_mvf))
                    {
                        adapter_mvf_freq = getFrequency(topic_name_adapter_mvf, topic_ptr_vision_adapter_mvf);
                        adapter_mvf_delay_s = topic_ptr_vision_adapter_mvf->data / 1000000000.0;
                        is_adapter_mvf_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(adapter_mvf_freq), DelaySeverity(adapter_mvf_delay_s));
                        adapter_mvf_color = SeverityColor(sev);
                    }
                    else
                    {
                        adapter_mvf_freq = -1.0;
                        adapter_mvf_delay_s = -1.0;
                        is_adapter_mvf_got = false;
                        adapter_mvf_color = color_grey;
                    }

                    // tsr
                    if (getValue(topic_name_uniinfer_tsr, topic_ptr_vision_uniinfer_tsr))
                    {
                        tsr_freq = getFrequency(topic_name_uniinfer_tsr, topic_ptr_vision_uniinfer_tsr);
                        tsr_delay_s = topic_ptr_vision_uniinfer_tsr->data / 1000000000.0;
                        is_tsr_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(tsr_freq), DelaySeverity(tsr_delay_s));
                        tsr_color = SeverityColor(sev);
                    }
                    else
                    {
                        tsr_freq = -1.0;
                        tsr_delay_s = -1.0;
                        is_tsr_got = false;
                        tsr_color = color_grey;
                    }

                    // tlr
                    if (getValue(topic_name_uniinfer_tlr, topic_ptr_vision_uniinfer_tlr))
                    {
                        tlr_freq = getFrequency(topic_name_uniinfer_tlr, topic_ptr_vision_uniinfer_tlr);
                        tlr_delay_s = topic_ptr_vision_uniinfer_tlr->data / 1000000000.0;
                        is_tlr_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(tlr_freq), DelaySeverity(tlr_delay_s));
                        tlr_color = SeverityColor(sev);
                    }
                    else
                    {
                        tlr_freq = -1.0;
                        tlr_delay_s = -1.0;
                        is_tlr_got = false;
                        tlr_color = color_grey;
                    }

                    // od dynamic
                    if (getValue(topic_name_uniinfer_full_obj, topic_ptr_vision_uniinfer_full_obj))
                    {
                        full_obj_freq = getFrequency(topic_name_uniinfer_full_obj, topic_ptr_vision_uniinfer_full_obj);
                        full_obj_delay_s = topic_ptr_vision_uniinfer_full_obj->data / 1000000000.0;
                        is_full_obj_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(full_obj_freq), DelaySeverity(full_obj_delay_s));
                        full_obj_color = SeverityColor(sev);
                    }
                    else
                    {
                        full_obj_freq = -1.0;
                        full_obj_delay_s = -1.0;
                        is_full_obj_got = false;
                        full_obj_color = color_grey;
                    }

                    // lm
                    if (getValue(topic_name_uniinfer_lm, topic_ptr_vision_uniinfer_lm))
                    {
                        lm_freq = getFrequency(topic_name_uniinfer_lm, topic_ptr_vision_uniinfer_lm);
                        lm_delay_s = topic_ptr_vision_uniinfer_lm->data / 1000000000.0;
                        is_lm_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(lm_freq), DelaySeverity(lm_delay_s));
                        lm_color = SeverityColor(sev);
                    }
                    else
                    {
                        lm_freq = -1.0;
                        lm_delay_s = -1.0;
                        is_lm_got = false;
                        lm_color = color_grey;
                    }

                    // bev lane
                    if (getValue(topic_name_uniinfer_bev_lane, topic_ptr_vision_uniinfer_bev_lane))
                    {
                        bev_lane_freq = getFrequency(topic_name_uniinfer_bev_lane, topic_ptr_vision_uniinfer_bev_lane);
                        bev_lane_delay_s = topic_ptr_vision_uniinfer_bev_lane->data / 1000000000.0;
                        is_bev_lane_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(bev_lane_freq), DelaySeverity(bev_lane_delay_s));
                        bev_lane_color = SeverityColor(sev);
                    }
                    else
                    {
                        bev_lane_freq = -1.0;
                        bev_lane_delay_s = -1.0;
                        is_bev_lane_got = false;
                        bev_lane_color = color_grey;
                    }

                    // cone obj
                    if (getValue(topic_name_uniinfer_cone_obj, topic_ptr_vision_uniinfer_cone_obj))
                    {
                        cone_obj_freq = getFrequency(topic_name_uniinfer_cone_obj, topic_ptr_vision_uniinfer_cone_obj);
                        cone_obj_delay_s = topic_ptr_vision_uniinfer_cone_obj->data / 1000000000.0;
                        is_cone_obj_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(cone_obj_freq), DelaySeverity(cone_obj_delay_s));
                        cone_obj_color = SeverityColor(sev);
                    }
                    else
                    {
                        cone_obj_freq = -1.0;
                        cone_obj_delay_s = -1.0;
                        is_cone_obj_got = false;
                        cone_obj_color = color_grey;
                    }

                    // mvod
                    if (getValue(topic_name_uniinfer_mvod, topic_ptr_vision_uniinfer_mvod))
                    {
                        mvod_freq = getFrequency(topic_name_uniinfer_mvod, topic_ptr_vision_uniinfer_mvod);
                        mvod_delay_s = topic_ptr_vision_uniinfer_mvod->data / 1000000000.0;
                        is_mvod_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(mvod_freq), DelaySeverity(mvod_delay_s));
                        mvod_color = SeverityColor(sev);
                    }
                    else
                    {
                        mvod_freq = -1.0;
                        mvod_delay_s = -1.0;
                        is_mvod_got = false;
                        mvod_color = color_grey;
                    }

                    // mvfod
                    if (getValue(topic_name_uniinfer_mvfod, topic_ptr_vision_uniinfer_mvfod))
                    {
                        mvfod_freq = getFrequency(topic_name_uniinfer_mvfod, topic_ptr_vision_uniinfer_mvfod);
                        mvfod_delay_s = topic_ptr_vision_uniinfer_mvfod->data / 1000000000.0;
                        is_mvfod_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(mvfod_freq), DelaySeverity(mvfod_delay_s));
                        mvfod_color = SeverityColor(sev);
                    }
                    else
                    {
                        mvfod_freq = -1.0;
                        mvfod_delay_s = -1.0;
                        is_mvfod_got = false;
                        mvfod_color = color_grey;
                    }
                    stream << "Mod : ";
                    if (is_adapter_f120_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "AF120" << adapter_f120_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "AF120" << color_grey << " | ";
                    }
                    if (is_adapter_mv_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "AM_V" << adapter_mv_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "AM_V" << color_grey << " | ";
                    }
                    if (is_adapter_mvf_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "AM_Vf" << adapter_mvf_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "M_Vf" << color_grey << " | ";
                    }
                    if (is_tsr_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "TSR" << tsr_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "TSR " << color_grey << " | ";
                    }
                    if (is_tlr_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "TLR" << tlr_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "TLR " << color_grey << " | ";
                    }
                    if (is_full_obj_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "O_D" << full_obj_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "O_D " << color_grey << " | ";
                    }
                    if (is_lm_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "L_M" << lm_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "L_M " << color_grey << " | ";
                    }
                    if (is_bev_lane_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "BevL" << bev_lane_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "BevL " << color_grey << " | ";
                    }
                    if (is_cone_obj_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "cnobj" << cone_obj_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "cnobj " << color_grey << " | ";
                    }
                    if (is_mvod_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "mvod" << mvod_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "mvod" << color_grey << " | ";
                    }
                    if (is_mvfod_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "mvfod" << mvfod_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "mvfod" << color_grey << " | ";
                    }
                    stream << std::endl;

                    stream << "Fqcy(hz): ";
                    if (is_adapter_f120_got)
                    {
                        int prec = (adapter_f120_freq >= 9.995) ? 1 : 2;
                        AppendColoredValue(stream, adapter_f120_freq, adapter_f120_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, adapter_f120_freq, color_grey);
                    }
                    if (is_adapter_mv_got)
                    {
                        int prec = (adapter_mv_freq >= 9.995) ? 1 : 2;
                        AppendColoredValue(stream, adapter_mv_freq, adapter_mv_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, adapter_mv_freq, color_grey);
                    }
                    if (is_adapter_mvf_got)
                    {
                        int prec = (adapter_mvf_freq >= 9.995) ? 1 : 2;
                        AppendColoredValue(stream, adapter_mvf_freq, adapter_mvf_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, adapter_mvf_freq, color_grey);
                    }
                    if (is_tsr_got)
                    {
                        int prec = (tsr_freq >= 9.995) ? 1 : 2;
                        AppendColoredValue(stream, tsr_freq, tsr_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, tsr_freq, color_grey);
                    }
                    if (is_tlr_got)
                    {
                        int prec = (tlr_freq >= 9.995) ? 1 : 2;
                        AppendColoredValue(stream, tlr_freq, tlr_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, tlr_freq, color_grey);
                    }
                    if (is_full_obj_got)
                    {
                        int prec = (full_obj_freq >= 9.995) ? 1 : 2;
                        AppendColoredValue(stream, full_obj_freq, full_obj_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, full_obj_freq, color_grey);
                    }
                    if (is_lm_got)
                    {
                        int prec = (lm_freq >= 9.995) ? 1 : 2;
                        AppendColoredValue(stream, lm_freq, lm_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, lm_freq, color_grey);
                    }
                    if (is_bev_lane_got)
                    {
                        int prec = (bev_lane_freq >= 9.995) ? 1 : 2;
                        AppendColoredValue(stream, bev_lane_freq, bev_lane_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, bev_lane_freq, color_grey);
                    }
                    if (is_cone_obj_got)
                    {
                        int prec = (cone_obj_freq >= 9.995) ? 1 : 2;
                        AppendColoredValue(stream, cone_obj_freq, cone_obj_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, cone_obj_freq, color_grey);
                    }
                    if (is_mvod_got)
                    {
                        int prec = (mvod_freq >= 9.995) ? 1 : 2;
                        AppendColoredValue(stream, mvod_freq, mvod_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, mvod_freq, color_grey);
                    }
                    if (is_mvfod_got)
                    {
                        int prec = (mvfod_freq >= 9.995) ? 1 : 2;
                        AppendColoredValue(stream, mvfod_freq, mvfod_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, mvfod_freq, color_grey);
                    }
                    stream << std::endl;

                    stream << std::fixed << std::setprecision(2);
                    stream << "Delay(s): ";
                    if (is_adapter_f120_got)
                    {
                        AppendColoredValue(stream, adapter_f120_delay_s, adapter_f120_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, adapter_f120_delay_s, color_grey);
                    }
                    if (is_adapter_mv_got)
                    {
                        AppendColoredValue(stream, adapter_mv_delay_s, adapter_mv_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, adapter_mv_delay_s, color_grey);
                    }
                    if (is_adapter_mvf_got)
                    {
                        AppendColoredValue(stream, adapter_mvf_delay_s, adapter_mvf_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, adapter_mvf_delay_s, color_grey);
                    }
                    if (is_tsr_got)
                    {
                        AppendColoredValue(stream, tsr_delay_s, tsr_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, tsr_delay_s, color_grey);
                    }
                    if (is_tlr_got)
                    {
                        AppendColoredValue(stream, tlr_delay_s, tlr_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, tlr_delay_s, color_grey);
                    }
                    if (is_full_obj_got)
                    {
                        AppendColoredValue(stream, full_obj_delay_s, full_obj_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, full_obj_delay_s, color_grey);
                    }
                    if (is_lm_got)
                    {
                        AppendColoredValue(stream, lm_delay_s, lm_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, lm_delay_s, color_grey);
                    }
                    if (is_bev_lane_got)
                    {
                        AppendColoredValue(stream, bev_lane_delay_s, bev_lane_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, bev_lane_delay_s, color_grey);
                    }
                    if (is_cone_obj_got)
                    {
                        AppendColoredValue(stream, cone_obj_delay_s, cone_obj_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, cone_obj_delay_s, color_grey);
                    }
                    if (is_mvod_got)
                    {
                        AppendColoredValue(stream, mvod_delay_s, mvod_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, mvod_delay_s, color_grey);
                    }
                    if (is_mvfod_got)
                    {
                        AppendColoredValue(stream, mvfod_delay_s, mvfod_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, mvfod_delay_s, color_grey);
                    }
                    stream << std::endl;
                    stream << std::fixed << std::setprecision(2);
                }
                else if (is_ipu_8775)
                {
                    static float adapter_mv_freq = 0.0, adapter_mv_delay_s = 0.0;
                    static std::string adapter_mv_color = color_grey;
                    static bool is_adapter_mv_got = false;
                    static float adapter_mvf_freq = 0.0, adapter_mvf_delay_s = 0.0;
                    static std::string adapter_mvf_color = color_grey;
                    static bool is_adapter_mvf_got = false;
                    static float adapter_parking_freq = 0.0, adapter_parking_delay_s = 0.0;
                    static std::string adapter_parking_color = color_grey;
                    static bool is_adapter_parking_got = false;
                    static float mvod_freq = 0.0, mvod_delay_s = 0.0;
                    static std::string mvod_color = color_grey;
                    static bool is_mvod_got = false;
                    static float mvfod_freq = 0.0, mvfod_delay_s = 0.0;
                    static std::string mvfod_color = color_grey;
                    static bool is_mvfod_got = false;
                    static float pld_freq = 0.0, pld_delay_s = 0.0;
                    static std::string pld_color = color_grey;
                    static bool is_pld_got = false;
                    static float pmap_freq = 0.0, pmap_delay_s = 0.0;
                    static std::string pmap_color = color_grey;
                    static bool is_pmap_got = false;
                    static float superpoint_freq = 0.0, superpoint_delay_s = 0.0;
                    static std::string superpoint_color = color_grey;
                    static bool is_superpoint_got = false;

                    // mv adapter
                    if (getValue(topic_name_adapter_mv, topic_ptr_vision_adapter_mv))
                    {
                        adapter_mv_freq = getFrequency(topic_name_adapter_mv, topic_ptr_vision_adapter_mv);
                        adapter_mv_delay_s = topic_ptr_vision_adapter_mv->data / 1000000000.0;
                        is_adapter_mv_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(adapter_mv_freq), DelaySeverity(adapter_mv_delay_s));
                        adapter_mv_color = SeverityColor(sev);
                    }
                    else
                    {
                        is_adapter_mv_got = false;
                        adapter_mv_freq = -1.0;
                        adapter_mv_delay_s = -1.0;
                        adapter_mv_color = color_grey;
                    }
                    // mvf adapter
                    if (getValue(topic_name_adapter_mvf, topic_ptr_vision_adapter_mvf))
                    {
                        adapter_mvf_freq = getFrequency(topic_name_adapter_mvf, topic_ptr_vision_adapter_mvf);
                        adapter_mvf_delay_s = topic_ptr_vision_adapter_mvf->data / 1000000000.0;
                        is_adapter_mvf_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(adapter_mvf_freq), DelaySeverity(adapter_mvf_delay_s));
                        adapter_mvf_color = SeverityColor(sev);
                    }
                    else
                    {
                        is_adapter_mvf_got = false;
                        adapter_mvf_freq = -1.0;
                        adapter_mvf_delay_s = -1.0;
                        adapter_mvf_color = color_grey;
                    }

                    // parking adapter
                    if (getValue(topic_name_adapter_parking, topic_ptr_vision_adapter_parking))
                    {
                        adapter_parking_freq = getFrequency(topic_name_adapter_parking, topic_ptr_vision_adapter_parking);
                        adapter_parking_delay_s = topic_ptr_vision_adapter_parking->data / 1000000000.0;
                        is_adapter_parking_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(adapter_parking_freq), DelaySeverity(adapter_parking_delay_s));
                        adapter_parking_color = SeverityColor(sev);
                    }
                    else
                    {
                        is_adapter_parking_got = false;
                        adapter_parking_freq = -1.0;
                        adapter_parking_delay_s = -1.0;
                        adapter_parking_color = color_grey;
                    }

                    // mvod
                    if (getValue(topic_name_uniinfer_mvod, topic_ptr_vision_uniinfer_mvod))
                    {
                        mvod_freq = getFrequency(topic_name_uniinfer_mvod, topic_ptr_vision_uniinfer_mvod);
                        mvod_delay_s = topic_ptr_vision_uniinfer_mvod->data / 1000000000.0;
                        is_mvod_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(mvod_freq), DelaySeverity(mvod_delay_s));
                        mvod_color = SeverityColor(sev);
                    }
                    else
                    {
                        is_mvod_got = false;
                        mvod_freq = -1.0;
                        mvod_delay_s = -1.0;
                        mvod_color = color_grey;
                    }
                    // mvfod
                    if (getValue(topic_name_uniinfer_mvfod, topic_ptr_vision_uniinfer_mvfod))
                    {
                        mvfod_freq = getFrequency(topic_name_uniinfer_mvfod, topic_ptr_vision_uniinfer_mvfod);
                        mvfod_delay_s = topic_ptr_vision_uniinfer_mvfod->data / 1000000000.0;
                        is_mvfod_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(mvfod_freq), DelaySeverity(mvfod_delay_s));
                        mvfod_color = SeverityColor(sev);
                    }
                    else
                    {
                        is_mvfod_got = false;
                        mvfod_freq = -1.0;
                        mvfod_delay_s = -1.0;
                        mvfod_color = color_grey;
                    }
                    // pld
                    if (getValue(topic_name_uniinfer_pld, topic_ptr_vision_uniinfer_pld))
                    {
                        pld_freq = getFrequency(topic_name_uniinfer_pld, topic_ptr_vision_uniinfer_pld);
                        pld_delay_s = topic_ptr_vision_uniinfer_pld->data / 1000000000.0;
                        is_pld_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(pld_freq), DelaySeverity(pld_delay_s));
                        pld_color = SeverityColor(sev);
                    }
                    else
                    {
                        is_pld_got = false;
                        pld_freq = -1.0;
                        pld_delay_s = -1.0;
                        pld_color = color_grey;
                    }

                    // pmap
                    if (getValue(topic_name_uniinfer_pmap, topic_ptr_vision_uniinfer_pmap))
                    {
                        pmap_freq = getFrequency(topic_name_uniinfer_pmap, topic_ptr_vision_uniinfer_pmap);
                        pmap_delay_s = topic_ptr_vision_uniinfer_pmap->data / 1000000000.0;
                        is_pmap_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(pmap_freq), DelaySeverity(pmap_delay_s));
                        pmap_color = SeverityColor(sev);
                    }
                    else
                    {
                        pmap_freq = -1.0;
                        pmap_delay_s = -1.0;
                        is_pmap_got = false;
                        pmap_color = color_grey;
                    }
                    // superpoint
                    if (getValue(topic_name_uniinfer_superpoint, topic_ptr_vision_uniinfer_superpoint))
                    {
                        superpoint_freq = getFrequency(topic_name_uniinfer_superpoint, topic_ptr_vision_uniinfer_superpoint);
                        superpoint_delay_s = topic_ptr_vision_uniinfer_superpoint->data / 1000000000.0;
                        is_superpoint_got = true;
                        UniInferSeverity sev = (std::max)(FreqSeverity(superpoint_freq), DelaySeverity(superpoint_delay_s));
                        superpoint_color = SeverityColor(sev);
                    }
                    else
                    {
                        superpoint_freq = -1.0;
                        superpoint_delay_s = -1.0;
                        is_superpoint_got = false;
                        superpoint_color = color_grey;
                    }

                    stream << "Module : ";
                    if (is_adapter_mv_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "AMv" << adapter_mv_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "AMv" << color_grey << " | ";
                    }
                    if (is_adapter_mvf_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "AMvf" << adapter_mvf_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "AMvf" << color_grey << " | ";
                    }
                    if (is_adapter_parking_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "APark" << adapter_parking_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "APark" << color_grey << " | ";
                    }
                    if (is_mvod_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "MvOD" << mvod_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "MvOD" << color_grey << " | ";
                    }
                    if (is_mvfod_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "MvfOD" << mvfod_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "MvfOD" << color_grey << " | ";
                    }
                    if (is_pld_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "Pld" << pld_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "Pld" << color_grey << " | ";
                    }
                    if (is_pmap_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "Pmap" << pmap_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "Pmap" << color_grey << " | ";
                    }
                    if (is_superpoint_got)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "Sp" << superpoint_color << " | ";
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "Sp" << color_grey << " | ";
                    }
                    stream << std::endl;

                    stream << "Fqcy(hz): ";
                    if (is_adapter_mv_got)
                    {
                        int prec = (adapter_mv_freq > 10.00001) ? 1 : 2;
                        AppendColoredValue(stream, adapter_mv_freq, adapter_mv_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, adapter_mv_freq, color_grey, 2);
                    }
                    if (is_adapter_mvf_got)
                    {
                        int prec = (adapter_mvf_freq > 10.00001) ? 1 : 2;
                        AppendColoredValue(stream, adapter_mvf_freq, adapter_mvf_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, adapter_mvf_freq, color_grey, 2);
                    }
                    if (is_adapter_parking_got)
                    {
                        int prec = (adapter_parking_freq > 10.00001) ? 1 : 2;
                        AppendColoredValue(stream, adapter_parking_freq, adapter_parking_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, adapter_parking_freq, color_grey, 2);
                    }
                    if (is_mvod_got)
                    {
                        int prec = (mvod_freq > 10.00001) ? 1 : 2;
                        AppendColoredValue(stream, mvod_freq, mvod_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, mvod_freq, color_grey, 2);
                    }
                    if (is_mvfod_got)
                    {
                        int prec = (mvfod_freq > 10.00001) ? 1 : 2;
                        AppendColoredValue(stream, mvfod_freq, mvfod_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, mvfod_freq, color_grey, 2);
                    }
                    if (is_pld_got)
                    {
                        int prec = (pld_freq > 10.00001) ? 1 : 2;
                        AppendColoredValue(stream, pld_freq, pld_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, pld_freq, color_grey, 2);
                    }
                    if (is_pmap_got)
                    {
                        int prec = (pmap_freq > 10.00001) ? 1 : 2;
                        AppendColoredValue(stream, pmap_freq, pmap_color, prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, pmap_freq, color_grey, 2);
                    }
                    if (is_superpoint_got)
                    {
                        int prec = (superpoint_freq > 10.00001) ? 1 : 2;
                        AppendColoredValue(stream, superpoint_freq, (superpoint_color), prec);
                    }
                    else
                    {
                        AppendColoredValue(stream, superpoint_freq, color_grey, 2);
                    }
                    stream << std::endl;

                    stream << std::fixed << std::setprecision(2);
                    stream << "Delay(s): ";
                    if (is_adapter_mv_got)
                    {
                        AppendColoredValue(stream, adapter_mv_delay_s, adapter_mv_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, adapter_mv_delay_s, color_grey, uniinfer_delay_precision_);
                    }
                    if (is_adapter_mvf_got)
                    {
                        AppendColoredValue(stream, adapter_mvf_delay_s, adapter_mvf_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, adapter_mvf_delay_s, color_grey, uniinfer_delay_precision_);
                    }
                    if (is_adapter_parking_got)
                    {
                        AppendColoredValue(stream, adapter_parking_delay_s, adapter_parking_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, adapter_parking_delay_s, color_grey, uniinfer_delay_precision_);
                    }
                    if (is_mvod_got)
                    {
                        AppendColoredValue(stream, mvod_delay_s, mvod_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, mvod_delay_s, color_grey, uniinfer_delay_precision_);
                    }
                    if (is_mvfod_got)
                    {
                        AppendColoredValue(stream, mvfod_delay_s, mvfod_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, mvfod_delay_s, color_grey, uniinfer_delay_precision_);
                    }
                    if (is_pld_got)
                    {
                        AppendColoredValue(stream, pld_delay_s, pld_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, pld_delay_s, color_grey, uniinfer_delay_precision_);
                    }
                    if (is_pmap_got)
                    {
                        AppendColoredValue(stream, pmap_delay_s, pmap_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, pmap_delay_s, color_grey, uniinfer_delay_precision_);
                    }
                    if (is_superpoint_got)
                    {
                        AppendColoredValue(stream, superpoint_delay_s, superpoint_color, uniinfer_delay_precision_);
                    }
                    else
                    {
                        AppendColoredValue(stream, superpoint_delay_s, color_grey, uniinfer_delay_precision_);
                    }

                    stream << std::endl;
                    stream << std::fixed << std::setprecision(2);
                }
            }
        }
    }
    void AdapterDebugInfoShowNode::AlgBasicHodAdapter(std::stringstream &stream)
    {
        if (is_ptr_pnc_fsm_out_got || is_ptr_pnc_fsm_out_zero_got)
        {
            stream
                << ADD_TEXT_COLOR_LEFT()
                << static_cast<int>(topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_safety_panel.driving_safety_func_icon.tsr_spd_limit_value_kph)
                << color_red << " | "; // red

            // topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.acc_icon_color_enum
            // 0x0: None
            // 0x1: Gray
            // 0x2: Green
            // 0x3: Blue
            // 0x4: Yellow
            // 0x5: IconAction_Blue_Appear_Speed_limit_synchronized
            // 0x6: Override Flash_Blue
            static uint8_t acc_icon_flash_cnt = 0;
            static bool is_acc_blue_flash = false;
            switch (topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.acc_icon_color_enum)
            {
            case 0x0: // none dark gray
                stream << ADD_TEXT_COLOR_LEFT()
                       << "✕"
                       << color_grey;
                break;
            case 0x1: // gray
                stream << ADD_TEXT_COLOR_LEFT()
                       << static_cast<int>(topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.acc_cruise_speed_icon_km_h)
                       << color_grey;
                acc_icon_flash_cnt = 0;
                break;
            case 0x2: // green
                stream << ADD_TEXT_COLOR_LEFT()
                       << static_cast<int>(topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.acc_cruise_speed_icon_km_h)
                       << color_blue;
                acc_icon_flash_cnt = 0;
                break;
            case 0x3: // blue
                stream << ADD_TEXT_COLOR_LEFT()
                       << static_cast<int>(topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.acc_cruise_speed_icon_km_h)
                       << color_blue;
                acc_icon_flash_cnt = 0;
                break;
            case 0x4: // yellow
                stream << ADD_TEXT_COLOR_LEFT()
                       << static_cast<int>(topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.acc_cruise_speed_icon_km_h)
                       << color6;
                acc_icon_flash_cnt = 0;
                break;
            case 0x5: // IconAction_Blue_Appear_Speed_limit_synchronized
                stream << ADD_TEXT_COLOR_LEFT()
                       << static_cast<int>(topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.acc_cruise_speed_icon_km_h)
                       << color_blue;
                acc_icon_flash_cnt = 0;
                break;
            case 0x6:                                                                            // Override Flash_Blue
                (acc_icon_flash_cnt >= 250) ? (acc_icon_flash_cnt = 0) : (acc_icon_flash_cnt++); // 100ms once ++
                if (acc_icon_flash_cnt % 10 == 0)
                {
                    is_acc_blue_flash = !is_acc_blue_flash;
                }
                if (is_acc_blue_flash)
                {
                    stream << ADD_TEXT_COLOR_LEFT()
                           << static_cast<int>(topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.acc_cruise_speed_icon_km_h)
                           << color_blue;
                }
                else
                {
                    stream << ADD_TEXT_COLOR_LEFT()
                           << static_cast<int>(topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.acc_cruise_speed_icon_km_h)
                           << color_grey;
                }
                break;
            default:
                break;
            }
            stream << " | ";

            // topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.nca_icon_color_enum
            // 0x0: None
            // 0x1: Do Not Display
            // 0x2: Blue
            // 0x3: Flash_Blue
            // 0x4: TurnLeft_Blue
            // 0x5: TurnRight_Blue
            // 0x6: TOR_Red
            static bool is_nca_active = false;
            std::string nca_icon = "☮";
            std::string nac_left_turn_icon = "⇠";
            std::string nac_right_turn_icon = "⇢";
            if (topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.nca_icon_color_enum == 2 ||
                topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.nca_icon_color_enum == 3)
            {
                // Blue && Flash_Blue
                stream << ADD_TEXT_COLOR_LEFT()
                       << nca_icon << color_blue; // blue
                is_nca_active = true;
            }
            else if (topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.nca_icon_color_enum == 4)
            {
                // TurnLeft_Blue
                stream << ADD_TEXT_COLOR_LEFT()
                       << nac_left_turn_icon << color_blue; // blue
                is_nca_active = true;
            }
            else if (topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.nca_icon_color_enum == 5)
            {
                // TurnRight_Blue
                stream << ADD_TEXT_COLOR_LEFT()
                       << nac_right_turn_icon << color_blue; // blue
                is_nca_active = true;
            }
            else
            {
                is_nca_active = false;
            }

            // topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.ica_icon_color_enum
            // 0x0: None
            // 0x1: Gray
            // 0x2: Green
            // 0x3: Blue
            // 0x4: Yellow
            // 0x5: Override Flash_Blue
            // 0x6: TOR_Red
            if (!is_nca_active)
            {
                std::string ica_icon = "◎";
                static uint8_t ica_icon_flash_cnt = 0;
                static bool is_ica_blue_flash = false;
                switch (topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_icon.ica_icon_color_enum)
                {
                case 0x0: // none black
                    stream << ADD_TEXT_COLOR_LEFT() << "✕" << color_grey;
                    break;
                case 0x1: // gray
                    stream << ADD_TEXT_COLOR_LEFT() << ica_icon << color_grey;
                    break;
                case 0x2: // gren
                    stream << ADD_TEXT_COLOR_LEFT() << ica_icon << color_blue;
                    break;
                case 0x3: // blue
                    stream << ADD_TEXT_COLOR_LEFT() << ica_icon << color_blue;
                    break;
                case 0x4: // yellow
                    stream << ADD_TEXT_COLOR_LEFT() << ica_icon << color6;
                    break;
                case 0x5:                                                                            // Flash Blue
                    (ica_icon_flash_cnt >= 250) ? (ica_icon_flash_cnt = 0) : (ica_icon_flash_cnt++); // 100ms once ++
                    if (ica_icon_flash_cnt % 10 == 0)
                    {
                        is_ica_blue_flash = !is_ica_blue_flash;
                    }
                    if (is_ica_blue_flash)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << ica_icon << color_blue;
                    }
                    else
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << ica_icon << color_grey;
                    }
                    break;
                case 0x6: // TOR_Red
                    stream << ADD_TEXT_COLOR_LEFT() << ica_icon << color_red;
                    break;
                default:
                    break;
                }
            }
            stream << " | ";
        }
        else
        {
            stream << "⛔ | ⛔ | ";
        }
        if (getValue(topic_name_vehicle_status_50ms, topic_ptr_vehicle_status_50ms))
        {
            std::string leftLightStuString = "◀◀", rightLightStuString = "▶▶";
            bool leftLightStu = topic_ptr_vehicle_status_50ms->vehbody.light_sts.is_left_dircn_ind_lamp_on;
            bool rightLightStu = topic_ptr_vehicle_status_50ms->vehbody.light_sts.is_right_dircn_ind_lamp_on;
            if (leftLightStu)
            {
                stream << ADD_TEXT_COLOR_LEFT() << leftLightStuString << color6; // yellow
            }
            else
            {
                stream << ADD_TEXT_COLOR_LEFT() << leftLightStuString << color_grey; // gray
            }

            stream << " | ";

            if (rightLightStu)
            {
                stream << ADD_TEXT_COLOR_LEFT() << rightLightStuString << color6; // yellow
            }
            else
            {
                stream << ADD_TEXT_COLOR_LEFT() << rightLightStuString << color_grey; // gray
            }

            stream << " | ";
        }
        if (getValue(topic_name_vehicle_status_50ms_zero, topic_ptr_vehicle_status_50ms_zero))
        {
            std::string leftLightStuString = "◀◀", rightLightStuString = "▶▶";
            bool leftLightStu = topic_ptr_vehicle_status_50ms_zero->vehbody.light_sts.is_left_dircn_ind_lamp_on;
            bool rightLightStu = topic_ptr_vehicle_status_50ms_zero->vehbody.light_sts.is_right_dircn_ind_lamp_on;
            if (leftLightStu)
            {
                stream << ADD_TEXT_COLOR_LEFT() << leftLightStuString << color6; // yellow
            }
            else
            {
                stream << ADD_TEXT_COLOR_LEFT() << leftLightStuString << color_grey; // gray
            }

            stream << " | ";

            if (rightLightStu)
            {
                stream << ADD_TEXT_COLOR_LEFT() << rightLightStuString << color6; // yellow
            }
            else
            {
                stream << ADD_TEXT_COLOR_LEFT() << rightLightStuString << color_grey; // gray
            }

            stream << " | ";
        }

        if (is_vehicle_chassis_10ms_got || is_vehicle_chassis_10ms_zero_got)
        {
            std::string gearsString = "";
            int8_t gears = topic_ptr_vehicle_chassis_10ms_tmp->pt.gear.actv_gear_enum;

            switch (gears)
            {
            case 0:
                gearsString = "U | ";
                break;
            case 1:
                gearsString = "D | ";
                break;
            case 2:
                gearsString = "N | ";
                break;
            case 3:
                gearsString = "R | ";
                break;
            case 4:
                gearsString = "P | ";
                break;
            default:
                break;
            }
            stream << gearsString;
            stream << std::fixed << std::setprecision(0);
            stream << topic_ptr_vehicle_chassis_10ms_tmp->vehdyn.vehspd.dsp_veh_spd_kph << " km/h | ";
            // stream << std::fixed << std::setprecision(2);
            // stream << " | " << topic_ptr_vehicle_chassis_10ms_tmp->vehdyn.vehspd.vehspd_kph << " km/h | ";

            const int brkPdlPressedEnum = static_cast<int>(topic_ptr_vehicle_chassis_10ms_tmp->brksys.brkpdl.brkpdl_appd_enum);
            const float accPdlPos = topic_ptr_vehicle_chassis_10ms_tmp->pt.acclpdl.ept_accl_pos;

            if (brkPdlPressedEnum == 1)
            {
                stream << ADD_TEXT_COLOR_LEFT() << "▼" << color_red; // red
            }
            else
            {
                stream << "▼"; // red
            }

            stream << " | ";

            if (accPdlPos >= 0.0 && accPdlPos < 10.0)
            {
                stream << ADD_TEXT_COLOR_LEFT() << "▂" << color_blue; // blue;
            }
            else if (accPdlPos >= 10.0 && accPdlPos < 20.0)
            {
                stream << ADD_TEXT_COLOR_LEFT() << "▃" << color_blue; // blue;
            }
            else if (accPdlPos >= 20.0 && accPdlPos < 30.0)
            {
                stream << ADD_TEXT_COLOR_LEFT() << "▄" << color_blue; // blue;
            }
            else if (accPdlPos >= 30.0 && accPdlPos < 40.0)
            {
                stream << ADD_TEXT_COLOR_LEFT() << "▅" << color_blue; // blue;
            }
            else if (accPdlPos >= 40.0 && accPdlPos < 50.0)
            {
                stream << ADD_TEXT_COLOR_LEFT() << "▆" << color_blue; // blue;
            }
            else if (accPdlPos >= 50.0 && accPdlPos < 60.0)
            {
                stream << ADD_TEXT_COLOR_LEFT() << "▇" << color_blue; // blue;
            }
            else if (accPdlPos >= 60.0)
            {
                stream << ADD_TEXT_COLOR_LEFT() << "█" << color_blue; // blue;
            }
            else
            {
                stream << "  "; // blue
            }
            stream << " | ";
        }
    }
    // void AdapterDebugInfoShowNode::AlgAvpPlannerInDriving(std::stringstream &stream)
    // {
    //     if (getValue(topic_name_prk_trajectory_out, topic_ptr_prk_trajectory_out))
    //     {
    //         auto pnc_tra_frequency = getFrequency(topic_name_prk_trajectory_out, topic_ptr_prk_trajectory_out);
    //         if(pnc_tra_frequency> config_f_pnc_trajectory_out_normal_freq * 0.8 && pnc_tra_frequency < config_f_pnc_trajectory_out_normal_freq * 1.2 ){
    //             stream << ADD_TEXT_COLOR_LEFT() << "=== AvpPlanner | " << pnc_tra_frequency << " Hz " << " avp " << str_alg_avp_planner_version << " apa " << str_alg_apa_planner_version << "===" << color_green << std::endl;
    //         }
    //         else if(pnc_tra_frequency < config_f_pnc_trajectory_out_normal_freq * 0.5 && pnc_tra_frequency > config_f_pnc_trajectory_out_normal_freq * 1.5){
    //             stream << ADD_TEXT_COLOR_LEFT() << "=== AvpPlanner | " << pnc_tra_frequency << " Hz " << " avp " << str_alg_avp_planner_version << " apa " << str_alg_apa_planner_version << "===" << color3 << std::endl;
    //         }
    //         else{
    //             stream << ADD_TEXT_COLOR_LEFT() << "=== AvpPlanner | " << pnc_tra_frequency << " Hz " << " avp " << str_alg_avp_planner_version << " apa " << str_alg_apa_planner_version << "===" << color_orange << std::endl;
    //         }
    //         stream<< "Status:"<<static_cast<int>(topic_ptr_prk_trajectory_out->plan_node_info.plan_status)<<" | "<<"FailRes:"<<static_cast<int>(topic_ptr_prk_trajectory_out->plan_node_info.plan_failed_reason)
    //               << "|Lat|Lon|:"<<"|"<<static_cast<int>(topic_ptr_prk_trajectory_out->plan_node_info.lat_plan_status)<<"|"<<static_cast<int>(topic_ptr_prk_trajectory_out->plan_node_info.lon_plan_status)<<"| "
    //               << "FuncMode:"<<static_cast<int>(topic_ptr_prk_trajectory_out->plan_node_info.plan_function_mode)<<std::endl;
    //         // stream << "PlanStatus:" << plan_status[topic_ptr_prk_trajectory_out->plan_node_info.plan_status] << " | ";
    //         // stream << "PlanFailRes:" << plan_failed_reason[topic_ptr_prk_trajectory_out->plan_node_info.plan_failed_reason] << std::endl;
    //         // stream << "Plan Lat:" << pnc_plan_lat_state_map[topic_ptr_prk_trajectory_out->plan_node_info.lat_plan_status] << " | ";
    //         // stream << "Lon:" << pnc_plan_lon_state_map[topic_ptr_prk_trajectory_out->plan_node_info.lon_plan_status] << std::endl;
    //         // stream << "FuncMode : "<< plan_function_mode[topic_ptr_prk_trajectory_out->plan_node_info.plan_function_mode]<<std::endl;
    //     }
    //     else
    //     {
    //         stream << ADD_TEXT_COLOR_LEFT() << "=== AvpPlanner " << str_alg_avp_planner_version << "===" << color3 << std::endl;
    //     }
    // }
    void AdapterDebugInfoShowNode::AlgAvpAdapter(std::stringstream &stream)
    {
        if (! is_driving_mode())
        {
            TopicInfo topic_info;
            topic_info.head_name = "MapBuilder";
            topic_info.topic_name = topic_name_map_state;
            topic_info.node_name = "alg_map_builder";
            stream << InitTopicHeader(topic_info,topic_ptr_map_state) << std::endl;
            std::string str_alg_avp_planner_version = get_alg_node_version("alg_avp_planner");
            std::string str_alg_apa_planner_version = get_alg_node_version("alg_apa_planner");
            if(!topic_ptr_map_state){
            }
            else{
                stream << "MapPreCon :" << map_build_mode_enum[topic_ptr_map_state->map_build_precondition_sts_enum] << " | " << " MapSta :" << map_stage_enum[topic_ptr_map_state->map_stage_enum] << std::endl;
                stream << " MapBuildPre :" << static_cast<int>(topic_ptr_map_state->map_building_percent) << " | " << "Start2EndDist :"
                       << static_cast<int>(topic_ptr_map_state->start_to_end_dist_m) << std::endl;
            }
            //TO DO  instead topic_name_pnc_trajectory_out -> prk_trajectory
            TopicInfo prk_topic_info;
            prk_topic_info.head_name = "AvpPlanner";
            prk_topic_info.topic_name = topic_name_prk_trajectory_out;
            prk_topic_info.node_name = "";
            std::function<void(std::shared_ptr<planning_msgs::msg::TrajectoryOut  const> &msg, std::string& str)> func = [&str_alg_apa_planner_version, &str_alg_avp_planner_version](std::shared_ptr<planning_msgs::msg::TrajectoryOut const>& msg, std::string& str) {
                (void)msg;
                str = " avp " + str_alg_avp_planner_version + " apa " + str_alg_apa_planner_version;
            };
            stream << InitTopicHeader(prk_topic_info, topic_ptr_prk_trajectory_out, func)<<std::endl;
            if (topic_ptr_prk_trajectory_out)
            {
                stream << "PlanStatus:" << plan_status[topic_ptr_prk_trajectory_out->plan_node_info.plan_status] << " | ";
                stream << "PlanFailRes:" << plan_failed_reason[topic_ptr_prk_trajectory_out->plan_node_info.plan_failed_reason] << std::endl;
                stream << "Plan Lat:" << pnc_plan_lat_state_map[topic_ptr_prk_trajectory_out->plan_node_info.lat_plan_status] << " | ";
                stream << "Lon:" << pnc_plan_lon_state_map[topic_ptr_prk_trajectory_out->plan_node_info.lon_plan_status] << std::endl;
                stream << "CurrenStep:" << static_cast<int>(topic_ptr_prk_trajectory_out->parking_plan_info.current_step) << "|";
                stream << "TotalStep:" << static_cast<int>(topic_ptr_prk_trajectory_out->parking_plan_info.total_step) << " | ";
                stream << "CollisDis(m):" << topic_ptr_prk_trajectory_out->parking_plan_info.collision_distance_m << std::endl;
                stream << "Scene:" << plan_scene[topic_ptr_prk_trajectory_out->plan_node_info.plan_scene_enum] << " | ";
                stream << "FuncMode : "<< plan_function_mode[topic_ptr_prk_trajectory_out->plan_node_info.plan_function_mode]<<std::endl;
                stream << "CollisRes :" << collision_reason_enum[topic_ptr_prk_trajectory_out->parking_plan_info.collision_reason_enum] << std::endl;
            }
            else {
                // do nothing.
            }
            AlgHmiHmiReqAdapter(stream);
        }
    }
    void AdapterDebugInfoShowNode::AlgPncDebugAdapter(std::stringstream &stream)
    {
        std::vector<std::string> elements = {
            "Lgt_index_point",
            "Lgt_max_accl",
            "debug_lat_value_3",
            "debug_lon_value_4",
            "debug_lon_value_5",
            "debug_lon_value_6"
        };

        const std::vector<std::string> apa_elements = {
            "Apa_lgt_match_point",
            "Apa_lngc_targetVel",
            "Apa_lngc_targetAcc",
            "Apa_loc_to_wheel_chock_dis",
            "Apa_loc_to_last_point_dis",
            "Apa_wc_to_last_point_dis",
            "Hpa_StrAngReq",
            "Hpa_DstErr",
            "Hpa_AngErr",
            "Hpa_TrajFarK"
        };
        const std::vector<std::string> hpa_elements = {
            "Hpa_lgt_match_point",
            "Hpa_target_vel_",
            "Hpa_target_acc_",
            "Apa_loc_to_wheel_chock_dis",
            "Apa_loc_to_last_point_dis",
            "Apa_wc_to_last_point_dis",
            "Hpa_StrAngReq",
            "Hpa_DstErr",
            "Hpa_AngErr",
            "Hpa_TrajFarK"
        };
        const std::vector<std::string> ica_elements = {
            "Ctrl_BiasVal",
            "Ctrl_steerwheelbial_status",
            "lgt_match_point",
            "target_vel_",
            "target_acc_",
            "Ctrl_TrajFarK",
            "Ctrl_LatSts",
            "Ctrl_LatErr",
            "Ctrl_AngErr",
            "Lat_index_point",
            "Ctrl_StrAngReq",
        };
        if (config_b_pnc_debug_show)
        {
            TopicInfo topic_info;
            topic_info.head_name = "PnC Debug";
            topic_info.topic_name = topic_name_pnc_debug_out;
            stream << InitTopicHeader(topic_info,topic_ptr_pnc_debug_out) << std::endl;
            if(!topic_ptr_pnc_debug_out){
                return;
            }
            else{
                int i = 0;
                const std::vector<std::string> *tmp = nullptr;
                if (0x1 == getFsmMode())
                {
                    tmp = &ica_elements;
                }
                else if (0x3 == getFsmMode())
                {
                    tmp = &hpa_elements;
                }
                else if (0x2 == getFsmMode())
                {
                    tmp = &apa_elements;
                }
                if (tmp)
                {
                    for (const auto &one : *tmp)
                    {
                        elements.push_back(one);
                    }
                }
                // BiasVal validation check
                bool biasvalstatus = false;
                for (auto debug_info_element : topic_ptr_pnc_debug_out->debug_info)
                {
                    if (debug_info_element.signal_name == "Ctrl_steerwheelbial_status")
                    {
                        //
                        biasvalstatus = debug_info_element.value > 0.5;
                    }
                }
                for (auto debug_info_element : topic_ptr_pnc_debug_out->debug_info)
                {
                    for (const std::string &element : elements)
                    {
                        if (element.find(debug_info_element.signal_name) != std::string::npos)
                        {
                            i++;
                            if (i % 3 != 0)
                            {
                                if (biasvalstatus && ("Ctrl_BiasVal" == debug_info_element.signal_name))
                                {
                                    stream << ADD_TEXT_COLOR_LEFT() << debug_info_element.signal_name << " : " << debug_info_element.value << color_red << " | ";
                                }
                                else
                                {
                                    stream << debug_info_element.signal_name << " : " << debug_info_element.value << " | ";
                                }
                            }
                            else
                            {
                                stream << debug_info_element.signal_name << " : " << debug_info_element.value << std::endl;
                            }
                            break;
                        }
                    }
                }
                stream<<std::endl;
            }
        }
    }
    // void AdapterDebugInfoShowNode::AlgPncRouterAdapter(std::stringstream &stream)
    // {
    //     if (config_b_pnc_router_show)
    //     {
    //         bool is_valid = setFrequency(topic_name_pnc_router_out,topic_ptr_pnc_router_out,stream,str_alg_pnc_router_version);
    //         if(!is_valid){
    //             return;
    //         }
    //         else{
    //             if (0u == topic_ptr_pnc_router_out->router_node_info.router_status)
    //             {
    //                 stream << ADD_TEXT_COLOR_LEFT() << "Router Status : " << pnc_router_status_map[topic_ptr_pnc_router_out->router_node_info.router_status] << color_green << std::endl;
    //             }
    //             else
    //             {
    //                 stream << ADD_TEXT_COLOR_LEFT() << "Router Status : " << pnc_router_status_map[topic_ptr_pnc_router_out->router_node_info.router_status] << color3 << std::endl;
    //             }
    //             for (const auto &path : topic_ptr_pnc_router_out->paths)
    //             {
    //                 if (path.is_router_path)
    //                 {
    //                     stream << "Cur ID : " << static_cast<int>(path.currentlane_id) << " Tar ID : " << static_cast<int>(path.targetlane_id)
    //                            << " | PathRemainDist : " << path.remain_distance_m << " m " << std::endl;
    //                     stream << "ChangeType : ";
    //                     for (const auto &t : path.change_type_enum_list)
    //                     {
    //                         stream << pnc_router_change_type_map[t] << " | ";
    //                     }
    //                     stream << std::endl;
    //                     stream << "LaneRemainDist : ";
    //                     for (size_t i = 0; i < path.lane_info.size(); i++)
    //                     {
    //                         stream << i << " " << path.lane_info[i].remaining_distance_m << " m | ";
    //                     }
    //                     stream << std::endl;
    //                     stream << "id/score : ";
    //                     for (size_t i = 0; i < path.lane_score.size(); i++)
    //                     {
    //                         stream << path.lane_score[i].lane_id << "/" << path.lane_score[i].lane_score << " | ";
    //                     }
    //                     stream << std::endl;
    //                 }
    //             }
    //         }
    //     }
    // }
    void AdapterDebugInfoShowNode::AlgPncControlAdapter(std::stringstream &stream)
    {
        std::shared_ptr<control_msgs::msg::ControlOut const> topic_ptr_pnc_control_out_tmp;
        TopicInfo topic_info;
        topic_info.head_name = "PnC Control";
        topic_info.topic_name = topic_name_pnc_control_out;
        topic_info.node_name = "alg_pnc_control";
        stream << InitTopicHeader(topic_info,topic_ptr_pnc_control_out_tmp) << std::endl;
        if(config_b_pnc_control_show){
            if(!topic_ptr_pnc_control_out_tmp){
                return;
            }
            else{
                if (3u == topic_ptr_pnc_control_out_tmp->control_node_info.control_status)
                {
                    stream << ADD_TEXT_COLOR_LEFT() << "Control Status :" << pnc_control_status_map[topic_ptr_pnc_control_out_tmp->control_node_info.control_status] << color_green << std::endl;
                }
                else if (4u == topic_ptr_pnc_control_out_tmp->control_node_info.control_status)
                {
                    stream << ADD_TEXT_COLOR_LEFT() << "Control Status :" << pnc_control_status_map[topic_ptr_pnc_control_out_tmp->control_node_info.control_status] << color3 << std::endl;
                }
                else
                {
                    stream << ADD_TEXT_COLOR_LEFT() << "Control Status :" << pnc_control_status_map[topic_ptr_pnc_control_out_tmp->control_node_info.control_status] << ADD_TEXT_COLOR_RIGHT(1.0, 1.0, 1.0, 1.0) << std::endl;
                }
                stream << "TorqueReq : " << topic_ptr_pnc_control_out_tmp->long_signal.vcu_act_toq_req_nm << " N*m ";
                if (topic_ptr_pnc_control_out_tmp->long_signal.brk_req_val_m_s2 < -1)
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " | BrkReq : " << topic_ptr_pnc_control_out_tmp->long_signal.brk_req_val_m_s2 << " m/s2 | DcclReq : " << topic_ptr_pnc_control_out_tmp->long_signal.dccl_req_val_m_s2 << ADD_TEXT_COLOR_RIGHT(1.0, 1.0, 1.0, 1.0) << std::endl;
                }
                else
                {
                    stream << " | BrkReq : " << topic_ptr_pnc_control_out_tmp->long_signal.brk_req_val_m_s2 << " m/s2 | DcclReq : " << topic_ptr_pnc_control_out_tmp->long_signal.dccl_req_val_m_s2 << std::endl;
                }
                if (!is_driving_mode())
                {
                    auto enum_to_string = [](uint8_t value)
                    {
                        std::string str;
                        switch (value)
                        {
                        case 0x0:
                        {
                            str = "0 Normal";
                        }
                        break;
                        case 0x1:
                        {
                            str = "1 Block";
                        }
                        break;
                        case 0x2:
                        {
                            str = "2 SteerWheel0";
                        }
                        break;
                        case 0x3:
                        {
                            str = "3 PGear";
                        }
                        break;
                        default:
                            str = std::to_string(int(value)) + " NotSupport";
                        }
                        return str;
                    };
                    stream << "PrkCtrlSts : " << enum_to_string(topic_ptr_pnc_control_out_tmp->control_node_info.parking_ctrl_sts_enum) << std::endl;
                }
            }
        }
    }
    void AdapterDebugInfoShowNode::AlgPnCTrajectoryAdapter(std::stringstream &stream)
    {
        static bool is_pnc_planner_got = false;
        static bool is_pnc_planner_zero_got = false;
        std::shared_ptr<planning_msgs::msg::TrajectoryOut const> topic_ptr_pnc_trajectory_out_tmp;
        float pnc_planner_frequency = 0.0;
        if (config_b_pnc_planner_show)
        {
            TopicInfo topic_info;
            topic_info.head_name = "PnC Trajectory";
            topic_info.topic_name = topic_name_pnc_trajectory_out;
            topic_info.node_name  = "alg_pnc_planner";
            stream << InitTopicHeader(topic_info,topic_ptr_pnc_trajectory_out_tmp) << std::endl;
            if (topic_ptr_pnc_trajectory_out_tmp)
            {
                if (1u == topic_ptr_pnc_trajectory_out_tmp->plan_node_info.plan_status)
                {
                    stream << ADD_TEXT_COLOR_LEFT() << "Plan Status : " << pnc_plan_status_map[topic_ptr_pnc_trajectory_out_tmp->plan_node_info.plan_status] << color_green << std::endl;
                }
                else if (2u == topic_ptr_pnc_trajectory_out_tmp->plan_node_info.plan_status || 3u == topic_ptr_pnc_trajectory_out_tmp->plan_node_info.plan_status)
                {
                    stream << ADD_TEXT_COLOR_LEFT() << "Plan Status : " << pnc_plan_status_map[topic_ptr_pnc_trajectory_out_tmp->plan_node_info.plan_status] << color3 << std::endl;
                }
                else
                {
                    stream << ADD_TEXT_COLOR_LEFT() << "Plan Status : " << pnc_plan_status_map[topic_ptr_pnc_trajectory_out_tmp->plan_node_info.plan_status] << ADD_TEXT_COLOR_RIGHT(1.0, 1.0, 1.0, 1.0) << std::endl;
                }
                AlgPncDebugPlanOutAdapter(stream);
                stream << "PrimaryDecision : " << pnc_trajectory_primary_decision_map[topic_ptr_pnc_trajectory_out_tmp->id_result.primary_decision_enum]
                       << " | target_id : " << topic_ptr_pnc_trajectory_out_tmp->id_result.primary_target_id << std::endl;
                stream << "Plan fail rea : " << pnc_plan_fail_reason_map[topic_ptr_pnc_trajectory_out_tmp->plan_node_info.plan_failed_reason]
                       << " | Lat state : " << pnc_plan_lat_state_map[topic_ptr_pnc_trajectory_out_tmp->plan_node_info.lat_plan_status]
                       << " | Lon state : " << pnc_plan_lon_state_map[topic_ptr_pnc_trajectory_out_tmp->plan_node_info.lon_plan_status] << std::endl;
                // stream << "ID :" << "StartLane : " << topic_ptr_pnc_trajectory_out_tmp->id_result.start_lane_id
                stream << "SideFollow :" << static_cast<int>(topic_ptr_pnc_trajectory_out_tmp->id_result.side_follow_id)
                       << " | Cutin :" << static_cast<int>(topic_ptr_pnc_trajectory_out_tmp->id_result.cutin_id) 
                       << " | CutinProb :" << topic_ptr_pnc_trajectory_out_tmp->id_result.cutin_probability << std::endl;
                stream << "TgtSpdReason :" << static_cast<int>(topic_ptr_pnc_trajectory_out_tmp->target_speed_reason_enum)
                       << " | TgtSpd :" << topic_ptr_pnc_trajectory_out_tmp->target_speed_m_s << "m/s" << std::endl;
                stream << "TLtStatus : " << turn_light_status[topic_ptr_pnc_trajectory_out_tmp->plan_node_info.turn_light_status]
                       << " | LatAviodObj : " << topic_ptr_pnc_trajectory_out_tmp->plan_node_info.lat_avoid_obj_id << std::endl;
                stream << "TorSys : " << topic_ptr_pnc_trajectory_out_tmp->plan_node_info.tor_reason_sys_bits
                       << " | TorScene : " << topic_ptr_pnc_trajectory_out_tmp->plan_node_info.tor_reason_scene_bits << std::endl;
                // speed debug info
                static const auto brake_light_info = std::make_pair("is_brake_light", " BrakeLight");
                static const auto cipv_info = std::make_pair("cipv_v", " | CipvV");
                static const auto safes_info = std::make_pair("safe_s", " | SafeS");
                if(getValue(topic_name_pnc_debug_plan_out, topic_ptr_pnc_debug_plan_out)) {
                    bool is_show = false;
                    const auto& msg = topic_ptr_pnc_debug_plan_out;
                    std::unordered_map<std::string, float> signals;
                    for(const auto& one : msg->debug_plan_info) {
                        signals.emplace(one.signal_name, one.value);
                    }
                    if(signals.count(brake_light_info.first)){
                        stream << brake_light_info.second << " : " << signals[brake_light_info.first];
                    }
                    if(signals.count(cipv_info.first)){
                        stream << cipv_info.second << " : " << signals[cipv_info.first];
                    }
                    if(signals.count(safes_info.first)){
                        stream << safes_info.second << " : " << signals[safes_info.first] << std::endl;
                    }
                    if(signals.count("left_flow_speed") && signals["left_flow_speed"] > 1e-3){
                        stream << "LflowSpd : " << signals["left_flow_speed"];
                        is_show = true;
                    }
                    if(signals.count("right_flow_speed") && signals["right_flow_speed"] > 1e-3){
                        stream << " | RflowSpd : " << signals["right_flow_speed"];
                        is_show = true;
                    }
                    if(signals.count("is_ref_a_sampling") && signals["is_ref_a_sampling"]){
                        if(signals.count("a_ref_min")){
                            stream << " | ARefMin : " << signals["a_ref_min"];
                            is_show = true;
                        }
                        if(signals.count("is_extend_stops")){
                            stream << " | EStopS : " << signals["is_extend_stops"];
                            is_show = true;
                        }
                    }
                    if(is_show){
                        stream << std::endl;
                    }
                }
            }
        }
    }
    void AdapterDebugInfoShowNode::AlgPncDeciderAdapter(std::stringstream &stream)
    {
        std::shared_ptr<planning_msgs::msg::DeciderResult const> topic_ptr_pnc_decider_result_tmp;
        TopicInfo topic_info;
        topic_info.head_name = "PnC Decider";
        topic_info.topic_name = topic_name_pnc_decider_result;
        topic_info.node_name = "alg_pnc_decider";
        stream << InitTopicHeader(topic_info,topic_ptr_pnc_decider_result_tmp) << std::endl;
        if(config_b_pnc_decider_show){
            if(!topic_ptr_pnc_decider_result_tmp){
                return;
            }
            else{
                if (0x3u == topic_ptr_pnc_decider_result_tmp->decider_node_info.decider_status_enum)
                { // Running # 有参考线且fsm active
                    stream << ADD_TEXT_COLOR_LEFT() << "Decider Status : " << pnc_decider_status_map[topic_ptr_pnc_decider_result_tmp->decider_node_info.decider_status_enum] << color_green << std::endl;
                }
                else if (0x4u == topic_ptr_pnc_decider_result_tmp->decider_node_info.decider_status_enum || 0x5u == topic_ptr_pnc_decider_result_tmp->decider_node_info.decider_status_enum)
                { // Abort # 决策失败需要退出
                    stream << ADD_TEXT_COLOR_LEFT() << "Decider Status : " << pnc_decider_status_map[topic_ptr_pnc_decider_result_tmp->decider_node_info.decider_status_enum] << color3 << std::endl;
                }
                else
                {
                    stream << ADD_TEXT_COLOR_LEFT() << "Decider Status : " << pnc_decider_status_map[topic_ptr_pnc_decider_result_tmp->decider_node_info.decider_status_enum] << ADD_TEXT_COLOR_RIGHT(1.0, 1.0, 1.0, 1.0) << std::endl;
                }
                stream << "Func Mode : " << pnc_decider_function_mode_map[topic_ptr_pnc_decider_result_tmp->decider_node_info.decider_function_mode_enum];
                stream << " | Acc : " << pnc_decider_acc_inhibit_reason_map[topic_ptr_pnc_decider_result_tmp->decider_node_info.acc_inhibited_reason_enum];
                stream << " | Ica : " << pnc_decider_ica_inhibit_reason_map[topic_ptr_pnc_decider_result_tmp->decider_node_info.ica_inhibited_reason_enum];
                stream << " | ms : " << pnc_decider_macro_scene_enum[topic_ptr_pnc_decider_result_tmp->sd_result.macro_scene_enum];
                stream << " | Modal : ";
                for (const auto candidate : topic_ptr_pnc_decider_result_tmp->bd_result.maneuver_candidates) {
                    stream << pnc_decider_detailed_maneuver_map[candidate.detailed_maneuver_enum] << "/"
                           << candidate.maneuver_rating << " ";
                }
                stream << std::endl;
                stream << "SftRsn : " << pnc_decider_shift_reason_map[topic_ptr_pnc_decider_result_tmp->bd_result.ego_shift_decider.shift_reason_enum];
                stream << " | Val : " << topic_ptr_pnc_decider_result_tmp->bd_result.ego_shift_decider.shift_value_m << " m";
                stream << " | Dir : " << pnc_decider_shift_direction_map[topic_ptr_pnc_decider_result_tmp->bd_result.ego_shift_decider.shift_direction_enum];
                float ego_sft_val = 0.0, target_sft_val = 0.0;
                uint8_t ego_opt_status = 0, target_opt_status = 0, sample_status = 0;
                for (size_t i = 0; i < topic_ptr_pnc_decider_result_tmp->rd_result.reference_lines_list.size(); i++) {
                    const auto &ref = topic_ptr_pnc_decider_result_tmp->rd_result.reference_lines_list[i];
                    if (ref.refline_position_enum == 0 && ego_sft_val == 0.0) {
                        ego_sft_val = ref.ego_sample_offset_m;
                        ego_opt_status = ref.ego_refline_sqp_status_enum;
                    }
                    if (ref.ref_line_type_enum == 2 && target_sft_val == 0.0) {
                        target_sft_val = ref.target_sample_offset_m;
                        target_opt_status = ref.target_refline_sqp_status_enum;
                        sample_status = ref.ref_sample_status_enum;
                    }
                }
                stream << " | RespE: " << ego_sft_val << " m, T: " << target_sft_val << " m" << std::endl;
                stream << "Scene : " << pnc_decider_scene_map[topic_ptr_pnc_decider_result_tmp->sd_result.scene_enum];
                stream << " | Tgt: " << pnc_decider_refline_opt_status_map[target_opt_status] << ", Samp: " << pnc_decider_refline_opt_status_map[sample_status];
                stream << " | LS : " << static_cast<uint16_t>(topic_ptr_pnc_decider_result_tmp->bd_result.lane_merge_decider.lane_merge_target_gap_front_obj_id) << "E";
                stream << " | " << pnc_decider_lane_select_reason_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_merge_decider.lane_merge_target_gap_rear_obj_id] << std::endl;
                switch (topic_ptr_pnc_decider_result_tmp->sd_result.scene_enum)
                {
                case 0U: //
                    break;
                case 1U: // 0x1: LANE_FOLLOW
                {
                    stream << "LFType : " << pnc_decider_lane_follow_type_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_follow_decider.lane_follow_type_enum];
                    int prepare_reason = static_cast<int>(topic_ptr_pnc_decider_result_tmp->bd_result.lane_change_decider.lane_change_prepare_reason_enum);
                    std::string dir = prepare_reason < 32 ? "L " : "R ";
                    if (prepare_reason >= 32) {
                        prepare_reason -= 32;
                    }
                    stream << " | Prep Reason : " << dir << " " << pnc_decider_lane_change_prepare_reason_map[prepare_reason] << std::endl;
                    stream << "LC Src : " << pnc_decider_lane_change_source_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_change_decider.lane_change_source_enum];
                    stream << " | Dir : " << pnc_decider_lane_change_dir_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_change_decider.lane_change_direction_enum];
                    stream << " | Stage : " << pnc_decider_lane_change_stage_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_change_decider.lane_change_stage_enum];
                    stream << " | Gap : [ " << topic_ptr_pnc_decider_result_tmp->bd_result.lane_change_decider.lane_change_target_gap_rear_obj_id
                           << ", " << topic_ptr_pnc_decider_result_tmp->bd_result.lane_change_decider.lane_change_target_gap_front_obj_id << " ]" << std::endl;
                }
                break;
                case 2U: // 0x2: LANE_CHANGE
                    stream << "LC Src : " << pnc_decider_lane_change_source_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_change_decider.lane_change_source_enum];
                    stream << " | Dir : " << pnc_decider_lane_change_dir_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_change_decider.lane_change_direction_enum];
                    stream << " | Gap : [ " << topic_ptr_pnc_decider_result_tmp->bd_result.lane_change_decider.lane_change_target_gap_rear_obj_id
                           << ", " << topic_ptr_pnc_decider_result_tmp->bd_result.lane_change_decider.lane_change_target_gap_front_obj_id << " ]" << std::endl;
                    stream << "LC Stage : " << pnc_decider_lane_change_stage_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_change_decider.lane_change_stage_enum];
                    stream << " | Reason : " << pnc_decider_lane_change_cancel_reason_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_change_decider.lane_change_cancel_reason_enum] << std::endl;
                    break;
                case 3U: // 0x3: MERGE
                    stream << "MergeType : " << pnc_decider_lane_merge_type_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_merge_decider.lane_merge_type_enum];
                    stream << " | Stage : " << pnc_decider_lane_merge_stage_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_merge_decider.lane_merge_stage_enum] << std::endl;
                    // stream << "Merge Sts : " << pnc_decider_lane_merge_status_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_merge_decider.lane_merge_status_enum] << std::endl;
                    // stream << "Merge Start Point [" << topic_ptr_pnc_decider_result_tmp->bd_result.lane_merge_decider.lane_merge_start_point.x_m << ", "
                    //                                                 << topic_ptr_pnc_decider_result_tmp->bd_result.lane_merge_decider.lane_merge_start_point.y_m << "] Dist : "
                    //                                                 << topic_ptr_pnc_decider_result_tmp->bd_result.lane_merge_decider.lane_merge_start_dist_m << " m " << std::endl;
                    // stream << "Merge End Point [" << topic_ptr_pnc_decider_result_tmp->bd_result.lane_merge_decider.lane_merge_end_point.x_m << ", "
                    //                                                 << topic_ptr_pnc_decider_result_tmp->bd_result.lane_merge_decider.lane_merge_end_point.y_m << "] Dist : "
                    //                                                 << topic_ptr_pnc_decider_result_tmp->bd_result.lane_merge_decider.lane_merge_end_dist_m << " m " << std::endl;
                    break;
                case 4U: // 0x4: SPLIT
                    stream << "SplitType : " << pnc_decider_lane_split_type_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_split_decider.lane_split_type_enum];
                    stream << " | Stage : " << pnc_decider_lane_split_stage_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_split_decider.lane_split_stage_enum] << std::endl;
                    // stream << "SplitSts : " << pnc_decider_lane_split_status_map[topic_ptr_pnc_decider_result_tmp->bd_result.lane_split_decider.lane_split_status_enum] << std::endl;
                    // stream << "Split Start Point [" << topic_ptr_pnc_decider_result_tmp->bd_result.lane_split_decider.lane_split_start_point.x_m << ", "
                    //                                                 << topic_ptr_pnc_decider_result_tmp->bd_result.lane_split_decider.lane_split_start_point.y_m << "] Dist : "
                    //                                                 << topic_ptr_pnc_decider_result_tmp->bd_result.lane_split_decider.lane_split_start_dist_m << " m " << std::endl;
                    // stream << "Split End Point [" << topic_ptr_pnc_decider_result_tmp->bd_result.lane_split_decider.lane_split_end_point.x_m << ", "
                    //                                                 << topic_ptr_pnc_decider_result_tmp->bd_result.lane_split_decider.lane_split_end_point.y_m << "] Dist : "
                    //                                                 << topic_ptr_pnc_decider_result_tmp->bd_result.lane_split_decider.lane_split_end_dist_m << " m " << std::endl;
                    // stream << std::endl;
                    break;
                default:
                    break;
                }
                // Junction Info
                stream << "Light : " << pnc_decider_traffic_light_status_map[topic_ptr_pnc_decider_result_tmp->bd_result.traffic_junction_decider.traffic_light_status_enum];
                stream << " | Color : " << pnc_decider_traffic_light_color_map[topic_ptr_pnc_decider_result_tmp->bd_result.traffic_junction_decider.traffic_light_color_enum];
                stream << " | CntD : " << topic_ptr_pnc_decider_result_tmp->bd_result.traffic_junction_decider.traffic_light_countdown_s;
                stream << " | Sts : " << static_cast<int>(topic_ptr_pnc_decider_result_tmp->bd_result.traffic_junction_decider.traffic_light_countdown_status_enum) << std::endl;
                // GaoQiong20241125: temp comment countdown status since no HD Traffic light recently
                // stream << "TrafcLightCntDownSts : " << pnc_decider_traffic_light_countdown_status_map[topic_ptr_pnc_decider_result_tmp->bd_result.traffic_junction_decider.traffic_light_countdown_status_enum];
                // stream << " | Time : " << static_cast<int>(topic_ptr_pnc_decider_result_tmp->bd_result.traffic_junction_decider.traffic_light_countdown_status_enum) << " s" << std::endl;
                stream << " Stop : " << static_cast<int>(topic_ptr_pnc_decider_result_tmp->bd_result.traffic_junction_decider.stopline_dccl_enum);
                stream << " | JuncDir : " << pnc_decider_traffic_light_dir_map[topic_ptr_pnc_decider_result_tmp->bd_result.traffic_junction_decider.traffic_junction_direction_enum];
                stream << " | StopX : " << topic_ptr_pnc_decider_result_tmp->bd_result.traffic_junction_decider.stop_line_position_m.x_m << " m ";
                if (topic_ptr_pnc_decider_result_tmp->bd_result.traffic_junction_decider.junction_pass_stage_enum == 9U)
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " | Stage : " << pnc_decider_junction_pass_stage_map[topic_ptr_pnc_decider_result_tmp->bd_result.traffic_junction_decider.junction_pass_stage_enum] << color_blue<< std::endl;
                }
                else
                {
                    stream << " | Stage : " << pnc_decider_junction_pass_stage_map[topic_ptr_pnc_decider_result_tmp->bd_result.traffic_junction_decider.junction_pass_stage_enum] << std::endl;
                }
                // Speed Limit
                if (!topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.upper_set_speed_limit_kph_list.empty())
                {
                    stream << "SpdUp : Set " << static_cast<int>(topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.upper_set_speed_limit_kph_list[0]);
                }
                if (!topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.upper_tsr_speed_limit_kph_list.empty())
                {
                    stream << " TSR " << static_cast<int>(topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.upper_tsr_speed_limit_kph_list[0]);
                }
                // if (!topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.lower_set_speed_limit_kph_list.empty()) {
                //     stream << "| Low : Set " << static_cast<int>(topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.lower_set_speed_limit_kph_list[0]);
                // }
                // if (!topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.lower_tsr_speed_limit_kph_list.empty()) {
                //     stream << " TSR " <<  static_cast<int>(topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.lower_tsr_speed_limit_kph_list[0]);
                // }
                // stream << " kph " << std::endl;
                if (!topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.upper_set_speed_limit_change_dist_m_list.empty())
                {
                    stream << " | DistUp : Set " << static_cast<int>(topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.upper_set_speed_limit_change_dist_m_list[0]);
                }
                if (!topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.upper_tsr_speed_limit_change_dist_m_list.empty())
                {
                    stream << " TSR " << static_cast<int>(topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.upper_tsr_speed_limit_change_dist_m_list[0]);
                }
                stream << " FLOW "
                       << static_cast<int>(
                              topic_ptr_pnc_decider_result_tmp->decider_node_info.avp_inhibited_reason_enum);
                // if (!topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.lower_set_speed_limit_change_dist_m_list.empty()) {
                //     stream << " | Low : Set " << static_cast<int>(topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.lower_set_speed_limit_change_dist_m_list[0]);
                // }
                // if (!topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.lower_tsr_speed_limit_change_dist_m_list.empty()) {
                //     stream << " TSR " <<  static_cast<int>(topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.lower_tsr_speed_limit_change_dist_m_list[0]) << " m ";
                // }
                // stream << " m " << std::endl;
                stream << std::endl;
                stream <<"SpdAdj : " << static_cast<int>(topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.speed_adjust_value_kph) << " kph" << " | Recmd : " << pnc_decider_speed_adjust_recommend_map[topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.speed_adjust_recommend_enum];
                stream << " | Dist: " << topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.speed_adjust_dist_m;
                stream << " | Rsn : " << pnc_decider_speed_adjust_reason_map[topic_ptr_pnc_decider_result_tmp->bd_result.speed_limit_decider.speed_adjust_reason_enum] << std::endl;
                if (topic_ptr_pnc_decider_result_tmp->rd_result.is_valid_refline)
                {
                    stream << "RefNum : " << static_cast<int>(topic_ptr_pnc_decider_result_tmp->rd_result.reference_line_num);
                    bool is_found = false;
                    for (size_t i = 0; i < topic_ptr_pnc_decider_result_tmp->rd_result.reference_line_num; i++)
                    {
                        const auto &ref = topic_ptr_pnc_decider_result_tmp->rd_result.reference_lines_list[i];
                        if (ref.ref_line_type_enum == 2)
                        {
                            // 0x0: Unknown
                            // 0x1: Regular Type
                            // 0x2: Target Refline
                            stream << " | TgtPos : " << pnc_decider_refline_position_map[ref.refline_position_enum];
                            is_found = true;
                            break;
                        }
                    }
                    if (!is_found)
                    {
                        for (size_t i = 0; i < topic_ptr_pnc_decider_result_tmp->rd_result.reference_line_num; i++)
                        {
                            const auto &ref = topic_ptr_pnc_decider_result_tmp->rd_result.reference_lines_list[i];
                            if (ref.refline_position_enum == 0)
                            {
                                stream << " | EgoPos : " << pnc_decider_refline_position_map[ref.refline_position_enum];
                                is_found = true;
                                break;
                            }
                        }
                    }
                    if (!is_found)
                    {
                        stream << std::endl;
                    }
                    stream << " | Mod:";
                    for (const auto & iter: topic_ptr_pnc_decider_result_tmp->rd_result.effective_target_list) {
                        stream << " | " << pnc_decider_primary_maneuver_map[iter.interaction_modal_enum];
                    }
                    stream << std::endl;
                }
                else
                {
                    stream << std::endl;
                }
                if (!topic_ptr_pnc_decider_result_tmp->rd_result.reference_lines_list.empty()) {
                    const auto & error_vect = topic_ptr_pnc_decider_result_tmp->rd_result.reference_lines_list.front()
                                                  .left_soft_boundary_pts_list.boundary_dist_to_center_line_m_list;
                    if (error_vect.size() >= 9) {
                        stream << "RefSE: " << error_vect[0] * 100.0 << ", " << error_vect[1] * 100.0 << " | RefME: "
                               << error_vect[3] * 100.0 << ", " << error_vect[4] * 100.0 << " | InputME: "
                               << error_vect[6] * 100.0 << ", " << error_vect[7] * 100.0 << std::endl;
                    }
                }
            }
        }
    }
    void AdapterDebugInfoShowNode::AlgPncInferAdapter(std::stringstream &stream)
    {
        std::shared_ptr<planning_msgs::msg::PncMap const> topic_ptr_pnc_infer_result_tmp;
        if (is_driving_mode())
        {
            TopicInfo topic_info;
            topic_info.head_name = "PnC Infer";
            topic_info.topic_name = topic_name_pnc_infer_result;
            topic_info.node_name = "alg_pnc_infer";
            stream << InitTopicHeader(topic_info,topic_ptr_pnc_infer_result_tmp) << std::endl;
            if (config_b_pnc_infer_show)
            {
                if(!topic_ptr_pnc_infer_result_tmp){
                    return;
                }
                else{
                    if (0x0u == topic_ptr_pnc_infer_result_tmp->pnc_map_node_info.pnc_map_status_detail_enum)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "Pipline : " << pnc_infer_status_map[topic_ptr_pnc_infer_result_tmp->pnc_map_node_info.pnc_map_status_detail_enum] << color3<< " | ";
                    }
                    else if (0x1u == topic_ptr_pnc_infer_result_tmp->pnc_map_node_info.pnc_map_status_detail_enum)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "Pipline: " << pnc_infer_status_map[topic_ptr_pnc_infer_result_tmp->pnc_map_node_info.pnc_map_status_detail_enum] << color_green << " | ";
                    }
                    else if (0x2u == topic_ptr_pnc_infer_result_tmp->pnc_map_node_info.pnc_map_status_detail_enum) {
                        stream << ADD_TEXT_COLOR_LEFT() << "Pipline: " << pnc_infer_status_map[topic_ptr_pnc_infer_result_tmp->pnc_map_node_info.pnc_map_status_detail_enum] << color_green << " | ";
                    }
                    else if (0x3u == topic_ptr_pnc_infer_result_tmp->pnc_map_node_info.pnc_map_status_detail_enum) {
                        stream << ADD_TEXT_COLOR_LEFT() << "Pipline: " << pnc_infer_status_map[topic_ptr_pnc_infer_result_tmp->pnc_map_node_info.pnc_map_status_detail_enum] << color_green << " | ";
                    }
                    else if (0x4u == topic_ptr_pnc_infer_result_tmp->pnc_map_node_info.pnc_map_status_detail_enum) {
                        stream << ADD_TEXT_COLOR_LEFT() << "Pipline: " << pnc_infer_status_map[topic_ptr_pnc_infer_result_tmp->pnc_map_node_info.pnc_map_status_detail_enum] << color_green << " | ";
                    }
                    else if (0x5u == topic_ptr_pnc_infer_result_tmp->pnc_map_node_info.pnc_map_status_detail_enum) {
                        stream << ADD_TEXT_COLOR_LEFT() << "Pipline: " << pnc_infer_status_map[topic_ptr_pnc_infer_result_tmp->pnc_map_node_info.pnc_map_status_detail_enum] << color_green << " | ";
                    } else if (0x6u == topic_ptr_pnc_infer_result_tmp->pnc_map_node_info.pnc_map_status_detail_enum) {
                        stream << ADD_TEXT_COLOR_LEFT() << "Pipline: " << pnc_infer_status_map[topic_ptr_pnc_infer_result_tmp->pnc_map_node_info.pnc_map_status_detail_enum] << color_green << " | ";
                    } else {
                        stream << ADD_TEXT_COLOR_LEFT() << "Pipline: ?" << color_green << " | ";
                    }
                    stream
                        << "EgoStat: "
                        << pnc_infer_ego_sts
                               [topic_ptr_pnc_infer_result_tmp
                                    ->pnc_map_node_info.ego_lane_status]
                        << " | ";
                    stream
                        << "JuncAct: "
                        << pnc_infer_junction_active
                               [topic_ptr_pnc_infer_result_tmp->pnc_junc_info.is_junction_active]
                        << " | ";
                    stream
                        << "Src: "
                        << pnc_infer_junction_source
                               [topic_ptr_pnc_infer_result_tmp->pnc_junc_info.junction_active_src_enum]
                        << std::endl;
                    stream
                        << "Stage: "
                        << pnc_infer_stage[topic_ptr_pnc_infer_result_tmp
                                               ->pnc_junc_info.junction_stage_enum]
                        << " | ";
                    stream
                        << topic_ptr_pnc_infer_result_tmp->pnc_junc_info.dist_to_junction_m
                        << "m | ";
                    stream
                        << "Dir: "
                        << static_cast<int>(topic_ptr_pnc_infer_result_tmp->pnc_route_info.reverse_1)
                        << "|"
                        << pnc_infer_direction[topic_ptr_pnc_infer_result_tmp
                                               ->pnc_junc_info.junction_direction_enum]
                        << " | ";
                    stream
                        << "Vld: "
                        << static_cast<int>(topic_ptr_pnc_infer_result_tmp->pnc_junc_info.is_junction_refline_vld)
                        << " | ";
                    stream
                        << "TfcLgt: "
                        << static_cast<int>(topic_ptr_pnc_infer_result_tmp->pnc_junc_info.has_junction_traffic_light)
                        << " | ";
                    stream
                        << "Wait: "
                        << static_cast<int>(topic_ptr_pnc_infer_result_tmp->pnc_junc_info.is_in_waiting)
                        << std::endl;

                    // 4
                    stream
                        << "RT Node: "
                        << router_status_enum[topic_ptr_pnc_infer_result_tmp
                                                  ->pnc_map_node_info
                                                  .router_status_enum]
                        << " | ";
                    stream
                        << "FailReas: "
                        << router_failed_reason_enum
                               [topic_ptr_pnc_infer_result_tmp
                                    ->pnc_map_node_info
                                    .router_failed_reason_enum]
                        << " | ";
                    int ego_id = -1, left_id = -1, right_id = -1;
                    int ego_idx = -1, left_idx = -1, right_idx = -1;
                    for (size_t i = 0;
                         i < topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list_size;
                         ++i)
                    {
                        if (topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list[i]
                                .position_enum == 1)
                        {
                            ego_id =
                                topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list[i]
                                    .lane_id;
                            ego_idx = i;
                        }
                        if (topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list[i]
                                .position_enum == 2)
                        {
                            left_id =
                                topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list[i]
                                    .lane_id;
                            left_idx = i;
                        }
                        if (topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list[i]
                                .position_enum == 7)
                        {
                            right_id =
                                topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list[i]
                                    .lane_id;
                            right_idx = i;
                        }
                    }
                    stream << "id: " << left_id << "L|"
                           << ego_id << "E|" << right_id
                           << "R" << std::endl;

                    // 5
                    if (ego_idx != -1)
                    {
                        const auto &ego_lane =
                            topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list[ego_idx];
                        int j = 0;
                        stream
                            << "SpeedSrc: "
                            << speed_limit_source_enum
                                   [ego_lane.speed_limit_source_enum]
                            << " | ";
                        stream << "[";
                        for (auto maxspeed : ego_lane.max_speed_kph_list)
                        {   
                            if (j >= 2) {
                                break;
                            }
                            stream << maxspeed;
                            if (j + 1 == ego_lane.max_speed_kph_list.size())
                            {
                            }
                            else
                            {
                                stream << ",";
                            }
                            ++j;
                        }
                        stream << "] Kph" << " -> [";
                        j = 0;
                        for (auto maxspeed : ego_lane.max_speed_offset_m_list)
                        {
                            if (j >= 2) {
                                break;
                            }
                            stream << (int)maxspeed;
                            if (j + 1 == ego_lane.max_speed_offset_m_list.size())
                            {
                            }
                            else
                            {
                                stream << ",";
                            }
                            ++j;
                        }
                        stream << "] m  " << std::endl;

                        // 6
                        stream << "LnType: [";
                        j = 0;
                        for (auto lanetype : ego_lane.lane_type_enum_list)
                        {
                            if (j >= 2) {
                                break;
                            }
                            stream
                                << lane_type_enum_list[lanetype];
                            if (j + 1 == ego_lane.lane_type_enum_list.size())
                            {
                            }
                            else
                            {
                                stream << ",";
                            }
                            ++j;
                        }
                        stream << "] -> ";
                        stream << "[";
                        j = 0;
                        for (auto lanechoffset :
                             ego_lane.lane_type_change_offset_m_list)
                        {
                            if (j >= 2) {
                                break;
                            }
                            stream << (int)lanechoffset;
                            if (j + 1 ==
                                (ego_lane.lane_type_change_offset_m_list.size()))
                            {
                            }
                            else
                            {
                                stream << ",";
                            }
                            ++j;
                        }
                        stream << "] m" << std::endl;
                    }

                    // 7
                    stream
                        << "Remain: Ego "
                        << topic_ptr_pnc_infer_result_tmp->pnc_route_info
                               .remain_distance_m
                        << " | " << "Tar: "
                        << topic_ptr_pnc_infer_result_tmp->pnc_route_info
                               .target_remain_distance_m
                        << " m" << std::endl;
                    int lane_size = 0;
                    stream << "LCFlag: [";
                    for (auto lc_enum :
                         topic_ptr_pnc_infer_result_tmp->pnc_route_info
                             .front_lane_change_enum_list)
                    {
                        if (lane_size >= 3) {
                            break;
                        }
                        stream
                            << front_lane_change_enum_list[lc_enum];
                        if (lane_size + 1 ==
                            topic_ptr_pnc_infer_result_tmp->pnc_route_info
                                .front_lane_change_enum_list.size())
                        {
                        }
                        else
                        {
                            stream << ", ";
                        }
                        lane_size++;
                    }
                    stream << "] -> [";

                    int chanend_dis = 0;
                    for (auto chanenddis :
                         topic_ptr_pnc_infer_result_tmp->pnc_route_info
                             .change_end_dist_m_list)
                    {
                        if (chanend_dis >= 3) {
                            break;
                        }
                        stream << (int)chanenddis;
                        if (chanend_dis + 1 ==
                            topic_ptr_pnc_infer_result_tmp->pnc_route_info
                                .change_end_dist_m_list.size())
                        {
                        }
                        else
                        {
                            stream << ", ";
                        }
                        chanend_dis++;
                    }
                    stream << "]|";
                    stream
                        << static_cast<int>(
                               topic_ptr_pnc_infer_result_tmp->pnc_route_info
                                   .lane_change_times)
                        << " time" << std::endl;
                    
                    //8
                    if (left_idx != -1) {
                        const auto& left_lane =
                            topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list[left_idx];
                        int j = 0;
                        stream << "LSM:[";
                        for (auto& change_type : left_lane.front_merge_and_split_enum_list) {
                            if (j >= 3) {
                                break;
                            }
                            stream << front_lane_change_enum_list[change_type];
                            if (j+1 == left_lane.front_merge_and_split_enum_list.size()) {

                            } else {
                                stream << ",";
                            }
                            ++j;
                        }
                        stream << "] -> [";
                        j = 0;
                        for (auto& change_dis : left_lane.change_end_dist_m_list) {
                            if (j >= 3) {
                                break;
                            }
                            stream << (int)change_dis;
                            if(j+1 == left_lane.change_end_dist_m_list.size()) {

                            } else {
                                stream << ",";
                            }
                            ++j;
                        }
                        stream << "] m |";
                    }
                    if(right_idx != -1) {
                        const auto& right_lane =
                            topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list[right_idx];
                        int j = 0;
                        stream << "RSM:[";
                        for (auto& change_type : right_lane.front_merge_and_split_enum_list) {
                            if (j >= 3) {
                                break;
                            }
                            stream << front_lane_change_enum_list[change_type];
                            if(j+1 == right_lane.front_merge_and_split_enum_list.size()) {

                            } else {
                                stream << ",";
                            }
                            ++j;
                        }
                        stream << "] -> [";
                        j = 0;
                        for (auto& change_dis : right_lane.change_end_dist_m_list) {
                            if (j >= 3) {
                                break;
                            }
                            stream << (int)change_dis;
                            if (j+1 == right_lane.change_end_dist_m_list.size()) {

                            } else {
                                stream << ",";
                            }
                            ++j;
                        }
                        stream << "] m " << std::endl;
                    }

                    // 9
                    stream
                        << "odd: "
                        << topic_ptr_pnc_infer_result_tmp->pnc_route_info
                               .current_unodd_info.unodd_status_enum
                        << "/"
                        << (int)topic_ptr_pnc_infer_result_tmp->pnc_route_info
                               .current_unodd_info
                               .distance_to_next_unodd_start_m
                        << "|";

                    bool is_show = false;

                    stream << "N odd: [";
                    for (auto routerunodinfo :
                         topic_ptr_pnc_infer_result_tmp->pnc_route_info
                             .next_unodd_info_list)
                    {
                        is_show = true;
                        stream
                            << routerunodinfo.unodd_status_enum << "/"
                            << (int)routerunodinfo.distance_to_next_unodd_start_m
                            << "|";
                    }
                                
                    stream << "]" << std::endl;

                    //10
                    for (size_t i = 0;
                        i < topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list_size;
                        ++i) {
                        if(topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list[i].lane_id == 0) {
                        continue;
                        }
                        if ( i == 0) {
                            stream << "id/Lg:"
                            << (int)topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list[i].lane_id << "/"
                            << (int)topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list[i].remain_distance_m
                            << " | ";
                        } else {
                            stream << 
                            (int)topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list[i].lane_id << "/"
                            << (int)topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list[i].remain_distance_m
                            << " | ";
                        }
                        if (i == 6 && topic_ptr_pnc_infer_result_tmp->pnc_map_lane_list_size > 6) {
                            stream << std::endl;
                        }
                    }
                    stream << std::endl;

                    //11
                    stream
                        << "Ha size:"
                        << (int)topic_ptr_pnc_infer_result_tmp->pnc_route_info
                               .reverse_1
                        << "|" << "FH_id:" 
                        << (int)topic_ptr_pnc_infer_result_tmp->pnc_route_info
                               .reverse_2
                        << "|" << "BH_id:" 
                        << (int)topic_ptr_pnc_infer_result_tmp->pnc_route_info
                               .reverse_3
                        << "|" << "match_p_h:"
                        << (int)topic_ptr_pnc_infer_result_tmp->pnc_route_info
                               .reverse_4
                        << "->" << (int)topic_ptr_pnc_infer_result_tmp->pnc_route_info
                               .reverse_5;
                    stream << std::endl;

                    //12
                    if (!topic_ptr_pnc_infer_result_tmp->pnc_route_info.action_point_offset_m_list.empty()) {
                        stream
                        << "error_code:";
                        for (auto& error : topic_ptr_pnc_infer_result_tmp->pnc_route_info.action_point_offset_m_list) {
                            std::string str = std::to_string(error);
                            if (str.size() < 2) {
                                continue;
                            }
                            std::swap(str[0], str[1]);   
                            stream << str << "|";
                        }
                        stream << std::endl;
                    }
                }

            }
        }
    }
    void AdapterDebugInfoShowNode::AlgPnCFsmAdapterShow(std::stringstream &stream)
    {
        float pnc_fsm_out_frequency = 0.0;
        GetValueFromTopic(topic_name_pnc_fsm_out, topic_name_pnc_fsm_out_zero, topic_ptr_pnc_fsm_out, topic_ptr_pnc_fsm_out_zero, topic_ptr_pnc_fsm_out_tmp,
                          is_ptr_pnc_fsm_out_got, is_ptr_pnc_fsm_out_zero_got, pnc_fsm_out_frequency);
        if (config_b_pnc_fsm_show)
        {
            AlgPnCFsmAdapter(stream, is_ptr_pnc_fsm_out_got, is_ptr_pnc_fsm_out_zero_got, pnc_fsm_out_frequency, topic_ptr_pnc_fsm_out_tmp);
        }
    }
    void AdapterDebugInfoShowNode::AlgVehicleControllerAdapter(std::stringstream &stream)
    {
        std::shared_ptr<vehicle_msgs::msg::VehicleControllerStatus const> topic_ptr_vehicle_controller_sts_10ms_tmp;
        if (config_b_vehicle_controller_status_10ms_show)
        {
            TopicInfo topic_info;
            topic_info.head_name = "Vehicle Controller";
            topic_info.topic_name = topic_name_controller_status_10ms;
            stream << InitTopicHeader(topic_info,topic_ptr_vehicle_controller_sts_10ms_tmp) << std::endl;
            if(!topic_ptr_vehicle_controller_sts_10ms_tmp){
                return;
            }
            else{
                if (5u == topic_ptr_vehicle_controller_sts_10ms_tmp->long_ctrl_sts_enum){
                    stream << ADD_TEXT_COLOR_LEFT() << "LongCtrlSts : " << vehicle_long_lat_ctrl_sts_map[topic_ptr_vehicle_controller_sts_10ms_tmp->long_ctrl_sts_enum] << color_blue;
                }
                else{
                    stream << "LongCtrlSts : " << vehicle_long_lat_ctrl_sts_map[topic_ptr_vehicle_controller_sts_10ms_tmp->long_ctrl_sts_enum];
                }
                if (5u == topic_ptr_vehicle_controller_sts_10ms_tmp->lat_ctrl_sts_enum){
                    stream << ADD_TEXT_COLOR_LEFT() << " | LatiCtrlSts : " << vehicle_long_lat_ctrl_sts_map[topic_ptr_vehicle_controller_sts_10ms_tmp->lat_ctrl_sts_enum] << color_blue<< std::endl;
                }
                else{
                    stream << " | LatiCtrlSts : " << vehicle_long_lat_ctrl_sts_map[topic_ptr_vehicle_controller_sts_10ms_tmp->lat_ctrl_sts_enum] << std::endl;
                }
            }
        }
    }
    void AdapterDebugInfoShowNode::AlgVehicleChassisAdapter(std::stringstream &stream)
    {
        if (config_b_vehicle_chassis_10ms_show)
        {
            TopicInfo topic_info;
            topic_info.head_name = "Vehicle Chassis";
            topic_info.topic_name = topic_name_vehicle_chassis_10ms;
            topic_info.node_name = "alg_adapter_vehicle_pub";
            stream << InitTopicHeader(topic_info,topic_ptr_vehicle_chassis_10ms_tmp) << std::endl;
            if (topic_ptr_vehicle_chassis_10ms_tmp)
            {
                stream << "StrWheel Deg : " << topic_ptr_vehicle_chassis_10ms_tmp->strsys.pnnagsae_deg << " deg "
                                                                                                          " | Speed : "
                       << topic_ptr_vehicle_chassis_10ms_tmp->strsys.pnnagsae_grd << " deg/s " << " | ";
                if (abs(topic_ptr_vehicle_chassis_10ms_tmp->strsys.drvr_strg_dlvrd_toq_nm) > 1.0)
                {
                    stream << ADD_TEXT_COLOR_LEFT() << "HandT(+|-) : "
                           << topic_ptr_vehicle_chassis_10ms_tmp->strsys.drvr_strg_dlvrd_toq_nm << " N*m " << color15 << std::endl;
                }
                else
                {
                    stream << "HandT(+|-) : " << topic_ptr_vehicle_chassis_10ms_tmp->strsys.drvr_strg_dlvrd_toq_nm << " N*m " << std::endl;
                }
                stream << "CurSpd : " << topic_ptr_vehicle_chassis_10ms_tmp->vehdyn.vehspd.vehspd_kph << " km/h "
                       << topic_ptr_vehicle_chassis_10ms_tmp->vehdyn.vehspd.vehspd_m_s << " m/s "
                       << " | YawRate : " << topic_ptr_vehicle_chassis_10ms_tmp->vehdyn.vehaccl.vehdyn_yawrate_deg_s << " deg/s" << std::endl;
                if (topic_ptr_vehicle_chassis_10ms_tmp->vehdyn.vehaccl.long_accl_m_s2 < -1)
                {
                    stream << ADD_TEXT_COLOR_LEFT() << "Acc Lon : " << topic_ptr_vehicle_chassis_10ms_tmp->vehdyn.vehaccl.long_accl_m_s2 << " m/s2 " << color3
                           << " | Lat : " << topic_ptr_vehicle_chassis_10ms_tmp->vehdyn.vehaccl.lat_accl_m_s2 << " m/s2 ";
                }
                else
                {
                    stream << "Acc Lon : " << topic_ptr_vehicle_chassis_10ms_tmp->vehdyn.vehaccl.long_accl_m_s2 << " m/s2 "
                           << " | Lat : " << topic_ptr_vehicle_chassis_10ms_tmp->vehdyn.vehaccl.lat_accl_m_s2 << " m/s2 ";
                }
                stream << " | Epb Staus : " << topic_ptr_vehicle_chassis_10ms_tmp->brksys.prkbrk.epbsts_enum << std::endl;
                if (1u == topic_ptr_vehicle_chassis_10ms_tmp->pt.acclpdl.is_accl_ovrd)
                {
                    stream << ADD_TEXT_COLOR_LEFT() << "Accpdl Ovrd : " << static_cast<int>(topic_ptr_vehicle_chassis_10ms_tmp->pt.acclpdl.is_accl_ovrd) << color3
                           << " | Pos : " << topic_ptr_vehicle_chassis_10ms_tmp->pt.acclpdl.ept_accl_pos;
                }
                else
                {
                    stream << "Accpdl Ovrd : " << static_cast<int>(topic_ptr_vehicle_chassis_10ms_tmp->pt.acclpdl.is_accl_ovrd) << " | Pos : " << topic_ptr_vehicle_chassis_10ms_tmp->pt.acclpdl.ept_accl_pos;
                }
                stream << " | ";
                if (1u == topic_ptr_vehicle_chassis_10ms_tmp->brksys.brkpdl.brkpdl_appd_enum)
                {
                    stream << "Brkpdl Pos : " << topic_ptr_vehicle_chassis_10ms_tmp->brksys.brkpdl.brkpdl_appd_pos
                           << " | Press : " << topic_ptr_vehicle_chassis_10ms_tmp->brksys.brkpdl.brkpdl_appd_prs_kpa << " Kpa" << std::endl;
                }
                else
                {
                    stream << "Brkpdl Pos : " << topic_ptr_vehicle_chassis_10ms_tmp->brksys.brkpdl.brkpdl_appd_pos
                           << " | Press : " << topic_ptr_vehicle_chassis_10ms_tmp->brksys.brkpdl.brkpdl_appd_prs_kpa << " Kpa" << std::endl;
                }
                stream << "FL | FR | RL | RR | Wheel Spd(kph) & Cnt: " << std::endl
                       << "| "
                       << topic_ptr_vehicle_chassis_10ms_tmp->whl.whlspdsts.fl_whlspd_kph << " "
                       << topic_ptr_vehicle_chassis_10ms_tmp->whl.whlplscnt.fl_whlpls_cnt << " | "
                       << topic_ptr_vehicle_chassis_10ms_tmp->whl.whlspdsts.fr_whlspd_kph << " "
                       << topic_ptr_vehicle_chassis_10ms_tmp->whl.whlplscnt.fr_whlpls_cnt << " | "
                       << topic_ptr_vehicle_chassis_10ms_tmp->whl.whlspdsts.rl_whlspd_kph << " "
                       << topic_ptr_vehicle_chassis_10ms_tmp->whl.whlplscnt.rl_whlpls_cnt << " | "
                       << topic_ptr_vehicle_chassis_10ms_tmp->whl.whlspdsts.rr_whlspd_kph << " "
                       << topic_ptr_vehicle_chassis_10ms_tmp->whl.whlplscnt.rr_whlpls_cnt << " | " << std::endl;
            }
            else
            {
                return;
            }
        }
    }
    void AdapterDebugInfoShowNode::AlgHealthMonitorAdapter(std::stringstream &stream)
    {
        float temperature_deg = -999.0f;
        float cpu_load_percent = -999.0f;
        std::string cpu_load_info = "";
        std::string str_alg_health_monitor_version = get_alg_node_version("alg_health_monitor");
        if (getValue(topic_name_health_monitor_info, topic_ptr_health_monitor_info))
        {
            temperature_deg = topic_ptr_health_monitor_info->temperature_deg;
            cpu_load_percent = topic_ptr_health_monitor_info->cpu_load_percent;
            if (topic_ptr_health_monitor_info->other_info_str_list.size() > 0)
            {
                cpu_load_info = topic_ptr_health_monitor_info->other_info_str_list[0];
            }

            if (cpu_load_percent >= 90)
            {
                stream << ADD_TEXT_COLOR_LEFT() << " CpuLoadPecent : " << cpu_load_percent << "%" << ADD_TEXT_COLOR_RIGHT(1.0, 0.0, 0.0, 1.0);
            }
            else if (cpu_load_percent >= 80)
            {
                stream << ADD_TEXT_COLOR_LEFT() << " CpuLoadPecent : " << cpu_load_percent << "%" << color_orange;
            }
            else if (cpu_load_percent <= 0)
            {
                stream << ADD_TEXT_COLOR_LEFT() << " CpuLoadPecent : " << "InValid" << ADD_TEXT_COLOR_RIGHT(1.0, 0.0, 0.0, 1.0);
            }
            else
            {
                stream << " CpuLoadPecent : " << cpu_load_percent << "%";
            }

            if (is_ipu_8775)
            {
                stream << std::fixed << std::setprecision(0);
                if (temperature_deg >= 100)
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " | Temperature : " << temperature_deg << color_red << "℃";
                }
                else if (temperature_deg <= 0)
                {
                    stream << ADD_TEXT_COLOR_LEFT() << " | Temperature : " << "InValid" << color_red<< "℃";
                }
                else
                {
                    stream << " | Temperature : " << temperature_deg << "℃";
                }
                stream << std::fixed << std::setprecision(2);
            }

            if (!cpu_load_info.empty())
            {
                stream << std::endl;
                std::istringstream iss(cpu_load_info);
                std::vector<double> cpu_loads;
                double load;
                while (iss >> load)
                {
                    cpu_loads.push_back(load);
                }

                stream << "[ ";
                for (size_t i = 0; i < cpu_loads.size(); ++i)
                {

                    if (cpu_loads[i] >= 90)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << cpu_loads[i] << "% " << ADD_TEXT_COLOR_RIGHT(1.0, 0.0, 0.0, 1.0);
                    }
                    else if (cpu_loads[i] >= 80)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << cpu_loads[i] << "% " << color_orange;
                    }
                    else if (cpu_loads[i] <= 0)
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << "InValid ," << ADD_TEXT_COLOR_RIGHT(1.0, 0.0, 0.0, 1.0);
                    }
                    else
                    {
                        stream << cpu_loads[i] << "% ";
                    }
                }
                stream << "]";
            }

            stream << std::endl;
        }
        else
        {
            stream << ADD_TEXT_COLOR_LEFT() << "=== Health Monitor " << str_alg_health_monitor_version << " ===" << color_red << std::endl;
        }
    }
    void AdapterDebugInfoShowNode::AlgParameterConfig(std::stringstream &stream)
    {
        static std::string vehicle_info_str = "";
        std::string str_alg_x86_forward_master_version = get_alg_node_version("central_node");
        if (getValue(topic_name_vehicle_info, topic_ptr_vehicle_info))
        {
            vehicle_info_str = topic_ptr_vehicle_info->para_str;
        }

        if (parameter_vehicle_struct_.veh_name.empty())
        {
            if (getValue(topic_name_parameter_vehicle, topic_ptr_vehicle_parameter))
            {
                std::string parameter_vehicle_str = topic_ptr_vehicle_parameter->data.c_str();
                if (!parameter_vehicle_str.empty())
                {
                    xpack::json::decode(parameter_vehicle_str, parameter_vehicle_struct_);
                    std::string temp_file_path = "/tmp/parameterConfig.txt";

                    std::ofstream temp_file(temp_file_path, std::ios::trunc);
                    if (!temp_file)
                    {
                        // std::cerr << "无法创建临时文件" << std::endl;
                    }
                    else
                    {
                        temp_file << parameter_vehicle_struct_.veh_name << std::endl;
                        temp_file << parameter_vehicle_struct_.vin_code << std::endl;
                        temp_file.close();
                    }
                }
            }
        }
        stream << "NexMsg : " << config_str_nex_msgs_version;
        if (is_ipu_8775)
        {
            stream << "| QNX";
        }
        else
        {
            stream << "| Orin";
        }
        stream << " | " << vehicle_info_str << " | " << parameter_vehicle_struct_.veh_name << " | "
            << str_alg_x86_forward_master_version << " -> " << NEX_NODE_VERSION << std::endl;
    }

    void AdapterDebugInfoShowNode::OutputLineBias(std::ostream &stream,
                                                  const std::vector<debug_msgs::msg::DebugInfoFloat32> &debug_info,
                                                  int distance) {
        auto find_iter = [&debug_info, &distance](const std::string &side) {
            return std::find_if(debug_info.begin(), debug_info.end(), [&distance, &side](const auto &info) {
                return info.signal_name == side + "_" + std::to_string(distance);
            });
        };
        auto left_iter = find_iter("left");
        auto right_iter = find_iter("right");

        auto output_info = [&stream, this, &debug_info](auto iter) {
            if (iter != debug_info.end()) {
                if(iter->value > 0){
                    stream << ADD_TEXT_COLOR_LEFT() << "[" << color_orange;
                }else{
                    stream << "[";
                }
                stream.precision(1);
                stream << std::fixed << std::abs(iter->value);
                if (iter->value > 0) {
                    stream << "]";
                } else if (iter->value < 0) {
                    stream << ADD_TEXT_COLOR_LEFT() << "]" << color_orange;
                }
            } else {
                stream << "[NA]";
            }
        };

        if (left_iter != debug_info.end() || right_iter != debug_info.end()) {
            output_info(left_iter);
            output_info(right_iter);
            stream << " | ";
        } else {
            stream << "[NA][NA] | ";
        }
    }
    void AdapterDebugInfoShowNode::AlgFusionRoadAdapter(std::stringstream &stream)
    {
        TopicInfo topic_info;
        topic_info.head_name = "Fusion Road";
        topic_info.topic_name = topic_name_fusion_road;
        topic_info.node_name = "alg_fusion_road";
        stream << InitTopicHeader(topic_info, topic_ptr_fusion_road) << std::endl;
        if (!topic_ptr_fusion_road)
        {
            return;
        }
        else
        {
            float frequency = getFrequency(topic_name_fusion_road, topic_ptr_fusion_road);
            if (topic_ptr_fusion_monomap)
            {
                stream << "time diff fusionroad - monomap: "
                       << (topic_ptr_fusion_road->std_header.timestamp_ns - topic_ptr_fusion_monomap->std_header.timestamp_ns) * 1e-9 << std::endl;
            }
            std::unordered_map<int, std::string> fusion_road_status_info{{0, "Disable Hdmap"},
                                                                         {1, "Path ID is 0"},
                                                                         {2, "In Non ODD Withdraw Lanes Lines"},
                                                                         {3, "Approching Non Odd"},
                                                                         {4, "Map Engine Api Not Init No Heartbeat"},
                                                                         {5, "No Loc Pose Msg"},
                                                                         {6, "Approching Hdmap End"},
                                                                         {7, "Unmatch In Hdmap"},
                                                                         {8, "Unmatch In Fusion"},
                                                                         {9, "Hdmap Nullptr"},
                                                                         {10, "Origin Monomap"},
                                                                         {11, "Local Map Empty"},
                                                                         {12, "All Path Empty"},
                                                                         {13, "Map Empty"},
                                                                         {14, "Map Pos Check Failed"},
                                                                         {15, "Map Pos Invalid"},
                                                                         {16, "Loc Pos Low Quality"},
                                                                         {50, "Matched"},
                                                                         {51, "Large Curve Matched"},
                                                                         {52, "Entering Pure Hdmap"},
                                                                         {53, "In Perception POI"},
                                                                         {54, "Cache Prolong"},
                                                                         {100, "Config Pure Hdmap"},
                                                                         {101, "In Merge Split"},
                                                                         {102, "In Perception POI"},
                                                                         {103, "Fusion Unmatched"},
                                                                         {104, "Leaving Hdmap"},
                                                                         {105, "Large Curve Matched"},
                                                                         {106, "Hdmap Stub Area"},
                                                                         {107, "Urban Config Pure Hdmap"},
                                                                         {108, "Intersection Hdmap"},
                                                                         {109, "Y split Y merge Hdmap"},
                                                                         {110, "Monomap Low Plaus"},
                                                                         {111, "Hdmap Virtual Line"}};
            float left_c0 = 0, right_c0 = 0;
            for (const auto &lane_line_temp : topic_ptr_fusion_road->lane_lines_list)
            {
                if (lane_line_temp.start_offset_m > 0 || lane_line_temp.end_offset_m < 0)
                {
                    continue;
                }

                if (lane_line_temp.position_enum == 1)
                { // LEFT
                    if (!lane_line_temp.seg_lane_line_list.empty())
                    {
                        for (const auto &pt : lane_line_temp.seg_lane_line_list[0].geometry.sampling_point_list)
                        {
                            if (pt.x_m > 0)
                            {
                                left_c0 = pt.y_m;
                                break;
                            }
                        }
                    }
                }
                else if (lane_line_temp.position_enum == 8)
                { // RIGHT
                    if (!lane_line_temp.seg_lane_line_list.empty())
                    {
                        for (const auto &pt : lane_line_temp.seg_lane_line_list[0].geometry.sampling_point_list)
                        {
                            if (pt.x_m > 0)
                            {
                                right_c0 = pt.y_m;
                                break;
                            }
                        }
                    }
                }
            }
            auto iter = fusion_road_status_info.find(static_cast<int>(topic_ptr_fusion_road->status_enum));
            std::string aux_info = "";
            if (iter != fusion_road_status_info.end())
            {
                aux_info = iter->second;
            }
            switch (static_cast<uint8_t>(topic_ptr_fusion_road->status_enum / 50))
            {
            case 0:
            {
                stream << "Fusion Mode: Monomap (" << aux_info << ")" << std::endl;
                break;
            }
            case 1:
            {
                stream << ADD_TEXT_COLOR_LEFT() << "Fusion Mode: Fusion (" << aux_info << ")" << color_green << std::endl;
                break;
            }
            case 2:
            {
                stream << ADD_TEXT_COLOR_LEFT() << "Fusion Mode: Hdmap (" << aux_info << ")" << color_blue << std::endl;
                break;
            }
            default:
                stream << "Fusion Mode: Unknown" << std::endl;
                break;
            }

            if (getValue(topic_name_fusion_road_debug, topic_ptr_fusion_road_debug))
            {
                const std::vector<int> distances = {0, 10, 20, 30, 40};
                stream << "Bias(cm): ";
                for (int distance : distances) {
                    OutputLineBias(stream, topic_ptr_fusion_road_debug->debug_info, distance);
                }
                stream << std::endl;
            }
            // std::string status_enum_info = topic_ptr_fusion_road->status_enum == 0 ? "Monomap" :
            //                               (topic_ptr_fusion_road->status_enum == 1 ? "Fusion" :
            //                               (topic_ptr_fusion_road->status_enum == 2 ? "Hdmap" : "Unknown"));
            // stream << ADD_TEXT_COLOR_LEFT() << "Fusion Mode: " << status_enum_info  << color_green << std::endl;
            stream << "C0: left " << left_c0 << " m right " << right_c0 << " m ";
            float mid_bias = (left_c0 + right_c0) * 0.5;
            if (mid_bias > 0.02)
            { // near side is right
                stream << " [⇤ " << abs(mid_bias) << " m " << ADD_TEXT_COLOR_LEFT() << " ⇥] " << color_orange << std::endl;
            }
            else if (mid_bias < -0.02)
            { // near side is left
                stream << ADD_TEXT_COLOR_LEFT() << " [⇤ " << color_orange << abs(mid_bias) << " m " << " ⇥] " << std::endl;
            }
            else
            {
                stream << " [⇤ " << abs(mid_bias) << " m " << " ⇥] " << std::endl;
            }

            auto get_lane_info_fun = [](const fusion_msgs::msg::Lane &lane) -> std::string
            {
                std::stringstream lane_info;
                lane_info << std::fixed << std::setprecision(2);
                lane_info << "id:" << (int)lane.id << " | width:";
                if (!lane.seg_lane_list.empty())
                {
                    lane_info << lane.seg_lane_list[0].lane_width_m << " m";
                }
                else
                {
                    lane_info << 0 << " m";
                }

                // start topo
                lane_info << " | s_topo: ";
                for (size_t i = 0; i < lane.lane_topo_list.size(); i++)
                {
                    if (lane.lane_topo_list[i].start_lane_id > 0)
                    {
                        lane_info << (int)lane.lane_topo_list[i].start_lane_id << ",";
                    }
                }

                // end topo
                lane_info << " | e_topo:";
                for (size_t i = 0; i < lane.lane_topo_list.size(); i++)
                {
                    if (lane.lane_topo_list[i].end_lane_id > 0)
                    {
                        lane_info << (int)lane.lane_topo_list[i].end_lane_id << ",";
                    }
                }

                return lane_info.str();
            };

            std::string ego_lane_info = "EL:";
            std::string left_lane_info = "LL:";
            std::string right_lane_info = "RL:";

            for (const auto &lane_temp : topic_ptr_fusion_road->lanes_list)
            {
                if (lane_temp.position_enum == 1)
                { // ego lane top

                    ego_lane_info += get_lane_info_fun(lane_temp);
                }
                else if (lane_temp.position_enum == 2)
                { // left

                    left_lane_info += get_lane_info_fun(lane_temp);
                }
                else if (lane_temp.position_enum == 8)
                { // right

                    right_lane_info += get_lane_info_fun(lane_temp);
                }
            }

            stream << left_lane_info << "\n"
                   << ego_lane_info << "\n"
                   << right_lane_info << "\n";
        }
    }
    void AdapterDebugInfoShowNode::AlgFusionMonomapAdapter(std::stringstream &stream)
    {
        TopicInfo topic_info;
        topic_info.head_name = "Fusion Monomap";
        topic_info.topic_name = topic_name_fusion_monomap;
        topic_info.node_name = "alg_fusion_monomap";
        stream << InitTopicHeader(topic_info,topic_ptr_fusion_monomap) << std::endl;
    }
    void AdapterDebugInfoShowNode::AlgFrontRadarFreqAdapter(std::stringstream &stream)
    {
        TopicInfo topic_info;
        topic_info.head_name = "FrontRadarFreq";
        topic_info.topic_name = topic_name_front_radar_objects;
        stream << InitTopicHeader(topic_info,topic_ptr_front_radar_objects) << std::endl;
        if(!topic_ptr_front_radar_objects)
        {
            return;
        }
        else
        {
            if (topic_ptr_front_radar_objects->radar_state.is_sgu_fail)
            {
                stream << ADD_TEXT_COLOR_LEFT() << " | sug_fail : " << topic_ptr_front_radar_objects->radar_state.is_sgu_fail << color3<< std::endl;
            }
            else
            {
                stream << " | TimeDelay: " << static_cast<double>(loc_dr_ts - topic_ptr_front_radar_objects->std_header.timestamp_ns) / 1e6 << " ms" << std::endl;
            }
        }
    }
    void AdapterDebugInfoShowNode::AlgFusionOdAdapter(std::stringstream &stream)
    {
        std::shared_ptr<fusion_msgs::msg::FusionObjects const> topic_ptr_fusion_objects_tmp;

        TopicInfo topic_info;
        topic_info.head_name = "Fusion OD";
        topic_info.topic_name = topic_name_fusion_objects;
        topic_info.node_name = "alg_fusion_objects";
        std::string str;
        std::function<void(std::shared_ptr<fusion_msgs::msg::FusionObjects const> &msg, std::string& str)> func = [](std::shared_ptr<fusion_msgs::msg::FusionObjects const>& msg, std::string& str){
            if(msg) {
                str = " | num: " + std::to_string(msg->objects_size);
            }
        };
        stream << InitTopicHeader(topic_info,topic_ptr_fusion_objects_tmp, func) << std::endl;
    }
    void AdapterDebugInfoShowNode::AlgDrAdapter(std::stringstream &stream)
    {
        std::shared_ptr<loc_msgs::msg::Odometry const> topic_ptr_loc_dr_tmp;
        if(!config_b_loc_dr_show)
        {
            return;
        }
        else
        {
            TopicInfo topic_info;
            topic_info.head_name = "Dr";
            topic_info.topic_name = topic_name_alg_loc_dr;
            stream << InitTopicHeader(topic_info,topic_ptr_loc_dr_tmp) << std::endl;
            if(!topic_ptr_loc_dr_tmp)
            {
                return;
            }
            else
            {
                if (topic_ptr_loc_dr_tmp->status_bits == 0){
                    stream << "x " << topic_ptr_loc_dr_tmp->pose.position.x_m << " m "
                           << " | y " << topic_ptr_loc_dr_tmp->pose.position.y_m << " m "
                           << " | z " << topic_ptr_loc_dr_tmp->pose.position.z_m << " m " 
                           << " | Diagnose : " << +topic_ptr_loc_dr_tmp->status_bits
                           << " | " << +topic_ptr_loc_dr_tmp->chassis_diag_bits
                           << " | " << +topic_ptr_loc_dr_tmp->imu_diag_bits << std::endl;
                }else if(topic_ptr_loc_dr_tmp->status_bits == 1){
                    stream << "x " << topic_ptr_loc_dr_tmp->pose.position.x_m << " m "
                            << " | y " << topic_ptr_loc_dr_tmp->pose.position.y_m << " m "
                            << " | z " << topic_ptr_loc_dr_tmp->pose.position.z_m << " m " 
                            << ADD_TEXT_COLOR_LEFT() << " | Diagnose : " << +topic_ptr_loc_dr_tmp->status_bits << color_orange
                            << " | " << +topic_ptr_loc_dr_tmp->chassis_diag_bits
                            << " | " << +topic_ptr_loc_dr_tmp->imu_diag_bits << std::endl;
                }else{
                    stream << "x " << topic_ptr_loc_dr_tmp->pose.position.x_m << " m "
                            << " | y " << topic_ptr_loc_dr_tmp->pose.position.y_m << " m "
                            << " | z " << topic_ptr_loc_dr_tmp->pose.position.z_m << " m " 
                            << ADD_TEXT_COLOR_LEFT() << " | Diagnose : " << +topic_ptr_loc_dr_tmp->status_bits << color_red
                            << " | " << +topic_ptr_loc_dr_tmp->chassis_diag_bits
                            << " | " << +topic_ptr_loc_dr_tmp->imu_diag_bits << std::endl;
                }
            }
        }
    }
    void AdapterDebugInfoShowNode::AlgMapEngineServer(std::stringstream &stream)
    {
        TopicInfo topic_info;
        topic_info.head_name = "MapEngine";
        topic_info.topic_name = topic_name_map_engine_info;
        topic_info.node_name = "alg_map_engine_server";
        // stream << InitTopicHeader(topic_info,topic_ptr_map_heart_beat) << std::endl;
        stream << InitTopicHeader(topic_info,topic_ptr_map_engine_info) << std::endl;
        if (!topic_ptr_map_engine_info)
        {
            return;
        }
        stream << "Route " << topic_ptr_map_engine_info->route_id
               << " | MCA map " << topic_ptr_map_engine_info->mca_map_id 
               << " | HPA map " << topic_ptr_map_engine_info->avp_map_id << " | Load ";
        std::bitset<16> load_map_type_bits(topic_ptr_map_engine_info->current_load_map_type_bits);
        if (load_map_type_bits.test(0)) {
            stream << "PRK_IN,";
        } else if (load_map_type_bits.test(1)) {
            stream << "PRK_OUT,";
        }
        if (load_map_type_bits.test(2)) {
            stream << "RN,";
        }
        if (load_map_type_bits.test(3)) {
            stream << "SD,";
        }
        if (load_map_type_bits.test(4)) {
            stream << "HD,";
        }
        stream << std::endl;
    }
    void AdapterDebugInfoShowNode::AlgPncDeciderIDM(std::stringstream &stream)
    {
        std::shared_ptr<planning_msgs::msg::DeciderResult const> topic_ptr_pnc_decider_result_tmp;
        TopicInfo topic_info;
        topic_info.head_name = "PnC Decider";
        topic_info.topic_name = topic_name_pnc_decider_result;
        topic_info.node_name = "alg_pnc_decider";
        stream << InitTopicHeader(topic_info,topic_ptr_pnc_decider_result_tmp) << std::endl;
        if(config_b_pnc_decider_show){
            if(!topic_ptr_pnc_decider_result_tmp){
                return;
            }
            else{
                if (topic_ptr_pnc_decider_result_tmp->rd_result.effective_target_list.empty())
                { 
                    stream << "Decider IDM Result ==" << std::endl;
                    return;
                }
                else
                {
                    stream << "Decider IDM Result ==" << std::endl;
                    for (auto &submode : topic_ptr_pnc_decider_result_tmp->rd_result.effective_target_list){
                        stream << "multimodal : " << pnc_decider_multimodal[submode.interaction_modal_enum] << std::endl;
                        for (auto &tags : submode.interaction_tags){
                            stream << " | obj_id : " << tags.obj_id;
                            stream << " | s_tag : " << pnc_decider_idm_stag[tags.s_tag_enum];
                            stream << " | l_tag : " << pnc_decider_idm_ltag[tags.l_tag_enum];
                            stream << std::endl;
                        }
                    }
                }
            }
        }

    }
    void AdapterDebugInfoShowNode::AlgSDDebugAdapter(std::stringstream &stream){
        static float map_sd_debug_frequency = 0.0;
        TopicInfo topic_info;
        topic_info.head_name = "MapEngine";
        topic_info.topic_name = topic_name_map_sd_debug;
        InitTopicHeader(topic_info,topic_ptr_map_sd_debug);
        if(!topic_ptr_map_sd_debug){
            return;
        }
        stream << "FrontLane : ";
        for (const auto &laneid : topic_ptr_map_sd_debug->lane_info.front_lane)
        {
            stream << laneid << " ";
        }
        stream << "BackLane : ";
        for (const auto &laneid : topic_ptr_map_sd_debug->lane_info.back_lane)
        {
            stream << laneid << " ";
        }
        
        extractLaneData(topic_ptr_map_sd_debug->lane_info.back_lane, topic_ptr_map_sd_debug->lane_info.front_lane, stream);
        stream << " " <<  topic_ptr_map_sd_debug->lane_info.segment_idx << "m"<< std::endl;
        for(const auto& guide_info : topic_ptr_map_sd_debug->ego_pose.next_cross_info_list){
            stream << "[" << guide_info.main_action_enum << " " << guide_info.assist_action_enum << " ";
            stream << guide_info.cur_to_segment_dist_m << "m] ";
        }
        if (topic_ptr_map_sd_debug->ego_pose.next_cross_info_list.size()){
            stream << std::endl;
        }
    }

    void AdapterDebugInfoShowNode::AlgRawMapDataAdapter(std::stringstream &stream)
    {
        TopicInfo topic_info;
        topic_info.head_name = "RawMapData";
        topic_info.topic_name = topic_name_alg_map_raw_map_data;
        stream << InitTopicHeader(topic_info,topic_ptr_alg_map_raw_map_data) << std::endl;
    }
    void AdapterDebugInfoShowNode::AlgLocMapMatchAdapter(std::stringstream &stream)
    {
        std::shared_ptr<loc_msgs::msg::LocMapInfo const> topic_ptr_loc_loc_map_info_tmp;
        TopicInfo topic_info;
        topic_info.head_name = "LocMapMatch";
        topic_info.topic_name = topic_name_alg_loc_loc_map_info;
        topic_info.node_name = "alg_loc_map";
        stream << InitTopicHeader(topic_info,topic_ptr_loc_loc_map_info_tmp) << std::endl;
        if(!topic_ptr_loc_loc_map_info_tmp){
            return;
        }
        stream << std::fixed << std::setprecision(2);
        stream << "Dist2Lane : Start " << topic_ptr_loc_loc_map_info_tmp->driving_info.distance_to_lane_start_m
               << " m"
               << " End " << topic_ptr_loc_loc_map_info_tmp->driving_info.distance_to_lane_end_m << " m"
               << " Offset " << topic_ptr_loc_loc_map_info_tmp->driving_info.path_start_offset_m << " m"
               << std::endl;
        stream << std::fixed << std::setprecision(3);
        stream << "Bias Lateral " << topic_ptr_loc_loc_map_info_tmp->driving_info.lateral_correction_m << " m"
               << " Bias Yaw " << topic_ptr_loc_loc_map_info_tmp->driving_info.heading_correction_deg << " deg"
               << std::endl;

        std::bitset<16> q_bits(topic_ptr_loc_loc_map_info_tmp->driving_info.loc_map_quality_enum);
        std::bitset<64> customer_odd_bits(topic_ptr_loc_loc_map_info_tmp->customer_odd_bits);
        stream << "Drive: ";
        if (q_bits.test(0)) {
            stream << "LANE,";
        } else if (q_bits.test(1)) {
            stream << "LINK,";
        }
        if (q_bits.test(3)) {
            stream << "HD,";
        } else {
            if (q_bits.test(4)) {
                stream << "RN,";
            } else if (q_bits.test(5)) {
                stream << "SD,";
            }
        } 
        if (q_bits.test(6)) {
            stream << "NAV ";
        } else if (q_bits.test(7)) {
            stream << "CRS ";
        } else {
            stream << "NONE ";
        }
        stream << topic_ptr_loc_loc_map_info_tmp->driving_info.loc_map_quality_enum;
        stream << " | Scene: ";
        if (customer_odd_bits.test(35)) {
            stream << "PRK_RECOG";
        } else if (customer_odd_bits.test(34)) {
            stream << "PRK_CFM";
        } else if (customer_odd_bits.test(33)) {
            stream << "CURVE";
        } else if (customer_odd_bits.test(32)) {
            stream << "JUNC";
        }
        
        stream << " | Loc: " 
            << fail_state_fusion_pose_enum[topic_ptr_loc_loc_map_info_tmp->driving_info.fail_state_fusion_pose_enum]
            << std::endl;

        stream << "LocPre :" 
        << avp_located_precondition_sts_enum[topic_ptr_loc_loc_map_info_tmp->parking_info.avp_located_precondition_sts_enum];

        stream << " | Park: ";
        if (customer_odd_bits.test(0)) {
            stream << "IN";
        } else if (customer_odd_bits.test(1)) {
            stream << "OUT";
        }

        stream << " | AvpMapId: " 
        << topic_ptr_loc_loc_map_info_tmp->parking_info.avp_located_map_id;

        stream << std::endl;

        stream << " | Reloc: " 
        << avp_relocated_sts_enum[topic_ptr_loc_loc_map_info_tmp->parking_info.avp_relocated_sts_enum];
           
        stream << " | Loc: " 
            << avp_located_sts_enum[topic_ptr_loc_loc_map_info_tmp->parking_info.avp_located_sts_enum];
        
        stream << std::endl;

        stream << " | FloorType: " 
            << avp_floor_type_enum[topic_ptr_loc_loc_map_info_tmp->parking_info.path_id];

        stream << " | PerSlotId: " 
        << topic_ptr_loc_loc_map_info_tmp->parking_info.perception_slot_id;

        stream << std::endl;
    }
    void AdapterDebugInfoShowNode::AlgLocSuperOdomAdapter(std::stringstream &stream)
    {
      std::shared_ptr<loc_msgs::msg::LocPose const> topic_ptr_loc_pose_tmp;
      TopicInfo topic;
      topic.topic_name = topic_name_alg_loc_loc_pose;
      topic.head_name = "LocSuperOdom";
      topic.node_name = "alg_loc_super_odom";
      stream << InitTopicHeader(topic,topic_ptr_loc_pose_tmp) << std::endl;
      if(topic_ptr_loc_pose_tmp){
        map_show->SetLonLatHead(topic_ptr_loc_pose_tmp->longitude_deg,topic_ptr_loc_pose_tmp->latitude_deg,-(topic_ptr_loc_pose_tmp->heading_deg));
        viz_loc_pose_pub_->publish(*topic_ptr_loc_pose_tmp);
        if (topic_ptr_loc_pose_tmp->path_id != 0 || topic_ptr_loc_pose_tmp->link_id != 0 || topic_ptr_loc_pose_tmp->lane_id != 0)
        {
            stream << ADD_TEXT_COLOR_LEFT() << "pathId : " << topic_ptr_loc_pose_tmp->path_id
                   << " linkId : " << topic_ptr_loc_pose_tmp->link_id
                   << " laneId : " << topic_ptr_loc_pose_tmp->lane_id << color_green;
        }
        else
        {
            stream << ADD_TEXT_COLOR_LEFT() << "pathId : " << topic_ptr_loc_pose_tmp->path_id
                   << " linkId : " << topic_ptr_loc_pose_tmp->link_id
                   << " laneId : " << topic_ptr_loc_pose_tmp->lane_id << color_orange;
        }
        stream << " | PosQuality : " << int(topic_ptr_loc_pose_tmp->map_pose_quality_enum) << std::endl;
        stream << std::fixed << std::setprecision(7);
        double longitude = topic_ptr_loc_pose_tmp->longitude_deg;
        double latitude = topic_ptr_loc_pose_tmp->latitude_deg;
        double tolerance = 1e-9; // 容差值
        if (std::fabs(longitude) < tolerance)
        {
            stream << ADD_TEXT_COLOR_LEFT() << "Loc Lon : " << longitude << color3 << " Lat : " << latitude << " | ";
        }
        else
        {
            stream << "Loc Lon : " << longitude << " Lat : " << latitude << " | ";
        }
        stream << std::fixed << std::setprecision(1);
        stream << "Height : " << topic_ptr_loc_pose_tmp->altitude_m << " m" << std::endl;
        stream << " Roll : " << topic_ptr_loc_pose_tmp->roll_deg << " deg";

        double pitch = topic_ptr_loc_pose_tmp->pitch_deg;
        double tolerance_pitch = 2;
        if (std::fabs(pitch) > tolerance_pitch) {
            stream << ADD_TEXT_COLOR_LEFT() << " Pitch : " << pitch << color3 << " deg";
        }
        else
        {
            stream << " Pitch : " << pitch << " deg";
        }

        stream << " Yaw : " << topic_ptr_loc_pose_tmp->heading_deg << " deg" << std::endl;
        stream << std::fixed << std::setprecision(2);
        stream << "Std Lon : " << topic_ptr_loc_pose_tmp->longitude_deg_std << " m"
               << " | Lat : " << topic_ptr_loc_pose_tmp->latitude_deg_std << " m"
               << " | Yaw : " << topic_ptr_loc_pose_tmp->heading_deg_std << " deg" << std::endl;
        stream << "Bias GyroZ : " << topic_ptr_loc_pose_tmp->calibration.gyro_z_bias_deg_s
               << " deg/s"
               << " | MountP : " << topic_ptr_loc_pose_tmp->calibration.mounting_angle_pitch_deg << " deg"
               << " | MountY : " << topic_ptr_loc_pose_tmp->calibration.mounting_angle_yaw_deg << " deg"
               << std::endl;
        stream << std::fixed << std::setprecision(3);
        stream << "Spd Scale : " << topic_ptr_loc_pose_tmp->calibration.wheelspd_scale
               << " | Conf : " << +topic_ptr_loc_pose_tmp->map_pose_quality_enum
               << " Init : " << +topic_ptr_loc_pose_tmp->initial_state_enum
               << " | SlotMatch : " << slot_matched_sts_enum[topic_ptr_loc_pose_tmp->slot_matched_sts_enum] 
               << std::endl;
        stream
            << "FailGnss : " << +topic_ptr_loc_pose_tmp->fail_state_gnss_enum
            << " | Imu : " << +topic_ptr_loc_pose_tmp->fail_state_imu_enum
            << " | Chassis : " << +topic_ptr_loc_pose_tmp->fail_state_vehicle_chassis_enum
            << " | LocMap : " << +topic_ptr_loc_pose_tmp->fail_state_loc_map_enum
            << std::endl;
      }
      else {
        return;
      }
    }
    void AdapterDebugInfoShowNode::AlgImuAdapter(std::stringstream &stream)
    {
        std::shared_ptr<p_msgs::msg::IMU const> topic_ptr_sensor_pos_imuinfo_tmp;
        if(!config_b_sensor_imu_show){
            return;
        }
        else{
            TopicInfo topic_info;
            topic_info.head_name = "IMU";
            topic_info.topic_name = topic_name_sensor_pos_imuinfo;
            topic_info.node_name = "alg_adapter_ap_pub";
            stream << InitTopicHeader(topic_info,topic_ptr_sensor_pos_imuinfo_tmp) << std::endl;
            if(!topic_ptr_sensor_pos_imuinfo_tmp){
                return;
            }
            else{
                if (std::fabs(topic_ptr_sensor_pos_imuinfo_tmp->accel_z_m_s2) < 1e-6){
                    stream << ADD_TEXT_COLOR_LEFT() 
                           << "accel: x " << topic_ptr_sensor_pos_imuinfo_tmp->accel_x_m_s2 
                           << " | y " << topic_ptr_sensor_pos_imuinfo_tmp->accel_y_m_s2
                           << " | z " << topic_ptr_sensor_pos_imuinfo_tmp->accel_z_m_s2 << color3;
                    stream << " || gyro: x " << topic_ptr_sensor_pos_imuinfo_tmp->gyro_x_deg_s 
                           << " | y " << topic_ptr_sensor_pos_imuinfo_tmp->gyro_y_deg_s
                           << " | z " << topic_ptr_sensor_pos_imuinfo_tmp->gyro_z_deg_s << std::endl;
                }
                else{
                    stream << "accel: x " << topic_ptr_sensor_pos_imuinfo_tmp->accel_x_m_s2 
                           << " | y " << topic_ptr_sensor_pos_imuinfo_tmp->accel_y_m_s2
                           << " | z " << topic_ptr_sensor_pos_imuinfo_tmp->accel_z_m_s2;
                    stream << " || gyro: x " << topic_ptr_sensor_pos_imuinfo_tmp->gyro_x_deg_s 
                           << " | y " << topic_ptr_sensor_pos_imuinfo_tmp->gyro_y_deg_s
                           << " | z " << topic_ptr_sensor_pos_imuinfo_tmp->gyro_z_deg_s << std::endl;
                }
            }
            // stream << "imu feq : " << static_cast<int>(topic_ptr_sensor_pos_imuinfo_tmp->imu_feq_hz) << " hz "
            //     << "| temperature : " << topic_ptr_sensor_pos_imuinfo_tmp->imu_temperature << std::endl;
        }
    }
    void AdapterDebugInfoShowNode::AlgSafetyCtrlAdapter(std::stringstream &stream)
    {
        TopicInfo topic_info;
        topic_info.head_name = "PnC SafeCtrl";
        topic_info.topic_name = topic_name_safetyctrl_out;
        topic_info.node_name = "alg_pnc_safety";
        stream << InitTopicHeader(topic_info,topic_ptr_safetyctrl_out) << std::endl;
        if(topic_ptr_safetyctrl_out)
        {
            std::string red_color_right = color3;
            std::string white_color_right = ADD_TEXT_COLOR_RIGHT(1.0, 1.0, 1.0, 1.0);
            auto print_safety_out = [&](const std::string &name, int value)
            {
                if (value == 0x3){
                    stream << ADD_TEXT_COLOR_LEFT() << name << safety_sts_enum[value] << red_color_right << " | ";
                }
                else{
                    stream << ADD_TEXT_COLOR_LEFT() << name << safety_sts_enum[value] << white_color_right << " | ";
                }
            };
            print_safety_out("fcw_sts_enum : ", static_cast<int>(topic_ptr_safetyctrl_out->aeb_control_out.fcw_sts_enum));
            print_safety_out("ldw_sts_enum : ", static_cast<int>(topic_ptr_safetyctrl_out->ldwldp_control_out.ldw_sts_enum));
            print_safety_out("ldp_sts_enum : ", static_cast<int>(topic_ptr_safetyctrl_out->ldwldp_control_out.ldp_sts_enum));
            stream << std::endl;
            print_safety_out("bsd_sts_enum : ", static_cast<int>(topic_ptr_safetyctrl_out->bsd_control_out.bsd_sts_enum));
            print_safety_out("elk_sts_enum : ", static_cast<int>(topic_ptr_safetyctrl_out->elk_control_out.elk_sts_enum));
            stream << std::endl;
            print_safety_out("dow_sts_enum : ",static_cast<int>(topic_ptr_safetyctrl_out->dow_control_out.dow_sts_enum));
            print_safety_out("aeb_sts_enum : ",static_cast<int>(topic_ptr_safetyctrl_out->aeb_control_out.aeb_sts_enum));
            stream << std::endl;
        }
        else{
            return;
        }
    }
    void AdapterDebugInfoShowNode::publish_steering_wheel()
    {
        visualization_msgs::msg::MarkerArray steering_wheel_angle_rviz;
        visualization_msgs::msg::Marker objsDelete;
        objsDelete.type = visualization_msgs::msg::Marker::LINE_STRIP;
        objsDelete.action = visualization_msgs::msg::Marker::DELETEALL;
        steering_wheel_angle_rviz.markers.emplace_back(objsDelete);
        visualization_msgs::msg::Marker steering_wheel_angle_objs;
        steering_wheel_angle_objs.header.frame_id = "base_link";
        steering_wheel_angle_objs.ns = "steering_wheel_angle";
        steering_wheel_angle_objs.header.stamp = nlibcpp::Time();
        steering_wheel_angle_objs.action = visualization_msgs::msg::Marker::ADD;
        steering_wheel_angle_objs.pose.orientation.w = 1.0;
        steering_wheel_angle_objs.id = 0;
        steering_wheel_angle_objs.type = visualization_msgs::msg::Marker::LINE_STRIP;
        steering_wheel_angle_objs.scale.x = 0.05;
        steering_wheel_angle_objs.scale.y = 0.08;
        steering_wheel_angle_objs.scale.z = 0;
        steering_wheel_angle_objs.color.r = 1.0;
        steering_wheel_angle_objs.color.g = 1.0;
        steering_wheel_angle_objs.color.b = 1.0; // color of the line: white.
        steering_wheel_angle_objs.color.a = 1.0;

        // topic_ptr_pnc_fsm_out->drv_signals.ica_status_enum
        // 0x0: Off
        // 0x1: Passive
        // 0x2: Standby
        // 0x3: Active
        // 0x4: Standstill_Active
        // 0x5: Standstill_Wait
        // 0x6: Override
        // 0x7: Brake_Only
        // 0x8: Reserved
        static bool is_ptr_pnc_fsm_out_got = false;
        static bool is_ptr_pnc_fsm_out_zero_got = false;
        std::shared_ptr<fsm_msgs::msg::FSMOut const> topic_ptr_pnc_fsm_out_tmp;
        float pnc_fsm_out_frequency = 0.0;
        if (getValue(topic_name_pnc_fsm_out, topic_ptr_pnc_fsm_out))
        {
            is_ptr_pnc_fsm_out_got = true;
            topic_ptr_pnc_fsm_out_tmp = topic_ptr_pnc_fsm_out;
            pnc_fsm_out_frequency = getFrequency(topic_name_pnc_fsm_out, topic_ptr_pnc_fsm_out);
        }
        else
        {
            is_ptr_pnc_fsm_out_got = false;
        }
        if (getValue(topic_name_pnc_fsm_out_zero, topic_ptr_pnc_fsm_out_zero))
        {
            is_ptr_pnc_fsm_out_zero_got = true;
            topic_ptr_pnc_fsm_out_tmp = topic_ptr_pnc_fsm_out_zero;
            pnc_fsm_out_frequency = getFrequency(topic_name_pnc_fsm_out_zero, topic_ptr_pnc_fsm_out_zero);
        }
        else
        {
            is_ptr_pnc_fsm_out_zero_got = false;
        }

        if (is_ptr_pnc_fsm_out_got || is_ptr_pnc_fsm_out_zero_got)
        {
            const auto ica_sts_enum = topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_sts.ica_sts_enum;
            if (ica_sts_enum == 3 || ica_sts_enum == 4)
            { // active blue
                steering_wheel_angle_objs.color.r = 0.0f;
                steering_wheel_angle_objs.color.g = 0.75f;
                steering_wheel_angle_objs.color.b = 1.0f;
            }
            else if (ica_sts_enum == 6)
            { // override red
                steering_wheel_angle_objs.color.r = 1.0f;
                steering_wheel_angle_objs.color.g = 0.0f;
                steering_wheel_angle_objs.color.b = 0.0f;
            }
            else
            { // else white
                steering_wheel_angle_objs.color.r = 1.0;
                steering_wheel_angle_objs.color.g = 1.0;
                steering_wheel_angle_objs.color.b = 1.0;
            }
        }

        float steering_wheel_angle = 0.0;
        float steering_wheel_angle_spd = 0.0;
        if (getValue(topic_name_vehicle_chassis_10ms, topic_ptr_vehicle_chassis_10ms))
        {
            steering_wheel_angle = topic_ptr_vehicle_chassis_10ms->strsys.pnnagsae_deg;
            steering_wheel_angle_spd = topic_ptr_vehicle_chassis_10ms->strsys.pnnagsae_grd;
        }

        if (getValue(topic_name_vehicle_chassis_10ms_zero, topic_ptr_vehicle_chassis_10ms_zero))
        {
            steering_wheel_angle = topic_ptr_vehicle_chassis_10ms_zero->strsys.pnnagsae_deg;
            steering_wheel_angle_spd = topic_ptr_vehicle_chassis_10ms_zero->strsys.pnnagsae_grd;
        }

        float o_x = config_f_steering_wheel_center_x, o_y = config_f_steering_wheel_center_y, r = config_f_steering_wheel_radius;

        // draw wheel circle
        geometry_msgs::msg::Point p[100];
        for (int i = 0; i < 100; i++)
        {
            double alpha = 2 * 3.1415926 / (100 - 1);
            p[i].x = o_x + r * cos(i * alpha);
            p[i].y = o_y + r * sin(i * alpha);
            p[i].z = 0;
            steering_wheel_angle_objs.points.push_back(p[i]);
        }

        steering_wheel_angle_objs.points.push_back(p[0]);
        steering_wheel_angle_rviz.markers.emplace_back(steering_wheel_angle_objs);
        steering_wheel_angle_objs.id++;
        steering_wheel_angle_objs.points.clear();

        geometry_msgs::msg::Point steeringP[4];
        if (abs(steering_wheel_angle) < 0.001)
        {
            steeringP[0].x = o_x;
            steeringP[0].y = o_y;
            steeringP[1].x = o_x - r;
            steeringP[1].y = o_y;
            steering_wheel_angle_objs.points.push_back(steeringP[0]);
            steering_wheel_angle_objs.points.push_back(steeringP[1]);
            steering_wheel_angle_rviz.markers.emplace_back(steering_wheel_angle_objs);
            steering_wheel_angle_objs.id++;
            steering_wheel_angle_objs.points.clear();

            steeringP[2].x = o_x;
            steeringP[2].y = o_y + r;
            steeringP[3].x = o_x;
            steeringP[3].y = o_y - r;
            steering_wheel_angle_objs.points.push_back(steeringP[2]);
            steering_wheel_angle_objs.points.push_back(steeringP[3]);
            steering_wheel_angle_rviz.markers.emplace_back(steering_wheel_angle_objs);
            steering_wheel_angle_objs.id++;
            steering_wheel_angle_objs.points.clear();
        }
        else if (steering_wheel_angle > 0)
        {
            float temp2 = steering_wheel_angle / 180 * 3.1415926;
            steeringP[0].x = o_x;
            steeringP[0].y = o_y;
            steeringP[1].x = o_x - cos(temp2) * r;
            steeringP[1].y = o_y - sin(temp2) * r;
            steering_wheel_angle_objs.points.push_back(steeringP[0]);
            steering_wheel_angle_objs.points.push_back(steeringP[1]);
            steering_wheel_angle_rviz.markers.emplace_back(steering_wheel_angle_objs);
            steering_wheel_angle_objs.id++;
            steering_wheel_angle_objs.points.clear();

            steeringP[2].x = o_x - sin(temp2) * r;
            steeringP[2].y = o_y + cos(temp2) * r;
            steeringP[3].x = o_x + sin(temp2) * r;
            steeringP[3].y = o_y - cos(temp2) * r;
            steering_wheel_angle_objs.points.push_back(steeringP[2]);
            steering_wheel_angle_objs.points.push_back(steeringP[3]);
            steering_wheel_angle_rviz.markers.emplace_back(steering_wheel_angle_objs);
            steering_wheel_angle_objs.id++;
            steering_wheel_angle_objs.points.clear();
        }
        else if (steering_wheel_angle < 0)
        {
            float temp = -steering_wheel_angle;
            float temp1 = temp / 180 * 3.1415926;
            steeringP[0].x = o_x;
            steeringP[0].y = o_y;
            steeringP[1].x = o_x - cos(temp1) * r;
            steeringP[1].y = o_y + sin(temp1) * r;
            steering_wheel_angle_objs.points.push_back(steeringP[0]);
            steering_wheel_angle_objs.points.push_back(steeringP[1]);
            steering_wheel_angle_rviz.markers.emplace_back(steering_wheel_angle_objs);
            steering_wheel_angle_objs.id++;
            steering_wheel_angle_objs.points.clear();

            steeringP[2].x = o_x + sin(temp1) * r;
            steeringP[2].y = o_y + cos(temp1) * r;
            steeringP[3].x = o_x - sin(temp1) * r;
            steeringP[3].y = o_y - cos(temp1) * r;
            steering_wheel_angle_objs.points.push_back(steeringP[2]);
            steering_wheel_angle_objs.points.push_back(steeringP[3]);
            steering_wheel_angle_rviz.markers.emplace_back(steering_wheel_angle_objs);
            steering_wheel_angle_objs.id++;
            steering_wheel_angle_objs.points.clear();
        }

        visualization_msgs::msg::Marker steering_wheel_angle_text;
        steering_wheel_angle_text.header.frame_id = "base_link";
        steering_wheel_angle_text.header.stamp = nlibcpp::Time();
        steering_wheel_angle_text.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
        steering_wheel_angle_text.action = visualization_msgs::msg::Marker::ADD;
        steering_wheel_angle_text.id = 0;
        steering_wheel_angle_text.ns = "steering_wheel_angle_text";
        steering_wheel_angle_text.pose.position.x = o_x + r + 1;
        steering_wheel_angle_text.pose.position.y = o_y;
        steering_wheel_angle_text.pose.position.z = 0.0;
        steering_wheel_angle_text.pose.orientation.w = 1;
        steering_wheel_angle_text.scale.x = 0.55;
        steering_wheel_angle_text.scale.y = 0.55;
        steering_wheel_angle_text.scale.z = 0.55;
        steering_wheel_angle_text.color.a = 1.0;
        steering_wheel_angle_text.color.r = 1.0;
        steering_wheel_angle_text.color.g = 1.0;
        steering_wheel_angle_text.color.b = 1.0;

        // topic_ptr_pnc_fsm_out->drv_signals.ica_status_enum
        // 0x0: Off
        // 0x1: Passive
        // 0x2: Standby
        // 0x3: Active
        // 0x4: Standstill_Active
        // 0x5: Standstill_Wait
        // 0x6: Override
        // 0x7: Brake_Only
        // 0x8: Reserved
        if (is_ptr_pnc_fsm_out_got || is_ptr_pnc_fsm_out_zero_got)
        {
            const auto ica_sts_enum = topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_sts.ica_sts_enum;
            if (ica_sts_enum == 2)
            { // standby blue
                steering_wheel_angle_objs.color.r = 0.53f;
                steering_wheel_angle_objs.color.g = 0.80f;
                steering_wheel_angle_objs.color.b = 1.0f;
            }
            else if (ica_sts_enum == 3 || ica_sts_enum == 4)
            { // active green
                steering_wheel_angle_objs.color.r = 0.0f;
                steering_wheel_angle_objs.color.g = 1.0f;
                steering_wheel_angle_objs.color.b = 0.0f;
            }
            else if (ica_sts_enum == 6)
            { // override red
                steering_wheel_angle_objs.color.r = 1.0f;
                steering_wheel_angle_objs.color.g = 0.0f;
                steering_wheel_angle_objs.color.b = 0.0f;
            }
            else
            { // else white
                steering_wheel_angle_objs.color.r = 1.0;
                steering_wheel_angle_objs.color.g = 1.0;
                steering_wheel_angle_objs.color.b = 1.0;
            }
        }

        std::stringstream ss;
        ss << "Ang:" << std::fixed << std::setprecision(2) << steering_wheel_angle << "|Spd:" << steering_wheel_angle_spd;
        steering_wheel_angle_text.text = ss.str();
        steering_wheel_angle_rviz.markers.emplace_back(steering_wheel_angle_text);
        steering_wheel_angle_text.id++;
        steering_wheel_angle_text.text.clear();
        driver_wheel_circle_pub->publish(steering_wheel_angle_rviz);
    }

    void AdapterDebugInfoShowNode::publish_plot_xy()
    {
        // Left down point
        float left_down_y = -20;
        float left_down_x = -14;
        float width = 7;
        float height = 6;
        float right_down_y = left_down_y - width;
        float right_down_x = left_down_x;
        float speed_high_limit = 144; // km/h

        left_down_x = this->config_f_plot_xy_left_down_x;
        left_down_y = this->config_f_plot_xy_left_down_y;
        width = this->config_f_plot_xy_width;
        height = this->config_f_plot_xy_height;

        if (is_driving_mode())
        {
            right_down_y = left_down_y - width;
            right_down_x = left_down_x;
            speed_high_limit = 144; // km/h
        }
        else
        {
            right_down_y = left_down_y - width;
            right_down_x = left_down_x;
            speed_high_limit = 4.32; // km/h
        }

        visualization_msgs::msg::MarkerArray vehicle_speed_rviz;
        visualization_msgs::msg::Marker vehicle_speed_objs;
        visualization_msgs::msg::Marker brake_pedal_objs;
        visualization_msgs::msg::Marker objsDelete;
        objsDelete.type = visualization_msgs::msg::Marker::LINE_STRIP;
        objsDelete.action = visualization_msgs::msg::Marker::DELETEALL;
        vehicle_speed_rviz.markers.emplace_back(objsDelete);
        visualization_msgs::msg::Marker vehicle_speed_text;
        visualization_msgs::msg::Marker xoy_text;
        visualization_msgs::msg::Marker xoy_objs;
        xoy_objs.header.frame_id = "base_link";
        xoy_objs.ns = "xoy_objs";
        xoy_objs.header.stamp = nlibcpp::Time();
        xoy_objs.action = visualization_msgs::msg::Marker::ADD;
        xoy_objs.pose.orientation.w = 1.0;
        xoy_objs.id = 0;
        xoy_objs.type = visualization_msgs::msg::Marker::ARROW;
        xoy_objs.scale.x = 0.1;
        xoy_objs.scale.y = 0.1;
        xoy_objs.scale.z = 0;
        xoy_objs.color.r = 1.0;
        xoy_objs.color.g = 1.0;
        xoy_objs.color.b = 1.0; // color of the line: white.
        xoy_objs.color.a = 1.0;
        xoy_text.color.r = 1.0;
        xoy_text.color.g = 1.0;
        xoy_text.color.b = 1.0; // color of the line: white.
        xoy_text.color.a = 1.0;

        static bool is_ptr_pnc_fsm_out_got = false;
        static bool is_ptr_pnc_fsm_out_zero_got = false;
        std::shared_ptr<fsm_msgs::msg::FSMOut const> topic_ptr_pnc_fsm_out_tmp;
        float pnc_fsm_out_frequency = 0.0;
        if (getValue(topic_name_pnc_fsm_out, topic_ptr_pnc_fsm_out))
        {
            is_ptr_pnc_fsm_out_got = true;
            topic_ptr_pnc_fsm_out_tmp = topic_ptr_pnc_fsm_out;
            pnc_fsm_out_frequency = getFrequency(topic_name_pnc_fsm_out, topic_ptr_pnc_fsm_out);
        }
        else
        {
            is_ptr_pnc_fsm_out_got = false;
        }
        if (getValue(topic_name_pnc_fsm_out_zero, topic_ptr_pnc_fsm_out_zero))
        {
            is_ptr_pnc_fsm_out_zero_got = true;
            topic_ptr_pnc_fsm_out_tmp = topic_ptr_pnc_fsm_out_zero;
            pnc_fsm_out_frequency = getFrequency(topic_name_pnc_fsm_out_zero, topic_ptr_pnc_fsm_out_zero);
        }
        else
        {
            is_ptr_pnc_fsm_out_zero_got = false;
        }

        if (is_ptr_pnc_fsm_out_got || is_ptr_pnc_fsm_out_zero_got)
        {
            // 0x0: Off
            // 0x1: Passive
            // 0x2: Standby
            // 0x3: Active
            // 0x4: Standstill_Active
            // 0x5: Standstill_Wait
            // 0x6: Override
            // 0x7: Brake_Only
            // 0x8: Fault
            // 0x9: Reserved
            const auto acc_sts_enum = topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_sts.acc_sts_enum;
            if (acc_sts_enum == 3 || acc_sts_enum == 4 || acc_sts_enum == 5) // active blue
            {
                xoy_objs.color.r = 20.0 / 255;
                xoy_objs.color.g = 190.0 / 255;
                xoy_objs.color.b = 230.0 / 255;
                xoy_text.color.r = 20.0 / 255;
                xoy_text.color.g = 190.0 / 255;
                xoy_text.color.b = 230.0 / 255;
                vehicle_speed_objs.color.r = 20.0 / 255;
                vehicle_speed_objs.color.g = 190.0 / 255;
                vehicle_speed_objs.color.b = 230.0 / 255;
            }
            // else if (topic_ptr_pnc_fsm_out_tmp->drv_signals.acc_status_enum == 2) // standby grey
            // {
            //     xoy_objs.color.r = 0.5;
            //     xoy_objs.color.g = 0.5;
            //     xoy_objs.color.b = 0.5;
            //     xoy_text.color.r = 0.5;
            //     xoy_text.color.g = 0.5;
            //     xoy_text.color.b = 0.5;
            //     vehicle_speed_objs.color.r = 0.5;
            //     vehicle_speed_objs.color.g = 0.5;
            //     vehicle_speed_objs.color.b = 0.5;
            // }
            else if (acc_sts_enum == 6) // override red
            {
                xoy_objs.color.r = 1.0;
                xoy_objs.color.g = 0.2;
                xoy_objs.color.b = 1.0;
                xoy_text.color.r = 1.0;
                xoy_text.color.g = 0.2;
                xoy_text.color.b = 1.0;
                vehicle_speed_objs.color.r = 1.0;
                vehicle_speed_objs.color.g = 0.2;
                vehicle_speed_objs.color.b = 1.0;
            }
            else // other white
            {
                xoy_objs.color.r = 1.0;
                xoy_objs.color.g = 1.0;
                xoy_objs.color.b = 1.0;
                xoy_text.color.r = 1.0;
                xoy_text.color.g = 1.0;
                xoy_text.color.b = 1.0;
                vehicle_speed_objs.color.r = 1.0;
                vehicle_speed_objs.color.g = 1.0;
                vehicle_speed_objs.color.b = 1.0;
            }
        }

        geometry_msgs::msg::Point q[2];
        q[0].x = left_down_x;
        q[0].y = left_down_y;
        q[0].z = 0;
        q[1].x = right_down_x;
        q[1].y = right_down_y;
        q[1].z = 0;
        xoy_objs.points.push_back(q[0]);
        xoy_objs.points.push_back(q[1]);
        xoy_objs.id++;
        vehicle_speed_rviz.markers.emplace_back(xoy_objs);
        xoy_objs.points.clear();

        q[0].x = left_down_x;
        q[0].y = left_down_y;
        q[0].z = 0;
        q[1].x = left_down_x + height;
        q[1].y = left_down_y;
        q[1].z = 0;
        xoy_objs.points.push_back(q[0]);
        xoy_objs.points.push_back(q[1]);
        xoy_objs.id++;
        vehicle_speed_rviz.markers.emplace_back(xoy_objs);
        xoy_objs.points.clear();

        float vehicle_speed = 0.0;
        float brake_pedal_status = 0.0;

        if (getValue(topic_name_vehicle_chassis_10ms, topic_ptr_vehicle_chassis_10ms))
        {
            vehicle_speed = topic_ptr_vehicle_chassis_10ms->vehdyn.vehspd.vehspd_kph;
            brake_pedal_status = static_cast<float>(topic_ptr_vehicle_chassis_10ms->brksys.brkpdl.brkpdl_appd_enum);
        }

        if (getValue(topic_name_vehicle_chassis_10ms_zero, topic_ptr_vehicle_chassis_10ms_zero))
        {
            vehicle_speed = topic_ptr_vehicle_chassis_10ms_zero->vehdyn.vehspd.vehspd_kph;
            brake_pedal_status = static_cast<float>(topic_ptr_vehicle_chassis_10ms_zero->brksys.brkpdl.brkpdl_appd_enum);
        }

        if (vehicle_speed >= speed_high_limit)
        {
            vehicle_speed = speed_high_limit;
        }

        vehicle_speed_objs.header.frame_id = "base_link";
        vehicle_speed_objs.ns = "vehicle_speed_objs";
        vehicle_speed_objs.header.stamp = nlibcpp::Time();
        vehicle_speed_objs.action = visualization_msgs::msg::Marker::ADD;
        vehicle_speed_objs.pose.orientation.w = 1.0;
        vehicle_speed_objs.id = 0;
        vehicle_speed_objs.type = visualization_msgs::msg::Marker::POINTS;
        vehicle_speed_objs.scale.x = 0.05;
        vehicle_speed_objs.scale.y = 0.05;
        vehicle_speed_objs.scale.z = 0;
        vehicle_speed_objs.color.r = 1.0;
        vehicle_speed_objs.color.g = 1.0;
        vehicle_speed_objs.color.b = 1.0; // color of the line: white.
        vehicle_speed_objs.color.a = 1.0;

        brake_pedal_objs.header.frame_id = "base_link";
        brake_pedal_objs.ns = "brake_pedal_objs";
        brake_pedal_objs.header.stamp = nlibcpp::Time();
        brake_pedal_objs.action = visualization_msgs::msg::Marker::ADD;
        brake_pedal_objs.pose.orientation.w = 1.0;
        brake_pedal_objs.id = 0;
        brake_pedal_objs.type = visualization_msgs::msg::Marker::POINTS;
        brake_pedal_objs.scale.x = 0.05;
        brake_pedal_objs.scale.y = 0.05;
        brake_pedal_objs.scale.z = 0;
        brake_pedal_objs.color.r = 1.0;
        brake_pedal_objs.color.g = 1.0;
        brake_pedal_objs.color.b = 0.0; // color of the line: white.
        brake_pedal_objs.color.a = 1.0;

        if (is_ptr_pnc_fsm_out_got || is_ptr_pnc_fsm_out_zero_got)
        {
            // 0x0: Off
            // 0x1: Passive
            // 0x2: Standby
            // 0x3: Active
            // 0x4: Standstill_Active
            // 0x5: Standstill_Wait
            // 0x6: Override
            // 0x7: Brake_Only
            // 0x8: Fault
            // 0x9: Reserved
            const auto acc_sts_enum = topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_sts.acc_sts_enum;
            if (acc_sts_enum == 3 || acc_sts_enum == 4 || acc_sts_enum == 5) // active blue
            {
                vehicle_speed_objs.color.r = 20.0 / 255;
                vehicle_speed_objs.color.g = 190.0 / 255;
                vehicle_speed_objs.color.b = 230.0 / 255;
            }
            // else if (topic_ptr_pnc_fsm_out_tmp->drv_signals.acc_status_enum == 2) // standby grey
            // {
            //     vehicle_speed_objs.color.r = 0.5;
            //     vehicle_speed_objs.color.g = 0.5;
            //     vehicle_speed_objs.color.b = 0.5;
            // }
            else if (acc_sts_enum == 6) // override red
            {
                vehicle_speed_objs.color.r = 1.0;
                vehicle_speed_objs.color.g = 0.2;
                vehicle_speed_objs.color.b = 1.0;
            }
            else // other white
            {
                vehicle_speed_objs.color.r = 1.0;
                vehicle_speed_objs.color.g = 1.0;
                vehicle_speed_objs.color.b = 1.0;
            }
        }

        geometry_msgs::msg::Point p;

        const size_t max_speed_count = size_t(width * 100);

        static float size_count_speed = 0;

        if (size_count_speed < max_speed_count)
        {
            p.x = left_down_x + vehicle_speed / speed_high_limit * height;
            p.y = left_down_y - size_count_speed / 100;
            p.z = 0;
            p_list.push_back(p);
            size_count_speed++;
        }
        else
        {
            for (size_t i = 1; i < max_speed_count; ++i)
            {
                p_list[i - 1].x = p_list[i].x;
                p_list[i - 1].z = p_list[i].z;
            }

            p_list[max_speed_count - 1].x = left_down_x + vehicle_speed / speed_high_limit * height;
            p_list[max_speed_count - 1].y = right_down_y;
            p_list[max_speed_count - 1].z = 0;
        }

        geometry_msgs::msg::Point p_brkpdl;

        static float size_count_brkpdl = 0;

        if (size_count_brkpdl < max_speed_count)
        {
            p_brkpdl.x = left_down_x + brake_pedal_status * height * 0.2 + 0.1; // 往X方向偏移0.1，避免被坐标轴覆盖
            p_brkpdl.y = left_down_y - size_count_brkpdl / 100;
            p_brkpdl.z = 0;
            p_brake_pedal_list.push_back(p_brkpdl);
            size_count_brkpdl++;
        }
        else
        {
            for (size_t i = 1; i < max_speed_count; ++i)
            {
                p_brake_pedal_list[i - 1].x = p_brake_pedal_list[i].x;
                p_brake_pedal_list[i - 1].z = p_brake_pedal_list[i].z;
            }

            p_brake_pedal_list[max_speed_count - 1].x = left_down_x + brake_pedal_status * height * 0.2 + 0.1; // 往X方向偏移0.1，避免被坐标轴覆盖
            p_brake_pedal_list[max_speed_count - 1].y = right_down_y;
            p_brake_pedal_list[max_speed_count - 1].z = 0;
        }

        for (size_t k = 0; k < p_list.size(); ++k)
        {
            vehicle_speed_objs.points.push_back(p_list[k]);
        }

        for (size_t k = 0; k < p_list.size(); ++k)
        {
            brake_pedal_objs.points.push_back(p_brake_pedal_list[k]);
        }

        vehicle_speed_rviz.markers.emplace_back(vehicle_speed_objs);
        vehicle_speed_rviz.markers.emplace_back(brake_pedal_objs);
        vehicle_speed_objs.points.clear();
        brake_pedal_objs.points.clear();
        vehicle_speed_objs.id++;
        brake_pedal_objs.id++;

        if (is_driving_mode())
        {
            xoy_text.header.frame_id = "base_link";
            xoy_text.header.stamp = nlibcpp::Time();
            xoy_text.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
            xoy_text.action = visualization_msgs::msg::Marker::ADD;
            xoy_text.id = 0;
            xoy_text.ns = "xoy_text";
            xoy_text.pose.position.x = left_down_x + 50 / speed_high_limit * height;
            xoy_text.pose.position.y = left_down_y + 1; // 字体宽度
            xoy_text.pose.position.z = 0.0;
            xoy_text.pose.orientation.w = 1;
            xoy_text.scale.x = 0.55;
            xoy_text.scale.y = 0.55;
            xoy_text.scale.z = 0.55;
            xoy_text.text = "50km/h";
            vehicle_speed_rviz.markers.emplace_back(xoy_text);
            xoy_text.id++;
            xoy_text.pose.position.x = left_down_x + 100 / speed_high_limit * height;
            xoy_text.pose.position.y = left_down_y + 1; // 字体宽度
            xoy_text.text = "100km/h";
            vehicle_speed_rviz.markers.emplace_back(xoy_text);
            xoy_text.text.clear();
        }
        else
        {
            xoy_text.header.frame_id = "base_link";
            xoy_text.header.stamp = nlibcpp::Time();
            xoy_text.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
            xoy_text.action = visualization_msgs::msg::Marker::ADD;
            xoy_text.id = 0;
            xoy_text.ns = "xoy_text";
            xoy_text.pose.position.x = left_down_x + height;
            xoy_text.pose.position.y = left_down_y + 1; // 字体宽度
            xoy_text.pose.position.z = 0.0;
            xoy_text.pose.orientation.w = 1;
            xoy_text.scale.x = 0.55;
            xoy_text.scale.y = 0.55;
            xoy_text.scale.z = 0.55;
            xoy_text.text = "4.32km/h";
            vehicle_speed_rviz.markers.emplace_back(xoy_text);
            xoy_text.id++;
            xoy_text.text.clear();
        }
        xy_plot_pub->publish(vehicle_speed_rviz);
    }

    void AdapterDebugInfoShowNode::publish_plot_xy_steering_wheel()
    {
        // Left down point
        float left_down_y = -27;
        float left_down_x = -14;
        float width = 7;
        float height = 6;
        static float str_wheel_max_deg = this->config_f_str_wheel_max_deg;
        static float str_wheel_min_deg = this->config_f_str_wheel_min_deg;

        left_down_x = this->config_f_plot_strwheel_xy_down_x;
        left_down_y = this->config_f_plot_strwheel_xy_down_y;
        height = this->config_f_plot_strwheel_xy_height;
        width = this->config_f_plot_strwheel_xy_width;
        float right_down_y = left_down_y - width;
        float right_down_x = left_down_x;

        visualization_msgs::msg::MarkerArray str_wheel_rviz;
        visualization_msgs::msg::Marker str_wheel_objs;
        visualization_msgs::msg::Marker objsDelete;
        objsDelete.type = visualization_msgs::msg::Marker::LINE_STRIP;
        objsDelete.action = visualization_msgs::msg::Marker::DELETEALL;
        str_wheel_rviz.markers.emplace_back(objsDelete);
        visualization_msgs::msg::Marker str_wheel_text;
        visualization_msgs::msg::Marker xoy_text;
        visualization_msgs::msg::Marker xoy_objs;
        xoy_objs.header.frame_id = "base_link";
        xoy_objs.ns = "xoy_objs";
        xoy_objs.header.stamp = nlibcpp::Time();
        xoy_objs.action = visualization_msgs::msg::Marker::ADD;
        xoy_objs.pose.orientation.w = 1.0;
        xoy_objs.id = 0;
        xoy_objs.type = visualization_msgs::msg::Marker::ARROW;
        xoy_objs.scale.x = 0.1;
        xoy_objs.scale.y = 0.1;
        xoy_objs.scale.z = 0;
        xoy_objs.color.r = 1.0;
        xoy_objs.color.g = 1.0;
        xoy_objs.color.b = 1.0; // color of the line: white.
        xoy_objs.color.a = 1.0;
        xoy_text.color.r = 1.0;
        xoy_text.color.g = 1.0;
        xoy_text.color.b = 1.0; // color of the line: white.
        xoy_text.color.a = 1.0;

        if (getValue(topic_name_pnc_fsm_out, topic_ptr_pnc_fsm_out))
        {
            // 0x0: Off
            // 0x1: Passive
            // 0x2: Standby
            // 0x3: Active
            // 0x4: Standstill_Active
            // 0x5: Standstill_Wait
            // 0x6: Override
            // 0x7: Brake_Only
            // 0x8: Fault
            // 0x9: Reserved
            const auto acc_sts_enum = topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_sts.acc_sts_enum;
            if (acc_sts_enum == 3 || acc_sts_enum == 4 || acc_sts_enum == 5) // active blue
            {
                xoy_objs.color.r = 20.0 / 255;
                xoy_objs.color.g = 190.0 / 255;
                xoy_objs.color.b = 230.0 / 255;
                xoy_text.color.r = 20.0 / 255;
                xoy_text.color.g = 190.0 / 255;
                xoy_text.color.b = 230.0 / 255;
                str_wheel_objs.color.r = 20.0 / 255;
                str_wheel_objs.color.g = 190.0 / 255;
                str_wheel_objs.color.b = 230.0 / 255;
            }
            // else if (topic_ptr_pnc_fsm_out->drv_signals.acc_status_enum == 2) // standby grey
            // {
            //     xoy_objs.color.r = 0.5;
            //     xoy_objs.color.g = 0.5;
            //     xoy_objs.color.b = 0.5;
            //     xoy_text.color.r = 0.5;
            //     xoy_text.color.g = 0.5;
            //     xoy_text.color.b = 0.5;
            //     str_wheel_objs.color.r = 0.5;
            //     str_wheel_objs.color.g = 0.5;
            //     str_wheel_objs.color.b = 0.5;
            // }
            else if (acc_sts_enum == 6) // override red
            {
                xoy_objs.color.r = 1.0;
                xoy_objs.color.g = 0.2;
                xoy_objs.color.b = 1.0;
                xoy_text.color.r = 1.0;
                xoy_text.color.g = 0.2;
                xoy_text.color.b = 1.0;
                str_wheel_objs.color.r = 1.0;
                str_wheel_objs.color.g = 0.2;
                str_wheel_objs.color.b = 1.0;
            }
            else // other white
            {
                xoy_objs.color.r = 1.0;
                xoy_objs.color.g = 1.0;
                xoy_objs.color.b = 1.0;
                xoy_text.color.r = 1.0;
                xoy_text.color.g = 1.0;
                xoy_text.color.b = 1.0;
                str_wheel_objs.color.r = 1.0;
                str_wheel_objs.color.g = 1.0;
                str_wheel_objs.color.b = 1.0;
            }
        }

        geometry_msgs::msg::Point q[2];
        q[0].x = left_down_x;
        q[0].y = left_down_y;
        q[0].z = 0;
        q[1].x = right_down_x;
        q[1].y = right_down_y;
        q[1].z = 0;
        xoy_objs.points.push_back(q[0]);
        xoy_objs.points.push_back(q[1]);
        xoy_objs.id++;
        str_wheel_rviz.markers.emplace_back(xoy_objs);
        xoy_objs.points.clear();

        q[0].x = left_down_x;
        q[0].y = left_down_y;
        q[0].z = 0;
        q[1].x = left_down_x + height;
        q[1].y = left_down_y;
        q[1].z = 0;
        xoy_objs.points.push_back(q[0]);
        xoy_objs.points.push_back(q[1]);
        xoy_objs.id++;
        str_wheel_rviz.markers.emplace_back(xoy_objs);
        xoy_objs.points.clear();

        float steering_wheel_angle_deg = 0.0;
        if (getValue(topic_name_vehicle_chassis_10ms, topic_ptr_vehicle_chassis_10ms))
        {
            steering_wheel_angle_deg = topic_ptr_vehicle_chassis_10ms->strsys.pnnagsae_deg;
        }

        if (getValue(topic_name_vehicle_chassis_10ms_zero, topic_ptr_vehicle_chassis_10ms_zero))
        {
            steering_wheel_angle_deg = topic_ptr_vehicle_chassis_10ms_zero->strsys.pnnagsae_deg;
        }

        // float str_wheel_max_edge_deg = (static_cast<int>(steering_wheel_angle_deg/this->config_f_str_wheel_change_gap_deg) + 2) * this->config_f_str_wheel_change_gap_deg;
        // float str_wheel_min_edge_deg = (static_cast<int>(steering_wheel_angle_deg/this->config_f_str_wheel_change_gap_deg) - 2) * this->config_f_str_wheel_change_gap_deg;

        if (steering_wheel_angle_deg >= str_wheel_max_deg)
        {
            steering_wheel_angle_deg = str_wheel_max_deg;
        }
        else if (steering_wheel_angle_deg <= str_wheel_min_deg)
        {
            steering_wheel_angle_deg = str_wheel_min_deg;
        }
        else
        {
            str_wheel_max_deg = str_wheel_max_deg;
        }

        // str_wheel_max_deg = str_wheel_max_edge_deg + this->config_f_str_wheel_change_gap_deg;
        // str_wheel_min_deg = str_wheel_min_edge_deg - this->config_f_str_wheel_change_gap_deg;

        str_wheel_objs.header.frame_id = "base_link";
        str_wheel_objs.ns = "str_wheel_objs";
        str_wheel_objs.header.stamp = nlibcpp::Time();
        str_wheel_objs.action = visualization_msgs::msg::Marker::ADD;
        str_wheel_objs.pose.orientation.w = 1.0;
        str_wheel_objs.id = 0;
        str_wheel_objs.type = visualization_msgs::msg::Marker::LINE_STRIP;
        str_wheel_objs.scale.x = 0.05;
        str_wheel_objs.scale.y = 0.05;
        str_wheel_objs.scale.z = 0;
        str_wheel_objs.color.r = 1.0;
        str_wheel_objs.color.g = 1.0;
        str_wheel_objs.color.b = 1.0; // color of the line: white.
        str_wheel_objs.color.a = 1.0;

        if (getValue(topic_name_pnc_fsm_out, topic_ptr_pnc_fsm_out))
        {
            // 0x0: Off
            // 0x1: Passive
            // 0x2: Standby
            // 0x3: Active
            // 0x4: Standstill_Active
            // 0x5: Standstill_Wait
            // 0x6: Override
            // 0x7: Brake_Only
            // 0x8: Fault
            // 0x9: Reserved
            const auto acc_sts_enum = topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_sts.acc_sts_enum;
            if (acc_sts_enum == 3 || acc_sts_enum == 4 || acc_sts_enum == 5) // active blue
            {
                str_wheel_objs.color.r = 20.0 / 255;
                str_wheel_objs.color.g = 190.0 / 255;
                str_wheel_objs.color.b = 230.0 / 255;
            }
            // else if (topic_ptr_pnc_fsm_out->drv_signals.acc_status_enum == 2) // standby grey
            // {
            //     str_wheel_objs.color.r = 0.5;
            //     str_wheel_objs.color.g = 0.5;
            //     str_wheel_objs.color.b = 0.5;
            // }
            else if (acc_sts_enum == 6) // override red
            {
                str_wheel_objs.color.r = 1.0;
                str_wheel_objs.color.g = 0.2;
                str_wheel_objs.color.b = 1.0;
            }
            else // other white
            {
                str_wheel_objs.color.r = 1.0;
                str_wheel_objs.color.g = 1.0;
                str_wheel_objs.color.b = 1.0;
            }
        }

        geometry_msgs::msg::Point p;

        const size_t max_speed_count = size_t(width * 10);

        static float size_count_speed = 0;

        if (size_count_speed < max_speed_count)
        {
            p.x = left_down_x + (steering_wheel_angle_deg - str_wheel_min_deg) / (str_wheel_max_deg - str_wheel_min_deg) * height;
            p.y = left_down_y - size_count_speed / 10;
            p.z = 0;
            p_str_wheel_list.push_back(p);
            size_count_speed++;
        }
        else
        {
            for (size_t i = 1; i < max_speed_count; ++i)
            {
                p_str_wheel_list[i - 1].x = p_str_wheel_list[i].x;
                p_str_wheel_list[i - 1].z = p_str_wheel_list[i].z;
            }

            p_str_wheel_list[max_speed_count - 1].x = left_down_x + (steering_wheel_angle_deg - str_wheel_min_deg) / (str_wheel_max_deg - str_wheel_min_deg) * height;
            p_str_wheel_list[max_speed_count - 1].y = right_down_y;
            p_str_wheel_list[max_speed_count - 1].z = 0;
        }

        for (size_t k = 0; k < p_str_wheel_list.size(); ++k)
        {
            str_wheel_objs.points.push_back(p_str_wheel_list[k]);
        }

        str_wheel_rviz.markers.emplace_back(str_wheel_objs);
        str_wheel_objs.points.clear();
        str_wheel_objs.id++;

        std::ostringstream ostr_up, ostr_down, ostr_mid;
        ostr_up << std::fixed << std::setprecision(1);
        ostr_down << std::fixed << std::setprecision(1);
        xoy_text.header.frame_id = "base_link";
        xoy_text.header.stamp = nlibcpp::Time();
        xoy_text.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
        xoy_text.action = visualization_msgs::msg::Marker::ADD;
        xoy_text.id = 0;
        xoy_text.ns = "xoy_text";
        xoy_text.pose.position.x = left_down_x + height;
        xoy_text.pose.position.y = left_down_y + 1; // 字体宽度
        xoy_text.pose.position.z = 0.0;
        xoy_text.pose.orientation.w = 1;
        xoy_text.scale.x = 0.55;
        xoy_text.scale.y = 0.55;
        xoy_text.scale.z = 0.55;
        ostr_up << str_wheel_max_deg << "deg";
        xoy_text.text = ostr_up.str();
        str_wheel_rviz.markers.emplace_back(xoy_text);
        xoy_text.id++;
        xoy_text.pose.position.x = left_down_x;
        xoy_text.pose.position.y = left_down_y + 1; // 字体宽度
        ostr_down << str_wheel_min_deg << "deg";
        xoy_text.text = ostr_down.str();
        str_wheel_rviz.markers.emplace_back(xoy_text);
        xoy_text.id++;
        xoy_text.pose.position.x = left_down_x + height / 2.0;
        xoy_text.pose.position.y = left_down_y + 1; // 字体宽度
        ostr_mid << ((str_wheel_min_deg + str_wheel_max_deg) / 2.0) << "deg";
        xoy_text.text = ostr_mid.str();
        str_wheel_rviz.markers.emplace_back(xoy_text);
        xoy_text.text.clear();

        xy_steering_wheel_plot_pub->publish(str_wheel_rviz);
    }

    bool AdapterDebugInfoShowNode::initialize_nex_sub_pub()
    {
        std::cout << "start initialize nex sub pub" << std::endl;
        // =============== Pub topics =============== //
        alg_debug_info_right_up_pub = this->create_publisher<nviz_2d_overlay_msgs::msg::OverlayText>("/debug_info_text_right_up", 10);
        alg_debug_info_mid_up_pub = this->create_publisher<nviz_2d_overlay_msgs::msg::OverlayText>("/debug_info_text_mid_up", 10);
        alg_debug_info_left_pub = this->create_publisher<nviz_2d_overlay_msgs::msg::OverlayText>("/debug_info_text_left", 10);
        alg_debug_info_error_pub = this->create_publisher<nviz_2d_overlay_msgs::msg::OverlayText>("/debug_info_error_text", 10);
        alg_debug_traffic_light_pub = this->create_publisher<nviz_2d_overlay_msgs::msg::OverlayText>("/debug_tlr_txt", 10);
        alg_e2e_traffic_light_pub = this->create_publisher<nviz_2d_overlay_msgs::msg::OverlayText>("/debug_tlr_e2e", 10);
        driver_wheel_circle_pub = this->create_publisher<visualization_msgs::msg::MarkerArray>("/viz/steeringWheelAngle", 1);
        xy_plot_pub = this->create_publisher<visualization_msgs::msg::MarkerArray>("/viz/xyTimeSeqPlot", 1);
        xy_steering_wheel_plot_pub = this->create_publisher<visualization_msgs::msg::MarkerArray>("/viz/xyStrWheelPlot", 1);
        alg_prk_panel_sts_pub =  this->create_publisher<nviz_2d_overlay_msgs::msg::OverlayText>("/debug_info_prk_sts_text", 10);
        park_status_box_pub = this->create_publisher<visualization_msgs::msg::MarkerArray>("/park_status_box", 1);
        alg_fsm_out_pub = this->create_publisher<nviz_2d_overlay_msgs::msg::OverlayText>("/debug_fsm_out_text", 10);
        alg_debug_info_left_pub_second = this->create_publisher<nviz_2d_overlay_msgs::msg::OverlayText>("/debug_info_text_left_second", 10);
        std::cout << "end initialize nex pub" << std::endl;

        // Sensor
        createSubscription<p_msgs::msg::P10Hz>(topic_name_sensor_pos_p10hz);

        createSubscription<p_msgs::msg::P100Hz>(topic_name_sensor_pos_p100hz_zero);
        createSubscription<p_msgs::msg::P100Hz>(topic_name_sensor_pos_p100hz);

        createSubscription<p_msgs::msg::IMU>(topic_name_sensor_pos_imuinfo_zero);
        createSubscription<p_msgs::msg::IMU>(topic_name_sensor_pos_imuinfo);

        createSubscription<uss_msgs::msg::UssOriginalInfo>(topic_name_uss_origin_zero);
        createSubscription<uss_msgs::msg::UssOriginalInfo>(topic_name_uss_origin);

        createSubscription<camera_msgs::msg::CameraYUV1M>(topic_name_vision_bev_stitch_yuv_zero);

        // localization
        createSubscription<loc_msgs::msg::LocalizationPosition>(topic_name_alg_loc_absolute_position);

        createSubscription<loc_msgs::msg::LocPose>(topic_name_alg_loc_loc_pose_zero);
        createSubscription<loc_msgs::msg::LocPose>(topic_name_alg_loc_loc_pose);

        createSubscription<loc_msgs::msg::LocMapInfo>(topic_name_alg_loc_loc_map_info_zero);
        createSubscription<loc_msgs::msg::LocMapInfo>(topic_name_alg_loc_loc_map_info);

        createSubscription<loc_msgs::msg::Odometry>(topic_name_alg_loc_dr_zero);
        createSubscription<loc_msgs::msg::Odometry>(topic_name_alg_loc_dr);

        createSubscription<loc_msgs::msg::MapData>(topic_name_alg_map_raw_map_data);

        // fusion road
        createSubscription<fusion_msgs::msg::Road>(topic_name_fusion_road);
        // fusion monomap
        createSubscription<fusion_msgs::msg::Road>(topic_name_fusion_monomap);
        // fusion road debug
        createSubscription<debug_msgs::msg::DebugInfo>(topic_name_fusion_road_debug);

        // uniinfer adapter
        createSubscription<std_msgs::msg::UInt64>(topic_name_adapter_f120);
        createSubscription<std_msgs::msg::UInt64>(topic_name_adapter_mv);
        createSubscription<std_msgs::msg::UInt64>(topic_name_adapter_mvf);
        createSubscription<std_msgs::msg::UInt64>(topic_name_adapter_parking);

        // uniinfer result
        // mono
        createSubscription<std_msgs::msg::UInt64>(topic_name_uniinfer_tsr);
        createSubscription<std_msgs::msg::UInt64>(topic_name_uniinfer_tlr);
        createSubscription<std_msgs::msg::UInt64>(topic_name_uniinfer_full_obj);
        createSubscription<std_msgs::msg::UInt64>(topic_name_uniinfer_lm);
        createSubscription<std_msgs::msg::UInt64>(topic_name_uniinfer_bev_lane);
        createSubscription<std_msgs::msg::UInt64>(topic_name_uniinfer_pv_lane);
        createSubscription<std_msgs::msg::UInt64>(topic_name_uniinfer_cone_obj);
        // mv
        createSubscription<std_msgs::msg::UInt64>(topic_name_uniinfer_mvod);
        // mv-fisheye
        createSubscription<std_msgs::msg::UInt64>(topic_name_uniinfer_mvfod);
        // pld
        createSubscription<std_msgs::msg::UInt64>(topic_name_uniinfer_pld);
        // pmap
        createSubscription<std_msgs::msg::UInt64>(topic_name_uniinfer_pmap);
        // superpoint
        createSubscription<std_msgs::msg::UInt64>(topic_name_uniinfer_superpoint);

        // vision object
        createSubscription<vision_msgs::msg::CommonStruct>(topic_name_vision_monood_objects);
        createSubscription<vision_msgs::msg::CommonStruct>(topic_name_vision_mvod_objects);

        createSubscription<vision_msgs::msg::PerceptionObjects>(topic_name_vision_fusion_objects_zero);
        createSubscription<vision_msgs::msg::PerceptionObjects>(topic_name_vision_fusion_objects);

        // radar object
        createSubscription<radar_msgs::msg::LongRangeRadarObjects>(topic_name_front_radar_objects);

        // fusion object
        createSubscription<fusion_msgs::msg::FusionObjects>(topic_name_fusion_objects_zero);
        createSubscription<fusion_msgs::msg::FusionObjects>(topic_name_fusion_objects);

        createSubscription<fusion_msgs::msg::FusionObjects>(topic_name_fusion_objects_prediction_zero);
        createSubscription<fusion_msgs::msg::FusionObjects>(topic_name_fusion_objects_prediction);

        // fusion traffic light
        createSubscription<fusion_msgs::msg::TrafficLights>(topic_name_fusion_traffic_light);

        // e2e traffic light
        createSubscription<vision_msgs::msg::CommonStruct>(topic_name_e2e_traffic_light);

        // pnc highway
        createSubscription<fsm_msgs::msg::FSMOut>(topic_name_pnc_fsm_out_zero);
        createSubscription<fsm_msgs::msg::FSMOut>(topic_name_pnc_fsm_out);

        createSubscription<planning_msgs::msg::PncMap>(topic_name_pnc_infer_result_zero);
        createSubscription<planning_msgs::msg::PncMap>(topic_name_pnc_infer_result);

        createSubscription<planning_msgs::msg::DeciderResult>(topic_name_pnc_decider_result_zero);
        createSubscription<planning_msgs::msg::DeciderResult>(topic_name_pnc_decider_result);

        createSubscription<planning_msgs::msg::TrajectoryOut>(topic_name_pnc_trajectory_out_zero);
        createSubscription<planning_msgs::msg::TrajectoryOut>(topic_name_pnc_trajectory_out);

        createSubscription<planning_msgs::msg::TrajectoryOut>(topic_name_prk_trajectory_out_zero);
        createSubscription<planning_msgs::msg::TrajectoryOut>(topic_name_prk_trajectory_out);

        createSubscription<debug_msgs::msg::DebugPlanInfo>(topic_name_pnc_debug_plan_out);

        createSubscription<control_msgs::msg::ControlOut>(topic_name_pnc_control_out_zero);
        createSubscription<control_msgs::msg::ControlOut>(topic_name_pnc_control_out);

        createSubscription<router_msgs::msg::RouterOut>(topic_name_pnc_router_out);

        createSubscription<debug_msgs::msg::DebugInfo>(topic_name_pnc_debug_out);

        // vehicle signal
        createSubscription<vehicle_msgs::msg::VehicleChassis>(topic_name_vehicle_chassis_10ms_zero);
        createSubscription<vehicle_msgs::msg::VehicleChassis>(topic_name_vehicle_chassis_10ms);

        createSubscription<vehicle_msgs::msg::VehicleStatus>(topic_name_vehicle_status_50ms_zero);
        createSubscription<vehicle_msgs::msg::VehicleStatus>(topic_name_vehicle_status_50ms);

        createSubscription<vehicle_msgs::msg::VehicleControllerStatus>(topic_name_controller_status_10ms_zero);
        createSubscription<vehicle_msgs::msg::VehicleControllerStatus>(topic_name_controller_status_10ms);

        // version
        createSubscription<std_msgs::msg::String>(topic_name_node_version);
        createSubscription<parameter_info_msgs::msg::ParameterString>(topic_name_vehicle_info);
        createSubscription<std_msgs::msg::String>(topic_name_parameter_vehicle);

        // health monitor
        createSubscription<diag_msgs::msg::DiagSystemInfo>(topic_name_health_monitor_info);

        // sd map navi path
        createSubscription<navi_msgs::msg::PathInfo>(topic_name_map_navi_path);
        createSubscription<navi_msgs::msg::PathInfo>(topic_name_map_sd_debug);

        // hmi
        createSubscription<hmi_msgs::msg::HMIRequest>(topic_name_hmi_hmi_req);

        // safety
        createSubscription<safety_msgs::msg::SafetyCtrlOut>(topic_name_safetyctrl_out);
        // Avp Show
        createSubscription<loc_msgs::msg::MapState>(topic_name_map_state);

        //MapEngine
        createSubscription<std_msgs::msg::UInt64>(topic_name_map_heart_beat);
        createSubscription<loc_msgs::msg::MapEngineInfo>(topic_name_map_engine_info);

        // viz
        viz_loc_pose_pub_ = this->create_publisher<loc_msgs::msg::LocPose>("/viz/loc_pose",
            nlibcpp::QoS(5).best_effort());

        using namespace std::chrono_literals;
        timer = this->create_wall_timer(100ms, [this]() -> void
                                        { publish_debug_info(); });

        _is_node_initialized = true;
        std::cout << "initialize nex sub pub : success" << std::endl;
        return _is_node_initialized;
    }

    void AdapterDebugInfoShowNode::find_alg_node_version_info(const std::string &input_str)
    {

        size_t found = input_str.find(":");
        if (found == std::string::npos)
        {
            std::cout << "Separator not found" << std::endl;
            return;
        }

        std::string node_name_str, version_str;
        std::istringstream ss(input_str);
        std::getline(ss, node_name_str, ':');
        std::getline(ss, version_str);
        node_version_map[node_name_str] = version_str;
    }


    std::string AdapterDebugInfoShowNode::get_alg_node_version(const std::string& node_name)
    {
        return node_version_map[node_name];
    }

    void AdapterDebugInfoShowNode::extractLaneData(const std::vector<int> &back_lane_data, const std::vector<int> &front_lane_data,
                                                   std::stringstream &stream)
    {
        if (back_lane_data.size())
        {
            stream << ADD_TEXT_COLOR_LEFT() << " | " << color3;
        }
        for (size_t i = 0; i < back_lane_data.size(); i++)
        {
            for (auto backenum : GAODEorignlaneconvert[back_lane_data[i]])
            {
                for (auto frontenum : GAODEorignlaneconvert[front_lane_data[i]])
                {
                    if (laneshow[backenum] == laneshow[frontenum])
                    {
                        stream << ADD_TEXT_COLOR_LEFT() << laneshow[backenum] << color_blue;
                    }
                    else
                    {
                        stream << laneshow[backenum];
                    }
                }
            }
            stream << ADD_TEXT_COLOR_LEFT() << " | " << color3;
        }
        // if(back_lane_data.size())
        // {
        //     stream<<std::endl;
        // }
    }

    bool AdapterDebugInfoShowNode::read_config_file(const std::string &configFile, const std::string &modeName)
    {
        auto yaml_node = YAML::LoadFile(configFile);
        const YAML::Node &debug_config_node = yaml_node["debug_config"];
        if (debug_config_node)
        {
            config_str_nex_msgs_version = debug_config_node["nex_msgs_version"].as<std::string>();
            config_f_vision_decode_normal_time_offset_ms = debug_config_node["vision_decode_normal_time_offset_ms"].as<float>();
            config_f_vision_decode_normal_delay_time_ms = debug_config_node["vision_decode_normal_delay_time_ms"].as<float>();
            config_f_vision_decode_normal_delay_max_count = debug_config_node["vision_decode_normal_delay_max_count"].as<float>();
            config_f_ipu_ipc_delay_time_s = debug_config_node["ipu_ipc_delay_time_s"].as<float>();
            const auto &node = debug_config_node[modeName];
            if (node)
            {
                config_str_mode = node["mode"].as<std::string>();
                config_b_log_output = node["log_output"].as<bool>();
                config_b_steering_wheel_display = node["steering_wheel_display"].as<bool>();
                config_f_steering_wheel_center_x = node["steering_wheel_center_x"].as<float>();
                config_f_steering_wheel_center_y = node["steering_wheel_center_y"].as<float>();
                config_f_steering_wheel_radius = node["steering_wheel_radius"].as<float>();
                config_f_plot_xy_left_down_x = node["plot_xy_left_down_x"].as<float>();
                config_f_plot_xy_left_down_y = node["plot_xy_left_down_y"].as<float>();
                config_f_plot_xy_height = node["plot_xy_height"].as<float>();
                config_f_plot_xy_width = node["plot_xy_width"].as<float>();
                config_f_plot_strwheel_xy_down_x = node["plot_strwheel_xy_down_x"].as<float>();
                config_f_plot_strwheel_xy_down_y = node["plot_strwheel_xy_down_y"].as<float>();
                config_f_plot_strwheel_xy_height = node["plot_strwheel_xy_height"].as<float>();
                config_f_plot_strwheel_xy_width = node["plot_strwheel_xy_width"].as<float>();
                config_f_str_wheel_max_deg = node["str_wheel_max_deg"].as<float>();
                config_f_str_wheel_min_deg = node["str_wheel_min_deg"].as<float>();
                config_f_str_wheel_change_gap_deg = node["str_wheel_change_gap_deg"].as<float>();
                config_b_diag_info_show = node["diag_info_show"].as<bool>();
                config_b_vehicle_status_50ms_show = node["vehicle_status_50ms_show"].as<bool>();
                config_b_vehicle_chassis_10ms_show = node["vehicle_chassis_10ms_show"].as<bool>();
                config_b_vehicle_controller_status_10ms_show = node["vehicle_controller_status_10ms_show"].as<bool>();
                config_b_sensor_p10hz_show = node["sensor_p10hz_show"].as<bool>();
                config_b_sensor_p100hz_show = node["sensor_p100hz_show"].as<bool>();
                config_b_sensor_imu_show = node["sensor_imu_show"].as<bool>();
                config_b_map_and_localization_show = node["map_and_localization_show"].as<bool>();
                config_b_loc_dr_show = node["loc_dr_show"].as<bool>();
                config_b_uniinfer_delaytime_show = node["uniinfer_delaytime_show"].as<bool>();
                config_b_vision_fusion_show = node["vision_fusion_show"].as<bool>();
                config_b_fusion_od_show = node["fusion_od_show"].as<bool>();
                config_b_fusion_od_pred_show = node["fusion_od_pred_show"].as<bool>();
                config_b_fusion_road_show = node["fusion_road_show"].as<bool>();
                config_b_pnc_router_show = node["pnc_router_show"].as<bool>();
                config_b_pnc_decider_show = node["pnc_decider_show"].as<bool>();
                config_b_pnc_planner_show = node["pnc_planner_show"].as<bool>();
                config_b_pnc_control_show = node["pnc_control_show"].as<bool>();
                config_b_pnc_fsm_show = node["pnc_fsm_show"].as<bool>();
                config_b_pnc_debug_show = node["pnc_debug_show"].as<bool>();
                config_b_trigger_event_show = node["trigger_event_show"].as<bool>();
                if(node["box_pos_x"] && node["box_pos_y"] && node["box_pos_z"] ){
                    config_p_box_pos_x = node["box_pos_x"].as<float>();
                    config_p_box_pos_y = node["box_pos_y"].as<float>();
                    config_p_box_pos_z = node["box_pos_z"].as<float>();
                }
            }
            else
            {
                std::cerr << "not found the mode '" << modeName << "' in config file" << std::endl;
                exit(-1);
            }
        }
        else
        {
            std::cerr << "yaml file not correct" << std::endl;
            exit(-1);
        }

        std::cout << "load config mode : " << config_str_mode << std::endl;
        std::cout << "load config log_output : " << int(config_b_log_output) << std::endl;
        std::cout << "load config steering_wheel_display : " << int(config_b_steering_wheel_display) << std::endl;
        std::cout << "load config steering_wheel_center_x : " << config_f_steering_wheel_center_x << std::endl;
        std::cout << "load config steering_wheel_center_y : " << config_f_steering_wheel_center_y << std::endl;
        std::cout << "load config steering_wheel_radius : " << config_f_steering_wheel_radius << std::endl;
        std::cout << "load config plot_xy_left_down_x : " << config_f_plot_xy_left_down_x << std::endl;
        std::cout << "load config plot_xy_left_down_y : " << config_f_plot_xy_left_down_y << std::endl;
        std::cout << "load config plot_xy_height : " << config_f_plot_xy_height << std::endl;
        std::cout << "load config plot_xy_width : " << config_f_plot_xy_width << std::endl;
        std::cout << "load config config_b_diag_info_show :" << config_b_diag_info_show << std::endl;
        std::cout << "load config config_b_vehicle_status_50ms_show :" << config_b_vehicle_status_50ms_show << std::endl;
        std::cout << "load config config_b_vehicle_chassis_10ms_show :" << config_b_vehicle_chassis_10ms_show << std::endl;
        std::cout << "load config config_b_vehicle_controller_status_10ms_show :" << config_b_vehicle_controller_status_10ms_show << std::endl;
        std::cout << "load config config_b_sensor_p10hz_show :" << config_b_sensor_p10hz_show << std::endl;
        std::cout << "load config config_b_sensor_p100hz_show :" << config_b_sensor_p100hz_show << std::endl;
        std::cout << "load config config_b_sensor_imu_show :" << config_b_sensor_imu_show << std::endl;
        std::cout << "load config config_b_map_and_localization_show :" << config_b_map_and_localization_show << std::endl;
        std::cout << "load config config_b_loc_dr_show :" << config_b_loc_dr_show << std::endl;
        std::cout << "load config config_b_uniinfer_delaytime_show :" << config_b_uniinfer_delaytime_show << std::endl;
        std::cout << "load config config_b_vision_fusion_show :" << config_b_vision_fusion_show << std::endl;
        std::cout << "load config config_b_fusion_od_show :" << config_b_fusion_od_show << std::endl;
        std::cout << "load config config_b_fusion_od_pred_show :" << config_b_fusion_od_pred_show << std::endl;
        std::cout << "load config config_b_fusion_road_show :" << config_b_fusion_road_show << std::endl;
        std::cout << "load config config_b_pnc_router_show :" << config_b_pnc_router_show << std::endl;
        std::cout << "load config config_b_pnc_decider_show :" << config_b_pnc_decider_show << std::endl;
        std::cout << "load config config_b_pnc_planner_show :" << config_b_pnc_planner_show << std::endl;
        std::cout << "load config config_b_pnc_control_show :" << config_b_pnc_control_show << std::endl;
        std::cout << "load config config_b_pnc_fsm_show :" << config_b_pnc_fsm_show << std::endl;
        std::cout << "load config config_b_pnc_debug_show :" << config_b_pnc_debug_show << std::endl;
        std::cout << "load config config_b_trigger_event_show :" << config_b_trigger_event_show << std::endl;
        return true;
    }

    void AdapterDebugInfoShowNode::AddFixedLocalMapLegend(std::stringstream& stream) {
        if(is_driving_mode()) {
            return;
        }

        stream << ADD_TEXT_COLOR_LEFT() << "=== FusLocalmap " << get_alg_node_version("alg_fusion_localmap")
                    << " | ParkingElem " << get_alg_node_version("alg_fusion_parking_elements") << " ===" << color_green << std::endl;

        static std::string s;
        if(s.empty()) {
            std::stringstream ss;
            ss
            << ADD_TEXT_COLOR_LEFT() << "USSObj" << ADD_TEXT_COLOR_RIGHT(0.5647, 0.6784, 0.0118, 1.0) << " | "
            << ADD_TEXT_COLOR_LEFT() << "BuildingE" << ADD_TEXT_COLOR_RIGHT(1.0, 1.0, 0, 1.0) << " | "
            << ADD_TEXT_COLOR_LEFT() << "URoadE" << ADD_TEXT_COLOR_RIGHT(0.5882, 0.5882, 0.5882, 1.0) << " | "
            << ADD_TEXT_COLOR_LEFT() << "LRoadE" << ADD_TEXT_COLOR_RIGHT(0.8706, 0.4784, 0.0118, 1.0) << " | "
            << ADD_TEXT_COLOR_LEFT() << "HRoadE" << ADD_TEXT_COLOR_RIGHT(0.5451, 0.2706, 0.0745, 1.0) << " | "
            << ADD_TEXT_COLOR_LEFT() << "PlantE" << ADD_TEXT_COLOR_RIGHT(0, 0.7725, 0.8039, 1.0) << " | "
            << "\n"
            << ADD_TEXT_COLOR_LEFT() << "Plot3DE" << ADD_TEXT_COLOR_RIGHT(1.0, 0.8549, 0.7255, 1.0) << " | "
            << ADD_TEXT_COLOR_LEFT() << "VehE" << ADD_TEXT_COLOR_RIGHT(1.0, 0, 0, 1.0) << " | "
            << ADD_TEXT_COLOR_LEFT() << "NonVehE" << ADD_TEXT_COLOR_RIGHT(1.0, 0.4118, 0.7059, 1.0) << " | "
            << ADD_TEXT_COLOR_LEFT() << "PedE" << ADD_TEXT_COLOR_RIGHT(0.5804, 0, 0.8275, 1.0) << " | "
            << ADD_TEXT_COLOR_LEFT() << "GeOE" << ADD_TEXT_COLOR_RIGHT(0, 0, 1.0, 1.0) << " | "
            << ADD_TEXT_COLOR_LEFT() << "ChockE" << ADD_TEXT_COLOR_RIGHT(0, 0.9608, 1.0, 1.0)
            << std::endl;
            s = ss.str();
        }
        stream << s;
    }

    void AdapterDebugInfoShowNode::AddUssDiagInfo(std::stringstream& stream) {
        TopicInfo topic_info;
        topic_info.head_name = "UssOriginal";
        topic_info.topic_name = topic_name_uss_origin;
        topic_info.node_name = "";
        std::shared_ptr<uss_msgs::msg::UssOriginalInfo const> msg;
        InitTopicHeader(topic_info, msg);
        bool front_ok = true, rear_ok = true;
        if(!msg) {
            front_ok = false;
            rear_ok = false;
            return; // no output when no msg
        } else {
            constexpr size_t group_count = 6;
            const auto& uss_diag_monitor_list = msg->uss_diag_info.uss_diag_monitor_list;
            for(size_t i = 0; (i < uss_diag_monitor_list.size() && i < group_count); ++i) {
                if(uss_diag_monitor_list.at(i).diaginfo_bits) {
                    front_ok = false;
                    break;
                }
            }
            for(size_t i = group_count; i < uss_diag_monitor_list.size(); ++i) {
                if(uss_diag_monitor_list.at(i).diaginfo_bits) {
                    rear_ok = false;
                    break;
                }
            }
        }

        std::string color = color_red, info;
        if(!front_ok && rear_ok) {
            info = "Front Error";
            color = color_red;  // maybe no need.
        } else if(front_ok && !rear_ok) {
            info = "Rear Error";
            color = color_red;  // maybe no need.
        } else if(!front_ok && !rear_ok) {
            info = "Front and Read Error";
            color = color_red;  // maybe no need.
        } else {
            info = "Uss Normal";
            color = color_default;
        }
        stream << ADD_TEXT_COLOR_LEFT() << "USS status:" << info << color << std::endl;
    }

    void AdapterDebugInfoShowNode::AlgVisionFisheyeStitch(std::stringstream& stream) {
        if(!alg_nviz_utils::CustomEnv::IsParking()) {
            return;
        }
        TopicInfo topic_info;
        topic_info.head_name = "FisheyesStitch";
        topic_info.topic_name = topic_name_vision_bev_stitch_yuv_zero;
        topic_info.node_name = "alg_vision_fisheyes_stitch";
        camera_msgs::msg::CameraYUV1M::ConstSharedPtr msg;
        auto&& str = InitTopicHeader(topic_info, msg);
        stream << str << std::endl;
    }

    void AdapterDebugInfoShowNode::AlgPncDebugPlanOutAdapter(std::stringstream& stream) {
        if(!getValue(topic_name_pnc_debug_plan_out, topic_ptr_pnc_debug_plan_out)) {
            return;
        }
        //
        const auto& msg = topic_ptr_pnc_debug_plan_out;
        struct Info {
            std::string name;
            std::string name_viz;

            Info(const std::string& n1, const char* n2) {
                name = n1;
                name_viz = n2;
            }
        };

        static const Info enable_stp = { "enable_stp", "STP" };
        static const std::vector<std::vector<Info>> names = {
            {enable_stp,
             {"final_opt_index", "Idx"},
             {"is_large_curve", "LargeK"},
             {"traj_candidate_num", "TrajN"},
             {"start_point_deviation", "LatDev"}},
            {{"planner_overall_time", "T_all"},
             {"spt_time", "T_spt"},
             {"speed_planner_time", "T_spd"},
             {"ref_unstable", "R_uns"}},
            {{"traj_0_vel", "T0V"},
             {"traj_0_res", "R"},
             {"traj_0_score", "Sco"},
             {"traj_0_overlap_id", "OL"},
             {"traj_0_centri_a", "Cen"},
             {"traj_0_time", "t"}},
            {{"traj_1_vel", "T1V"},
             {"traj_1_res", "R"},
             {"traj_1_score", "Sco"},
             {"traj_1_overlap_id", "OL"},
             {"traj_1_centri_a", "Cen"},
             {"traj_1_time", "t"}},
            {{"traj_2_vel", "T2V"},
             {"traj_2_res", "R"},
             {"traj_2_score", "Sco"},
             {"traj_2_overlap_id", "OL"},
             {"traj_2_centri_a", "Cen"},
             {"traj_2_time", "t"}},
        };

        std::unordered_map<std::string, float> signals;
        for(const auto& one : msg->debug_plan_info) {
            signals.emplace(one.signal_name, one.value);
        }
        auto IsEqualFloat = [](float s, float t) -> bool {
            return std::fabs(s - t) < std::numeric_limits<float>::epsilon();
        };
        auto it = signals.find(enable_stp.name);
        if (signals.end() == it) {
            return;
        } else if(!IsEqualFloat(it->second, 1.0f)) {
            stream << enable_stp.name_viz << " : " << it->second << std::endl;
            return;
        } else {
            // nothing.
        }

        for(const auto& row : names) {
            auto row_size = row.size();
            for(size_t i = 0; i < row_size; ++i) {
                const auto& one = row.at(i);
                stream << one.name_viz << " : ";
                auto it = signals.find(one.name);
                if(signals.end() != it) {
                    stream << it->second;
                }
                if(i + 1 != row_size) {
                    stream << " | ";
                }
            }
            stream << std::endl;
        }
    }
    void AdapterDebugInfoShowNode::PubText (nlibcpp::Publisher<nviz_2d_overlay_msgs::msg::OverlayText>::SharedPtr pub, const std::stringstream& ss) {
        nviz_2d_overlay_msgs::msg::OverlayText msg;
        msg.action = nviz_2d_overlay_msgs::msg::OverlayText::ADD;
        msg.text = ss.str();
        pub->publish(std::move(msg));
    }
    uint16_t AdapterDebugInfoShowNode::getFsmMode(){
        float pnc_fsm_out_frequency = 0.0;
        GetValueFromTopic(topic_name_pnc_fsm_out, topic_name_pnc_fsm_out_zero, topic_ptr_pnc_fsm_out, topic_ptr_pnc_fsm_out_zero, topic_ptr_pnc_fsm_out_tmp,
            is_ptr_pnc_fsm_out_got, is_ptr_pnc_fsm_out_zero_got, pnc_fsm_out_frequency);
        if(is_ptr_pnc_fsm_out_got || is_ptr_pnc_fsm_out_zero_got){
            auto fsm_mode = topic_ptr_pnc_fsm_out_tmp->drv_prk_dual_info_enum;
            return fsm_mode;
        }
        else{
            return 0x1;
        }
    }
    bool AdapterDebugInfoShowNode::is_driving_mode(){
        float pnc_fsm_out_frequency = 0.0;
        GetValueFromTopic(topic_name_pnc_fsm_out, topic_name_pnc_fsm_out_zero, topic_ptr_pnc_fsm_out, topic_ptr_pnc_fsm_out_zero, topic_ptr_pnc_fsm_out_tmp,
            is_ptr_pnc_fsm_out_got, is_ptr_pnc_fsm_out_zero_got, pnc_fsm_out_frequency);
        if(is_ptr_pnc_fsm_out_got || is_ptr_pnc_fsm_out_zero_got){
            if(0x2 == topic_ptr_pnc_fsm_out_tmp->drv_prk_dual_info_enum){
                return false;
            }else{
                return true;
            }
        }else{
            return true;
        }
    }
    void AdapterDebugInfoShowNode::AlgTlrPostpro(std::stringstream& stream) {
        TopicInfo topic_info;
        topic_info.head_name = "TLR_postpro";
        topic_info.topic_name = topic_name_fusion_traffic_light;
        topic_info.node_name = "alg_vision_trafficlight_postpro";
        stream << InitTopicHeader(topic_info,topic_ptr_fusion_traffic_light) << std::endl;
        std::unordered_map <uint8_t,std::string> color_map = {
            {0 , "un"},
            {1 , "red"},
            {2 , "yellow"},
            {3 , "green"},
            {4 , "dark"}
        };
        std::unordered_map <uint8_t,std::string> flash_status = {
            {0 , "常亮"},
            {1 , "闪烁"}
        };
        if(topic_ptr_fusion_traffic_light){
            if(topic_ptr_fusion_traffic_light->traffic_lights_list.size() >= 8){
                int Uturn_index = 7;
                int Left_index = 5;
                int Up_index = 4;
                int Right_index = 6;
                stream<<"Uturn : "<<color_map[topic_ptr_fusion_traffic_light->traffic_lights_list[Uturn_index].color_enum]<<" "<<flash_status[topic_ptr_fusion_traffic_light->traffic_lights_list[Uturn_index].flash_enum]<<static_cast<int>(topic_ptr_fusion_traffic_light->traffic_lights_list[Uturn_index].count_down_s)<<",T:"<<topic_ptr_fusion_traffic_light->traffic_lights_list[Uturn_index].id;
                stream<<"|Left:"<<color_map[topic_ptr_fusion_traffic_light->traffic_lights_list[Left_index].color_enum]<<" "<<flash_status[topic_ptr_fusion_traffic_light->traffic_lights_list[Left_index].flash_enum]<<static_cast<int>(topic_ptr_fusion_traffic_light->traffic_lights_list[Left_index].count_down_s)<<",T:"<<topic_ptr_fusion_traffic_light->traffic_lights_list[Left_index].id;
                stream<<"|Up:"<<color_map[topic_ptr_fusion_traffic_light->traffic_lights_list[Up_index].color_enum]<<" "<<flash_status[topic_ptr_fusion_traffic_light->traffic_lights_list[Up_index].flash_enum]<<static_cast<int>(topic_ptr_fusion_traffic_light->traffic_lights_list[Up_index].count_down_s)<<",T:"<<topic_ptr_fusion_traffic_light->traffic_lights_list[Up_index].id;
                stream<<"|Right:"<<color_map[topic_ptr_fusion_traffic_light->traffic_lights_list[Right_index].color_enum]<<" "<<flash_status[topic_ptr_fusion_traffic_light->traffic_lights_list[Right_index].flash_enum]<<static_cast<int>(topic_ptr_fusion_traffic_light->traffic_lights_list[Right_index].count_down_s)<<",T:"<<topic_ptr_fusion_traffic_light->traffic_lights_list[Right_index].id<<std::endl;
            }
        }
    }
    void  AdapterDebugInfoShowNode::GetSvpVersion(std::stringstream& stream){
        static std::string some_version;
        if(some_version.empty()) {
            std::stringstream ss;
            std::vector<const char*> envNames = {
                "MCU_VER",
                "SOC_VER",
                "SVP_VER",
                "USS_VER",
                "SWITCH_VER",
            };
            for (const auto& envName : envNames) {
                const char* value = std::getenv(envName);
                if( value && *value != '\0'){
                    ss << envName << " : " << value << "\n";
                }
            }
            some_version = ss.str();
        }
        stream << some_version;
    }
    void AdapterDebugInfoShowNode::AlgICAVoiceReminder(std::stringstream& stream){
        float pnc_fsm_out_frequency = 0.0;
        std::map<int,std::pair<std::string,std::string>> voice_reminder = {
            {0x0  , {"",color_default}},
            {0x1  , {"导航辅助驾驶已激活",color_default}},
            {0x2  , {"向左变道",color_default}},
            {0x3  , {"向右变道",color_default}},
            {0x4  , {"还是等一等再变道吧",color_default}},
            {0x5  , {"变道已取消",color_default}},
            {0x6  , {"请手动向右变道",color_default}},
            {0x7  , {"请手动向左变道",color_default}},
            {0x8  , {"准备进入匝道",color_default}},
            {0x9  , {"你已进入匝道，请小心驾驶",color_default}},
            {0xA  , {"准备进入主路",color_default}},
            {0xB  , {"一公里后将驶离导航驾驶路段",color_default}},
            {0xC  , {"200米后功能即将退出,请准备接管",color3}},
            {0xD  , {"",color_default}},
            {0xE  , {"导航辅助驾驶已结束",color3}},
            {0xF  , {"辅助驾驶已结束",color3}},
            {0x10 , {"复杂路况，小心驾驶",color3}},
            {0x11 , {"施工路段，请小心驾驶",color3}},
            {0x12 , {"向右沿导航变道",color_default}},
            {0x13 , {"向左超车变道",color_default}},
            {0x14 , {"向左沿导航变道",color_default}},
            {0x15 , {"您已控制车辆",color3}},
            {0x16 , {"向右超车变道",color_default}},
            {0x17 , {"您已控制方向",color3}},
            {0x18 , {"注意转向灯还未关闭",color_default}},
            {0x19 , {"ACC辅助驾驶已激活",color_default}},
            {0x1A , {"已切换至ICA辅助驾驶",color3}},
            {0x1B , {"辅助驾驶已激活",color_default}},
            {0x1C , {"已切换至ACC辅助驾驶",color3}},
            {0x1D , {"ICA辅助驾驶已激活",color_default}}
        };
        std::map<int,std::string> parking_disp_info_enum = {
            {0 , ""},
            {1 , "车位搜索中"},
            {2 , "已找到车位，请停车"},
            {3 , "车尾泊入，请点击开始泊入"},
            {4 , "请松开刹车踏板和方向盘，开启自动泊车"},
            {5 , ""},
            {6 , "自动泊入中"},
            {7 , "自动泊出中"},
            {8 , "泊入已完成"},
            {9 , "泊出已完成"},
            {11, "请选择泊出方向"},
            {12, "请关闭车门后继续泊车"},
            {13, "请关闭引擎盖后继续泊车"},
            {14, "请关闭后备箱后继续泊车"},
            {15, "请系上安全带继续泊车"},
            {16, ""},
            {17, ""},
            {18, "车头泊入，请点击开始泊入"},
            {19, "路径规划中"},
            {20, "路径规划失败"},
            {21, "请踩住刹车，并手动调整车位"},
            {22, "继续前行"},
            {23, "继续后退"},
            {24, "泊入空间小，自动折叠后视镜"}
        };
        std::map<int,std::string> parking_suspend_reason_enum = {
                {0 , ""},
                {1 , "泊车已暂停，点击“继续泊车”可继"},
                {2 , "车门"},
                {3 , "前机盖"},
                {4 , "后备箱"},
                {5 , ""},
                {6 , "请系上安全带继"},
                {7 , ""},
                {8 , "路径上存在动态"},
                {9 , "路径上存在静态"},
                {10, "检测蓝牙断开，请及时接"}
        };
        std::map<int,std::string> parking_abort_reason_enum = {
            {0  , ""},
            {1  , "ADC系统故障,自动泊车退出"},
            {2  , "EPS系统故障,自动泊车退出"},
            {3  , "IPB系统故障,自动泊车退出"},
            {4  , "SCU系统故障,自动泊车退出"},
            {5  , "VCU系统故障,自动泊车退出"},
            {6  , "EPB系统故障,自动泊车退出"},
            {7  , "PAD系统故障,自动泊车退出"},
            {8  , "PAS系统故障,自动泊车退出"},
            {9  , "BCM系统故障,自动泊车退出"},
            {10 , "驾驶员干预方向盘，自动泊车退出"},
            {11 , "驾驶员干预挡位，自动泊车退出"},
            {12 , "油门踏板被踩下，自动泊车退出"},
            {13 , "车速>25kph,自动泊车退出"},
            {14 , "道路坡度超限，自动泊车退出"},
            {15 , "揉库次数过多，自动泊车退出"},
            {16 , "泊车中断次数过多，自动泊车退出"},
            {17 , "泊车超时，自动泊车退出"},
            {18 , "路径规划失败，自动泊车退出"},
            {19 , "车辆受阻，自动泊车退出"},
            {20 , "自动泊车退出，请及时接管"},
            {21 , "横向执行器退出握手"},
            {22 , "纵向执行器退出握手"},
            {23 , "车辆发生碰撞，自动泊车退出"},
            {24 , "安全气囊系统故障，自动泊车退出"},
            {25 , "泊车暂停时间超时，自动泊车退出"},
            {26 , "EPB被拉齐,自动泊车退出"},
            {27 , "紧急制动触发，自动泊车退出"},
            {28 , "胎压系统故障，自动泊车退出"},
            {29 , "环境雨量过大，自动泊车退出"},
            {30 , "充电枪已连接，自动泊车退出"},
            {31 , "ABS激活,自动泊车退出"},
            {32 , "TCS激活,自动泊车退出"},
            {33 , "HDC激活,自动泊车退出"},
            {34 , "APA关联系统故障,自动泊车退出"}
        };
        std::map<int,std::string> parking_passive_reason_enum = {
            {1 , "当前驾驶模式不支持开启自动泊车"  },
            {2 , "车速过快，无法开启自动泊车"  },
            {3 , "行车功能开启，无法开启泊车"  },
            {4 , "当前驾驶模式不满足条件，无法开启泊车"  },
            {5 , "动力系统故障，无法开启泊车"  },
            {6 , "APA系统故障,无法开启泊车"  }
        };
        GetValueFromTopic(topic_name_pnc_fsm_out, topic_name_pnc_fsm_out_zero, topic_ptr_pnc_fsm_out, topic_ptr_pnc_fsm_out_zero, topic_ptr_pnc_fsm_out_tmp,
            is_ptr_pnc_fsm_out_got, is_ptr_pnc_fsm_out_zero_got, pnc_fsm_out_frequency);
        if(is_driving_mode()) {
            static int current_index = 0;
            static std::chrono::time_point<std::chrono::steady_clock> start_time;
            if (is_ptr_pnc_fsm_out_got || is_ptr_pnc_fsm_out_zero_got) {
                auto index = topic_ptr_pnc_fsm_out_tmp->panel_signals.driving_cruising_panel.driving_cruising_func_voice.voice_reminder_req_enum;
                if (!index ) {
                    if(current_index){
                        auto now = std::chrono::steady_clock::now();
                        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - start_time).count();
                        if (elapsed < 5000 && current_index != 0) {
                            stream << ADD_TEXT_COLOR_LEFT() << voice_reminder[current_index].first << voice_reminder[current_index].second << std::endl;
                        } else {
                            current_index = 0;
                        }
                    }else{
                        return;
                    }
                } else {
                    if (index != current_index) {
                        current_index = index;
                        start_time = std::chrono::steady_clock::now();
                    }
                    auto now = std::chrono::steady_clock::now();
                    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - start_time).count();
                    if (elapsed < 5000) {
                        stream << ADD_TEXT_COLOR_LEFT() << voice_reminder[current_index].first << voice_reminder[current_index].second << std::endl;
                    } else {
                        current_index = 0;
                    }
                }
            } else {
                current_index = 0; // 重置状态
                return;
            }
        } else if(0x1 == getFsmMode()) {
            if (is_ptr_pnc_fsm_out_got || is_ptr_pnc_fsm_out_zero_got) {
                auto get_parking_disp_info = topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_txt.parking_disp_info_enum;
                auto get_parking_suspend_reason = topic_ptr_pnc_fsm_out_tmp->panel_signals.parking_panel.parking_func_txt.parking_suspend_reason_enum;
                auto get_abort_reason  = topic_ptr_pnc_fsm_out_tmp -> panel_signals.parking_panel.parking_func_txt.parking_abort_reason_enum;
                auto get_passive_reason_enum =  topic_ptr_pnc_fsm_out_tmp -> panel_signals.parking_panel.parking_func_txt.parking_passive_reason_enum;
                auto get_remain_dis = topic_ptr_pnc_fsm_out_tmp -> panel_signals.parking_panel.parking_func_icon.apa_remain_dist_m;
                std::vector<int> disp_info = {0, 5, 16, 17};
                std::vector<int> suspend_reason = {0,5,7};
                if(std::find(disp_info.begin(), disp_info.end(), get_parking_disp_info) == disp_info.end()){
                    if(get_parking_disp_info == 22 || get_parking_disp_info == 23){
                        stream<< ADD_TEXT_COLOR_LEFT() <<parking_disp_info_enum[get_parking_disp_info] <<get_remain_dis <<" m"<<color_default<<std::endl;
                    }else{
                        stream<< ADD_TEXT_COLOR_LEFT() <<parking_disp_info_enum[get_parking_disp_info] << color_default<<std::endl;
                    }
                } else {
                }
                if(std::find(suspend_reason.begin(), suspend_reason.end(), get_parking_suspend_reason) == suspend_reason.end()){
                    stream<< ADD_TEXT_COLOR_LEFT() <<parking_suspend_reason_enum[get_parking_suspend_reason] << color_orange<<std::endl;
                } else {
                }
                if(get_abort_reason){
                    stream<< ADD_TEXT_COLOR_LEFT() <<parking_abort_reason_enum[get_abort_reason] << color3<<std::endl;
                } else {
                }
                if(get_passive_reason_enum){
                    stream<< ADD_TEXT_COLOR_LEFT() <<parking_passive_reason_enum[get_passive_reason_enum] << color3<<std::endl;
                }else{
                }
            } else {
                return;
            }

        } else {
            return;
        }
    }
}
