###
 # @Author: hexiaokang <EMAIL>
 # @Date: 2024-10-17 19:06:14
 # @LastEditors: hexiaokang <EMAIL>
 # @LastEditTime: 2025-05-23 10:01:16
 # @FilePath: /nviz_ws/visualization/visualization_node/alg_forward_master/script/record_other.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 

FORWARD_CHOICE="$1"
RECORD_PATH="$2"
SELECTION_NUM="$3"
SPLIT_CHOICE="$4"
DATA_TIME="$5"
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

SCRIPTPATH="$( cd -- "$(dirname -- "$0")" >/dev/null 2>&1 ; pwd -P )"
YAML_FILE="$SCRIPTPATH/../config/topicparamter.yaml"
ip_8775="***********"

CONFIG_PARAM="1"   # 1.ffmeng 2.vokscreen 3.local_test

## 获取版本配置文件Start
CONFIG_FILE="$SCRIPTPATH/../../../version.md"
echo "version.md is in $CONFIG_FILE"
# 检查配置文件是否存在
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Configuration file $CONFIG_FILE does not exist."
    pkill -f "nviz"
    exit 1
fi

nex_release_version=$(cat $CONFIG_FILE | sed -n '2p')
nex_msgs_version=$(cat $CONFIG_FILE | sed -n '4p')

# 打印提取的version值
echo "nex_release version: $nex_release_version"
echo "nex_msgs version: $nex_msgs_version"

#  验证网络连接
check_network() {
    ping -c 2 -w 3 $ip_8775 > /dev/null
    if [ $? -eq 0 ]; then
        echo "target platform: 8775"
    else
        echo -e "${RED}[ERROR] network is error! Please check $ip_8775.${NC}"
        pkill -f "nviz"
        exit 1
    fi
}

#  检查依赖工具
check_dependencies() {
    if ! command -v xdotool &> /dev/null; then
        echo -e "${RED}<<<<------ xdotool未安装(sudo apt install xdotool) ------>>>>>${NC}"
        pkill -f "nviz"
        exit 1
    fi
}

#  创建录制目录
create_record_directory() {
    datetime_name=$DATA_TIME
    # ACTUAL_RECORD_PATH="$RECORD_PATH$datetime_name"
    # mkdir -p $ACTUAL_RECORD_PATH
    # cd $ACTUAL_RECORD_PATH

    # # for data_sync
    # export NVIZ_RECORD_PATH=${ACTUAL_RECORD_PATH}

    ACTUAL_RECORD_PATH="$RECORD_PATH"

    cd $ACTUAL_RECORD_PATH

    # for data_sync
    export NVIZ_RECORD_PATH=${ACTUAL_RECORD_PATH}
}

#  vokscreen启动视频录制
start_recording_vokscreen() {
    pkill vokoscreen
    echo "Open vokoscreen "
    vokoscreen &
    sleep 1s 
    echo "start recording "
    current_time=$(date +%s)
    current_time_tmp=$(date +"%Y%m%d_%H%M%S")
    xdotool key ctrl+shift+F10
}

#  vokscreen停止视频录制
stop_recording_vokscreen() {
    echo "stop recording"
    sleep 3s
    xdotool key ctrl+shift+F11
    echo -e "${YELLOW}<<<<------请不要关闭终端!!!! 视频正在落盘!!!!------>>>>>${NC}"
    sleep 10s 
}

#  ffmpeng启动视频录制
start_recording_ffmpeng() {
    current_time=$(date +"%Y%m%d_%H%M%S")
    bash $SCRIPTPATH/ffmpeg_videos.sh
    B_PID=$!
}

#  ffmpeng停止视频录制
stop_recording_ffmpeng() {
    if [ -n "$B_PID" ]; then
        kill "$B_PID"
        wait "$B_PID"
    fi
    vin_code="vin17"
    realyname="alg_video-$current_time-$vin_name-$vin_code-$nex_msgs_version-$FORWARD_CHOICE"
    #alg_video-20250122_151200-A19-007-vin17-v1.2.5-ica_show.mp4
    for file in ffmpeg_merged*.mp4; do
        if [ -e "$file" ]; then
            mv "$file" "$realyname.mp4"
            echo "重命名文件 '$file' 为 '$realyname.mp4'"
            break
        fi
    done
}

#  vokscreen查找最新视频文件
find_latest_video() {
    latest_video=$(ls -t ~/Videos 2>/dev/null | grep vokoscreen | head -n1)
    if [ -z "$latest_video" ]; then
        latest_video=$(ls -t /home/<USER>/视频 2>/dev/null | grep vokoscreen | head -n1)
        video_path="/home/<USER>/视频"
    else
        video_path="~/Videos"
    fi
}

#  vokscreen处理最新视频文件
process_latest_video() {
    if [ -n "$latest_video" ]; then
        echo "最新的视频文件是: $latest_video"
        datetime_str=$(echo "$latest_video" | grep -oP '\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}')
        formatted_time=$(echo "$datetime_str" | sed 's/_/ /g' | sed 's/-/ /g' | awk '{print $1 "/" $2 "/" $3 " " $4 ":" $5 ":" $6}')
        file_time=$(date -d "$formatted_time" +%s 2>/dev/null)

        if [ -z "$file_time" ]; then
            echo "日期转换失败，检查输入格式。"
            return
        fi

        # current_time=$(date +%s)
        time_diff=$((file_time - current_time))
        [ "$time_diff" -lt 0 ] && time_diff=$(( -time_diff ))

        echo "时间差（秒）: $time_diff"

        if [ "$time_diff" -gt 5 ]; then
            echo "无法找到最新视频文件，请尝试手动移动视频"
        else
            mv "$video_path/$latest_video" "$ACTUAL_RECORD_PATH/$latest_video"
            realyname="alg_video-$current_time_tmp-$vin_name-$vin_code-$nex_msgs_version-$FORWARD_CHOICE"
            #alg_video-20250122_151200-A19-007-vin17-v1.2.5-ica_show.mp4
            for file in vok*.mkv; do
                if [ -e "$file" ]; then
                    mv "$file" "$realyname.mkv"
                    echo "重命名文件 '$file' 为 '$realyname.mkv'"
                    break
                fi
            done
            echo -e "${YELLOW}<<<<------视频已落盘,等待system_data落盘------>>>>>${NC}"
        fi
    else
        echo "未找到包含vokoscreen的最新视频文件"
    fi
}

# 主程序
if [ "$CONFIG_PARAM" -eq 1 ] || [ "$CONFIG_PARAM" -eq 2 ]; then
   check_network
fi
check_dependencies
create_record_directory

copy_ttransition_events() {
    # 查找state_transitions.txt文件
    state_file=$(find "$SCRIPTPATH/../../../" -maxdepth 1 -type f -name "state_transitions.txt")

    statetxt_file=$(find "$SCRIPTPATH/../../../" -maxdepth 1 -type f -name "state_log.txt")
    if [ -n "$statetxt_file" ]; then
        echo "Moving state_log.txt: $statetxt_file"
        mv "$statetxt_file" .
        cp state_log.txt ./$system_date
        # python3 $SCRIPTPATH/special_extract_data_and_video.py -i .
    else
        echo "No state_log.txt found."
    fi
    
    # 检查文件是否存在
    if [ -n "$state_file" ]; then
        echo "Moving state_transitions.txt: $state_file"
        mv "$state_file" .
        pwd
        # python3 $SCRIPTPATH/special_extract_data_and_video.py -i .
    else
        echo "No state_transitions.txt found."
    fi
}


# 启动launch
source "$SCRIPTPATH/../../../setup.bash"
echo "#################"
echo "nex_foward_master.py : $SCRIPTPATH/../../alg_tools/launch/nex_foward_master.py"
echo "yaml path : $YAML_FILE"
echo "actual record path : $ACTUAL_RECORD_PATH"
echo "select num : $SELECTION_NUM"

record_system_path="/alg_data/system_data_$datetime_name"
actual_cmd="ssh -t root@${ip_8775} '\
rm -rf /var/log/*.core* /var/log/coredump.log; \
mkdir -p ${record_system_path}  \
&& pidin ar > ${record_system_path}/pidin.log \
&& for i in {1..5}; do \
    pidin -f anlph >> $record_system_path/process.log; \
done \
&& hogs >> ${record_system_path}/hogs.log & \
while true; do \
    date \"+%Y-%m-%d %H:%M:%S\" >> ${record_system_path}/hogs.log; \
    sleep 1; \
done '"

if [ "$CONFIG_PARAM" -eq 1 ] || [ "$CONFIG_PARAM" -eq 2 ]; then
    eval $actual_cmd &
    background_pid=$!
fi


write_pc_top() {
    while true; do
        top_output=$(top -bc -w 512 -n 1 -o %CPU)
        header=$(echo "$top_output" | head -n 7)
        process_info=$(echo "$top_output" | tail -n +8 | head -n 50)
        echo -e "$header\n$process_info" >> PC_top.log
        sleep 1
        top_output2=$(top -H -b -w 512 -n 1 -o %CPU)
        header2=$(echo "$top_output2" | head -n 7)
        process_info2=$(echo "$top_output2" | tail -n +8 | head -n 50)
        echo -e "$header2\n$process_info2" >> PC_top.log
        sleep 1
    done
}


write_pc_top &
PC_TOP_PID=$!
trap 'kill $PC_TOP_PID 2>/dev/null' EXIT 

# 启动录制
if [ "$CONFIG_PARAM" -eq 1 ]; then
    start_recording_ffmpeng
elif [ "$CONFIG_PARAM" -eq 2 ]; then
    start_recording_vokscreen
elif [ "$CONFIG_PARAM" -eq 3 ]; then
    start_recording_ffmpeng
else
    start_recording_ffmpeng
    echo "Invalid option: $CONFIG_PARAM. Please set PARAM to 1, 2, or 3."
fi

# 启动主程序
# nex launch "$SCRIPTPATH/../../alg_tools/launch/nex_foward_master.py" \
#     yaml_path:="$YAML_FILE" \
#     forward_choice:="$FORWARD_CHOICE" \
#     record_path:="./" \
#     select_num:="$SELECTION_NUM" \
#     platform:="qnx" \
#     datetime_name:="$datetime_name" \
#     script_dir:="$SCRIPTPATH/../../../"
    
keep_printing() {
    while true; do
        echo -e "${YELLOW}<<<<--请不要手动关闭nviz!!! 脚本正在处理!!! 等待程序自动退出!!!-->>>>>${NC}"
        sleep 1
    done
}

keep_printing &
PRINT_PID=$!

trap 'kill $PRINT_PID 2>/dev/null' EXIT

echo -e "${YELLOW}<<<<--请不要手动关闭nviz!!! 脚本正在处理!!! 等待程序自动退出!!!-->>>>>${NC}"
echo -e "${YELLOW}<<<<------请不要关闭终端!!!! 视频正在落盘!!!!------>>>>>${NC}"

# 结束system_data录制进程
if [ -n "$background_pid" ]; then
    kill "$background_pid"
fi

## 获取车辆信息配置
PARAMETER_CONFIG_FILE="/tmp/parameterConfig.txt"
echo "parameterConfig.txt is in $PARAMETER_CONFIG_FILE"
# 检查配置文件是否存在
if [ ! -f "$PARAMETER_CONFIG_FILE" ]; then
    echo "Error: Configuration file $PARAMETER_CONFIG_FILE does not exist."
    vin_name="Unknow"
    vin_code="Unknow"
else
    vin_name=$(cat $PARAMETER_CONFIG_FILE | sed -n '1p')
    vin_code=$(cat $PARAMETER_CONFIG_FILE | sed -n '2p')
fi


if [ "$CONFIG_PARAM" -eq 1 ]; then
    stop_recording_ffmpeng
elif [ "$CONFIG_PARAM" -eq 2 ]; then
    stop_recording_vokscreen
    find_latest_video
    process_latest_video
elif [ "$CONFIG_PARAM" -eq 3 ]; then
    stop_recording_ffmpeng
else
    stop_recording_ffmpeng
    echo "Invalid option: $CONFIG_PARAM. Please set PARAM to 1, 2, or 3."
fi

if [ "$CONFIG_PARAM" -eq 1 ] || [ "$CONFIG_PARAM" -eq 2 ]; then

    MAX_RETRIES=5
    RETRY_COUNT=0

    echo -e "${YELLOW}<<<<--请不要手动关闭nviz!!! 脚本正在处理!!! 等待程序自动退出!!!-->>>>>${NC}"
    echo -e "${YELLOW}<<<<--请不要手动关闭nviz!!! 脚本正在处理!!! 等待程序自动退出!!!-->>>>>${NC}"
    echo -e "${YELLOW}<<<<--请不要手动关闭nviz!!! 脚本正在处理!!! 等待程序自动退出!!!-->>>>>${NC}"

    SSH_COMMAND=`ssh -o ConnectTimeout=10 -t root@${ip_8775} 'slay -f hogs ; sync'`

    run_ssh_command() {
    $SSH_COMMAND &
    SSH_PID=$!
    
    while kill -0 $SSH_PID 2>/dev/null; do
        echo -e "${YELLOW}<<<<--请不要手动关闭nviz!!! 脚本正在处理!!! 等待程序自动退出!!!-->>>>>${NC}"
        sleep 1s
    done
    
    wait $SSH_PID
    }

    while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    echo "尝试执行 SSH 命令，重试次数: $((RETRY_COUNT+1))"

    system_date="system_data_$datetime_name"
    
    if run_ssh_command; then
        echo "SSH 命令成功执行"
        scp -r root@${ip_8775}:${record_system_path} .
        mkdir coredump console map_engine
        ssh -t root@${ip_8775} "ls -l /var/log/ > /var/log/coredump.log"
        scp -r root@${ip_8775}:/var/log/coredump.log ./coredump
        scp -r root@${ip_8775}:/var/log/*.core* ./coredump
        # 遍历所有 .core 文件
        for core_file in $(ssh -t root@${ip_8775} "ls /var/log/*.core*"); do
            # 提取第一个 . 前面的字符串
            base_name=$(basename $core_file | cut -d '.' -f 1)
            # scp 同名文件夹
            scp -r root@${ip_8775}:/alg/app/running/alg/$base_name ./coredump
        done        
        ssh -t root@${ip_8775} "rm -rf ${record_system_path}"
        scp -r root@${ip_8775}:/etc/version_extra ./soc_etc_version
        scp -r root@${ip_8775}:/alg/app/running/release_note ./alg_release_note
        scp -r root@${ip_8775}:/log/console.log ./console
        scp -r root@${ip_8775}:/tmp/load_*.csv ./$system_date
        scp -r root@${ip_8775}:/alg/app/running/alg/alg_map_engine_server/script/tencent_log ./map_engine
        scp -r root@${ip_8775}:/alg/app/running/alg/alg_map_engine_server/script/data ./map_engine
        need_road_net=("avp" "ica" "ica_show" "ica_7v" "ica_7v_show" "p2p" "p2p_show" "p2p_7v" "p2p_7v_show")
        for item in "${need_road_net[@]}"; do
            if [[ "$item" == "${FORWARD_CHOICE}" ]]; then
                scp -r root@${ip_8775}:/data_fota/road_net .
                break
            fi
        done 
        need_rba_map=("apa")
        for item in "${need_rba_map[@]}"; do
            if [[ "$item" == "${FORWARD_CHOICE}" ]]; then
                scp -r root@${ip_8775}:/data_fota/tba_map .
                break
            fi
        done
        break
    else
        echo -e "${RED}SSH 命令超时或失败，请稍后再试...${NC}"
        ((RETRY_COUNT++))
        
        if [ $RETRY_COUNT -ge $MAX_RETRIES ]; then
        echo -e "${RED}达到最大重试次数，命令执行失败${NC}"
        break
        fi
        sleep 1s
    fi
    done

    echo -e "${YELLOW}<<<<--请不要手动关闭nviz!!! 脚本正在处理!!! 等待程序自动退出!!!-->>>>>${NC}"
    echo -e "${YELLOW}<<<<--请不要手动关闭nviz!!! 脚本正在处理!!! 等待程序自动退出!!!-->>>>>${NC}"
    echo -e "${YELLOW}<<<<--请不要手动关闭nviz!!! 脚本正在处理!!! 等待程序自动退出!!!-->>>>>${NC}"

    cp ${script_dir}version.md ./
    cp ${script_dir}record.sh ./

    copy_ttransition_events



    copy_trigger_events() {
        # 查找*_ica或*_apa目录
        find "$SCRIPTPATH/../../../" -maxdepth 1 -type d \( -name "*_ica" -o -name "*_apa" \) | while read dir; do
            echo "Processing directory: $dir"
            
            # 查找trigger_event*目录并移动
            find "$dir" -type d -name "trigger_event*" | while read trigger_dir; do
                echo "Moving trigger event directory: $trigger_dir"
                new_dir_name="${dir}/trigger_event_${datetime_name}"
                echo "dir : $dir"
                mv $trigger_dir $new_dir_name 
                mv "$dir"/* .               
            done
        done
    }

    # 调用函数
    echo "Moving trigger event directories..."
    copy_trigger_events

    if [ "$SPLIT_CHOICE" -eq 1 ] ; then
        # 执行切包
        PWD_OUTPUT=$(pwd)
        echo "Current directory: $PWD_OUTPUT"
        # 遍历 pwd 目录下所有以 alg_ 开头的文件夹
        for bag_folder in "$PWD_OUTPUT"/alg_*; do
            if [ -d "$bag_folder" ]; then
                if [ ! -f "$bag_folder/metadata.yaml" ]; then
                    echo -e "${RED}缺少metadata.yaml文件，请稍等,正在帮 $bag_folder 目录生成此文件${NC}"
                    nex_bag_convert -G -i "$bag_folder"
                fi
            fi
        done
        source ~/nviz_ws/setup.bash
        python3 ~/nviz_ws/install/alg_tools/scripts/extract_data_and_video.py -i "$PWD_OUTPUT"
    else
        echo -e "${YELLOW}不进行切包${NC}"
    fi

    echo -e "${YELLOW}切包完成,请不要关闭终端，正在删除本地打点数据${NC}"

    nviz_path=$SCRIPTPATH/../../..
    rm -r $nviz_path/*_ica || rm -r $nviz_path/*_ica



    if [ $? -eq 0 ]; then
        echo -e "${YELLOW}Trigger event directories moved successfully${NC}"
    else
        echo -e "${RED}Failed to move trigger event directories${NC}"
    fi



    if [ "$SELECTION_NUM" -eq 2 ] || [ "$SELECTION_NUM" -eq 6 ]; then
        cp /map_data/map.sqlite ./map.sqlite
        cp /log/alg_log ./log/alg_log
    else
        echo "SELECTION_NUM is not 2 or 6."
    fi
    pgrep -f "sender.py" | xargs kill -9
    pgrep -f "test2" | xargs kill -9
    echo -e "${YELLOW}<<<<--数据录制完成!!!! 请继续操作或关闭终端!!!-->>>>>${NC}"
fi

echo -e "${YELLOW}<<<<--数据录制完成!!!! 请继续操作或关闭终端!!!-->>>>>${NC}"
