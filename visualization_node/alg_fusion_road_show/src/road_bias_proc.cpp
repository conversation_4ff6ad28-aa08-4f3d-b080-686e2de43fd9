#include "alg_fusion_road_show/road_bias_proc.hpp"
#include "alg_fusion_road_show/road_odometry.hpp"
#include "alg_fusion_road_show/util.hpp"

struct LinePosInfo
{
    std::int64_t ts_;
    float val_;
};


std::vector<Line>::iterator RoadBiasProc::FindLineByPos(std::uint8_t pos, Frame& frame)
{
    return 
    std::find_if(frame.lines_.begin(), frame.lines_.end(),[&pos](const Line& line) {
        return pos == line.pos_;
    });
}

std::vector<Line>::iterator RoadBiasProc::FindLineById(const std::uint32_t& id, Frame& frame)
{
    return 
    std::find_if(frame.lines_.begin(), frame.lines_.end(),[&id](const Line& line) {
        return id == line.id;
    });
}

void RoadBiasProc::Proc(debug_msgs::msg::DebugInfo& debug, const Road& road)
{
    debug.std_header.timestamp_ns = road.std_header.timestamp_ns;
    Eigen::Isometry3d odometry;
    if(!ODOMETRY.GetTransform(road.std_header.timestamp_ns, odometry))
    {
        std::cout << "get dr failed\n";
        return;
    }

    Frame new_frame;
    new_frame.ts_ = road.std_header.timestamp_ns;
    for(const auto& line : road.lane_lines_list)
    {
        Line new_line;
        new_line.id = line.id;
        new_line.pos_ = line.position_enum;

        for(const auto seg_line : line.seg_lane_line_list)
        {
            for(const auto& pt : seg_line.geometry.sampling_point_list)
            {
                Point new_pt;
                new_pt.x = pt.x_m;
                new_pt.y = pt.y_m;
                TransformPoint(new_pt, odometry);
                new_line.pts.push_back(new_pt);
            }
        }

        new_frame.lines_.emplace_back(new_line);
    }

    frames_.push_back(new_frame);

    if(frames_.size() > 10)
    {
        frames_.pop_front();
    }

    if(frames_.size() < 2)
    {
        return;
    }
    
    auto& cur_frame = frames_.back();

    auto cur_left_line = FindLineByPos(1, cur_frame);
    auto cur_right_line = FindLineByPos(8, cur_frame);

    if(cur_left_line == cur_frame.lines_.end() || cur_right_line == cur_frame.lines_.end())
    {
        return;
    }

    std::unordered_map<int, std::vector<LinePosInfo>> l_line_pos_map;
    std::unordered_map<int, std::vector<LinePosInfo>> r_line_pos_map;
    std::vector<int> pos_list {0, 10, 20, 30, 40};

    auto calcul_lane_pos_list_fun = [&pos_list, odometry](const std::int64_t& ts ,const Line& line, std::unordered_map<int, std::vector<LinePosInfo>>& line_pos_map)
    {
        auto pts = line.pts;
        TransformPoints(pts, odometry.inverse());
        for(const auto& pos : pos_list)
        {
            for(const auto& pt : pts)
            {
                if(pt.x > pos)
                {
                    line_pos_map[pos].emplace_back(LinePosInfo{ts, (float)pt.y});
                    break;
                }
            }
        }
    };

    calcul_lane_pos_list_fun(cur_frame.ts_, *cur_left_line, l_line_pos_map);
    calcul_lane_pos_list_fun(cur_frame.ts_, *cur_right_line, r_line_pos_map);

    for(std::size_t i = 0; i < frames_.size(); ++ i)
    {
        if(i == frames_.size() - 1)
        {
            break;
        }

        auto other_left_line = FindLineById(cur_left_line->id, frames_[i]);
        auto other_right_line = FindLineById(cur_right_line->id, frames_[i]);

        if(other_left_line != frames_[i].lines_.end())
        {
            calcul_lane_pos_list_fun(frames_[i].ts_, *other_left_line, l_line_pos_map);
        }

        if(other_right_line != frames_[i].lines_.end())
        {
            calcul_lane_pos_list_fun(frames_[i].ts_, *other_right_line, r_line_pos_map);
        }
    }

    auto calcul_lane_line_diff_value_fun = [](std::vector<LinePosInfo>& line_pos_infos) {
        std::sort(line_pos_infos.begin(), line_pos_infos.end(), [](const LinePosInfo& a, const LinePosInfo& b) {
            return a.val_ > b.val_;
        });

        // for(const auto& val : line_pos_infos)
        // {
        //     std::cout << val.ts_ << "->" << val.val_ << ";"; 
        // }

        // std::cout << "\n";

        if(line_pos_infos.front().ts_ > line_pos_infos.back().ts_)
        {
            return (line_pos_infos.front().val_ - line_pos_infos.back().val_) * 100;
        }
        else
        {
            return (line_pos_infos.back().val_ - line_pos_infos.front().val_) * 100;
        }
    };

    for(const auto& pos : pos_list)
    {
        if(l_line_pos_map.count(pos) == 1 && l_line_pos_map.at(pos).size() > 1)
        {
            debug_msgs::msg::DebugInfoFloat32 debug_info;
            debug_info.signal_name = "left_" + std::to_string(pos) ;
            debug_info.value = calcul_lane_line_diff_value_fun(l_line_pos_map.at(pos));
            // std::cout << "l " << debug_info.signal_name << "," << debug_info.value << "\n";
            debug.debug_info.emplace_back(std::move(debug_info));
        } 

        if(r_line_pos_map.count(pos) == 1 && r_line_pos_map.at(pos).size() > 1)
        {
            debug_msgs::msg::DebugInfoFloat32 debug_info;
            debug_info.signal_name = "right_" + std::to_string(pos);
            debug_info.value = calcul_lane_line_diff_value_fun(r_line_pos_map.at(pos));
            // std::cout << "r " << debug_info.signal_name << "," << debug_info.value << "\n";
            debug.debug_info.emplace_back(std::move(debug_info));
        }
    }
}