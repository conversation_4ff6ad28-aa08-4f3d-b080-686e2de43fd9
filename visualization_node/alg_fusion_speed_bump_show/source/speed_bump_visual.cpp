/*
 * @Description: 
 * @Author: mvzosh
 * @Date: 2022-12-12 17:37:30
 * @LastEditTime: 2023-01-03 10:12:29
 * @LastEditors: mvzosh
 * @Reference: 
 */

#include "alg_fusion_speed_bump_show.h"
#include <bits/stdint-uintn.h>
#include <cstdint>
#include <std_msgs/msg/detail/color_rgba__struct.hpp>
#include "alg_nviz_utils/custom_env.hpp"

using namespace SPEED_BUMP;
using namespace std;

namespace SPEED_BUMP
{
bool getData = false;
static bool isOdometryInit_ = false;
nlibcpp::Clock::SharedPtr nlibcppGetClock;
nlibcpp::Subscription<fusion_msgs::msg::StaticElements>::SharedPtr sbSub;
nlibcpp::Subscription<fusion_msgs::msg::StaticElements>::SharedPtr sbSub_zero;
nlibcpp::Subscription<loc_msgs::msg::Odometry>::SharedPtr odomSub;
nlibcpp::Subscription<loc_msgs::msg::Odometry>::SharedPtr odomzeroSub;
nlibcpp::Subscription<fusion_msgs::msg::StaticElements>::SharedPtr vehOutlinesSub;
nlibcpp::Publisher<visualization_msgs::msg::MarkerArray>::SharedPtr sbPub; // speed_bump
nlibcpp::Publisher<visualization_msgs::msg::MarkerArray>::SharedPtr drain_cover_sbPub; // drain_cover
nlibcpp::Publisher<visualization_msgs::msg::MarkerArray>::SharedPtr pillarPub; // drain_cover
nlibcpp::Publisher<visualization_msgs::msg::MarkerArray>::SharedPtr visionOutlinePub;

template <typename Derived>
static Eigen::Matrix<typename Derived::Scalar, 3U, 1U> Quaternion2EularFxyz(const Eigen::QuaternionBase<Derived>& quat)
{
    using Scalar_t = typename Derived::Scalar;
    Eigen::Matrix<Scalar_t, 3U, 1U> rpy;
    rpy.x() = std::atan2(2. * (quat.w() * quat.x() + quat.y() * quat.z()), 1. - 2. * (quat.x() * quat.x() + quat.y() * quat.y()));
    rpy.y() = std::asin(2 * (quat.w() * quat.y() - quat.z() * quat.x()));
    rpy.z() = std::atan2(2. * (quat.w() * quat.z() + quat.x() * quat.y()), 1. - 2. * (quat.y() * quat.y() + quat.z() * quat.z()));

    return rpy;
}
int Sign(double x)
{
    if (std::abs(x) < 1e-4) {
        return 0;
    }

    return x > 0 ? 1 : -1;
}
double DistanceOf2Points(const base_msgs::msg::Vertex& a, const base_msgs::msg::Vertex& b)
{
    float dx = a.x_m - b.x_m;
    float dy = a.y_m - b.y_m;

    return sqrt(dx * dx + dy * dy);
}

double Distance2Line(const base_msgs::msg::Vertex& p, const base_msgs::msg::Vertex& a, const base_msgs::msg::Vertex& b)
{
    if (std::abs(a.x_m - b.x_m) < 1e-4 && std::abs(a.y_m - b.y_m) < 1e-4) {
        return DistanceOf2Points(p, a);
    }

    base_msgs::msg::Vertex s1, s2, s3;
    s1.x_m = b.x_m - a.x_m;
    s1.y_m = b.y_m - a.y_m;
    s2.x_m = p.x_m - a.x_m;
    s2.y_m = p.y_m - a.y_m;
    s3.x_m = p.x_m - b.x_m;
    s3.y_m = p.y_m - b.y_m;

    double sign12 = s1.x_m * s2.x_m + s1.y_m * s2.y_m;
    double sign13 = s1.x_m * s3.x_m + s1.y_m * s3.y_m;
    if (Sign(sign12) < 0) {
        return DistanceOf2Points(p, a);
    }
    else if (Sign(sign13) > 0) {
        return DistanceOf2Points(p, b);
    }
    else {
        double cross_product = (a.x_m - p.x_m) * (b.y_m - p.y_m) - (b.x_m - p.x_m) * (a.y_m - p.y_m);
        return std::abs(cross_product) / DistanceOf2Points(a, b);
    }
}

void OdomCallback(const loc_msgs::msg::Odometry::SharedPtr msg)
{
    if (isOdometryInit_) {
        float v = std::hypot(msg->twist.linear.x, msg->twist.linear.y);
        float w = msg->twist.angular.z;
        nlibcpp::Time stamp = nlibcpp::Time(msg->std_header.timestamp_ns);
        Eigen::Quaterniond q(msg->pose.orientation.w, msg->pose.orientation.x, msg->pose.orientation.y, msg->pose.orientation.z);
        Eigen::Vector3d eulerAngle = Quaternion2EularFxyz(q);
        double yaw = eulerAngle.z();
        Odometry::GetOdometry().Update(stamp.seconds(), msg->pose.position.x_m, msg->pose.position.y_m, yaw, v, w);
    } else {
        Odometry::GetOdometry().Init(0.0, 0.0, 0.0);
        isOdometryInit_ = true;
    }
}

void AddDrainCoverMarker(double x, double y, double z, int32_t id, visualization_msgs::msg::MarkerArray& marker_array) {
    static std_msgs::msg::ColorRGBA color;
    color.r = 1.0f;
    color.g = 0.0f;
    color.b = 0.0f;
    color.a = 0.5f;

    visualization_msgs::msg::Marker circle_marker;
    circle_marker.header.frame_id = "base_link";
    circle_marker.ns = "manhole_covers";
    circle_marker.id = id;
    circle_marker.type = visualization_msgs::msg::Marker::CYLINDER;
    circle_marker.action = visualization_msgs::msg::Marker::ADD;
    circle_marker.pose.position.x = x;
    circle_marker.pose.position.y = y;
    circle_marker.pose.position.z = z;    // Set to zero, assuming 2D
    circle_marker.pose.orientation.w = 1.0; // No rotation
    circle_marker.scale.x = 1.0;            // Diameter (2 * radius)
    circle_marker.scale.y = 1.0;            // Diameter (2 * radius)
    circle_marker.scale.z = 0.1;            // Small height to make it a flat circle
    circle_marker.color = color;

    marker_array.markers.emplace_back(circle_marker);
}

void SBCallback(fusion_msgs::msg::StaticElements::SharedPtr tracked_speed_bumps)
{
    Eigen::Vector3d egoPositionTemp;
    Eigen::Vector2d egoMotionTemp;
//    if(Odometry::GetOdometry().GetAllState(nlibcpp::Time(tracked_speed_bumps->std_header.timestamp_ns).seconds(), egoPositionTemp, egoMotionTemp)) {
//        Eigen::Isometry3d transEgo2World = Odometry::StateToTransform(egoPositionTemp);
//        Eigen::Isometry3d transWorld2Ego = transEgo2World.inverse();
        Eigen::Isometry3d transWorld2Ego = Eigen::Isometry3d::Identity();
        
        visualization_msgs::msg::MarkerArray speed_bump_nviz;
        visualization_msgs::msg::MarkerArray drain_cover_nviz;
        visualization_msgs::msg::MarkerArray pillar_nviz;
        // visualization_msgs::msg::MarkerArray vision_outline_nviz;
        visualization_msgs::msg::Marker objsDelete;
        objsDelete.type = visualization_msgs::msg::Marker::LINE_STRIP;
        objsDelete.action = visualization_msgs::msg::Marker::DELETEALL;
        const std_msgs::msg::Header header;
        speed_bump_nviz.markers.clear();
        speed_bump_nviz.markers.push_back(objsDelete);
        drain_cover_nviz.markers.clear();
        drain_cover_nviz.markers.push_back(objsDelete);
        pillar_nviz.markers.clear();
        pillar_nviz.markers.push_back(objsDelete);
        // vision_outline_nviz.markers.clear();
        // vision_outline_nviz.markers.push_back(objsDelete);


        base_msgs::msg::Vertex tmp1, tmp2;
        int k = 0;
        int j = 0;
        for (size_t i = 0; i < tracked_speed_bumps->num_of_elements; ++i) {
            visualization_msgs::msg::Marker objs;
            visualization_msgs::msg::Marker text;
            fusion_msgs::msg::StaticElement& obj = tracked_speed_bumps->elements_list[i];
            const uint8_t drain_cover_enum = 6;
            const uint8_t pillar_enum = 2;
            // const uint8_t vision_outline_enum = 10;
            if(drain_cover_enum == obj.class_enum) {
                // only one element
                if(!obj.num_of_vertices) {
                    continue;
                }
                Eigen::Vector3d pointWorld(obj.vertices_list.front().x_m, obj.vertices_list.front().y_m, 0);
                Eigen::Vector3d pointEgo = transWorld2Ego * pointWorld;
                // double x = obj.vertices_list.front().x_m,
                //        y = obj.vertices_list.front().y_m,
                //        z = obj.vertices_list.front().z_m;
                const double x = pointEgo(0), y = pointEgo(1), z = pointEgo(2);
                AddDrainCoverMarker(x, y, z, ++j, drain_cover_nviz);
                continue;
            } 
            // if (vision_outline_enum == obj.class_enum) {
            //     // only one element

            //     objs.header.frame_id = "world";
            //     objs.ns = "vision_outlines";
            //     objs.type = visualization_msgs::msg::Marker::LINE_STRIP;
            //     objs.action = visualization_msgs::msg::Marker::ADD;
            //     objs.scale.x = 0.1;
            //     objs.scale.y = 0.1;
            //     objs.pose.orientation.w = 1.0;
            //     objs.color.a = 1.0;
            //     objs.color.r = 0.0;
            //     objs.color.g = 1.0;
            //     objs.color.b = 1.0;
            //     if(!obj.num_of_vertices) {
            //         continue;
            //     }
            //     objs.id = obj.id;
            //     Eigen::Vector3d pointWorld(obj.vertices_list.front().x_m, obj.vertices_list.front().y_m, 0);
            //     Eigen::Vector3d pointEgo = transWorld2Ego * pointWorld;
            //     // double x = obj.vertices_list.front().x_m,
            //     //        y = obj.vertices_list.front().y_m,
            //     //        z = obj.vertices_list.front().z_m;

            //     for (int j = 0; j < obj.num_of_vertices; ++j)
            //     {
            //         Eigen::Vector3d pointWorld(obj.vertices_list[j].x_m,obj.vertices_list[j].y_m,0);
            //         Eigen::Vector3d pointEgo = transWorld2Ego * pointWorld;
            //         geometry_msgs::msg::Point pt;
            //         pt.x = pointEgo(0);
            //         pt.y = pointEgo(1);
            //         pt.z = 10;
            //         base_msgs::msg::Vertex p1, p2;
            //         p1.x_m = pt.x;
            //         p1.y_m = pt.y;
            //         p2.x_m = 0;
            //         p2.y_m = 0;
            //         objs.points.emplace_back(pt);
            //     }
            //     // objs.points.emplace_back(objs.points.front());
            //     geometry_msgs::msg::Pose pose;
            //     pose.position.x = (objs.points[0].x + objs.points[1].x) / 2;
            //     pose.position.y = (objs.points[0].y + objs.points[1].y) / 2;
            //     pose.position.z = 10;
            //     vision_outline_nviz.markers.emplace_back(objs);
            //     objs.points.clear();

            //     text.header.frame_id = "world";
            //     text.ns = "text";
            //     text.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
            //     text.action = visualization_msgs::msg::Marker::ADD;
            //     text.id = obj.id;
            //     text.pose.orientation.w = 1.0;
            //     text.scale.x = 0.5;
            //     text.scale.y = 0.5;
            //     text.scale.z = 0.5;
            //     text.color.a = 1.0;
            //     text.color.g = 1.0;
            //     text.color.r = 0.0;
            //     text.color.b = 0.0;
            //     text.text = std::to_string(obj.id);
            //     text.pose = pose;
            //     vision_outline_nviz.markers.emplace_back(text);

            //     continue;
            // } else 
            
            if (pillar_enum == obj.class_enum) {
                // only one element
                objs.header.frame_id = "base_link";
                objs.ns = "pillars";
                objs.type = visualization_msgs::msg::Marker::LINE_STRIP;
                objs.action = visualization_msgs::msg::Marker::ADD;
                objs.scale.x = 0.1;
                objs.scale.y = 0.1;
                objs.pose.orientation.w = 1.0;
                objs.color.a = 1.0;
                objs.color.r = 1.0;
                objs.color.g = 0.0;
                objs.color.b = 0.0;
                if(!obj.num_of_vertices) {
                    continue;
                }
                objs.id = obj.id;
                Eigen::Vector3d pointWorld(obj.vertices_list.front().x_m, obj.vertices_list.front().y_m, 0);
                Eigen::Vector3d pointEgo = transWorld2Ego * pointWorld;
                // double x = obj.vertices_list.front().x_m,
                //        y = obj.vertices_list.front().y_m,
                //        z = obj.vertices_list.front().z_m;

                for (int j = 0; j < obj.num_of_vertices; ++j)
                {
                    Eigen::Vector3d pointWorld(obj.vertices_list[j].x_m,obj.vertices_list[j].y_m,0);
                    Eigen::Vector3d pointEgo = transWorld2Ego * pointWorld;
                    geometry_msgs::msg::Point pt;
                    pt.x = pointEgo(0);
                    pt.y = pointEgo(1);
                    base_msgs::msg::Vertex p1, p2;
                    p1.x_m = pt.x;
                    p1.y_m = pt.y;
                    p2.x_m = 0;
                    p2.y_m = 0;
                    objs.points.emplace_back(pt);
                }
                objs.points.emplace_back(objs.points.front());

                pillar_nviz.markers.emplace_back(objs);
                objs.points.clear();
                continue;
            } else if (obj.class_enum != 1) {
                continue;
            }
            ++k;
            // objs.header.stamp = header.stamp;
            objs.header.frame_id = "base_link";
            objs.ns = "tracked_speed_bumps";
            objs.id = i;
            // objs.lifetime = nlibcpp::Duration(0,100000000);
            objs.type = visualization_msgs::msg::Marker::LINE_STRIP;
            objs.action = visualization_msgs::msg::Marker::ADD;
            objs.scale.x = 0.1;
            objs.scale.y = 0.1;
            vector<double> data;
            for (int j = 0; j < obj.num_of_vertices; ++j)
            {
                Eigen::Vector3d pointWorld(obj.vertices_list[j].x_m,obj.vertices_list[j].y_m,0);
                Eigen::Vector3d pointEgo = transWorld2Ego * pointWorld;
                geometry_msgs::msg::Point pt;
                pt.x = pointEgo(0);
                pt.y = pointEgo(1);
                base_msgs::msg::Vertex p1, p2;
                p1.x_m = pt.x;
                p1.y_m = pt.y;
                p2.x_m = 0;
                p2.y_m = 0;
                auto dis = DistanceOf2Points(p1, p2);
                data.emplace_back(dis);
                objs.points.emplace_back(pt);
            }
            // auto min_value = *min_element(data.begin(), data.end());
            int index = min_element(data.begin(), data.end()) - data.begin();
            if (index == 0){
                auto edge1 = DistanceOf2Points(obj.vertices_list[0], obj.vertices_list[1]);
                auto edge2 = DistanceOf2Points(obj.vertices_list[0], obj.vertices_list[3]);
                Eigen::Vector3d pointWorld(obj.vertices_list[0].x_m,obj.vertices_list[1].y_m,0);
                Eigen::Vector3d pointEgo = transWorld2Ego * pointWorld;
                if(edge1 > edge2){
                    tmp1 = obj.vertices_list[0];
                    tmp2 = obj.vertices_list[1];
                }
                else {
                    tmp1 = obj.vertices_list[3];
                    tmp2 = obj.vertices_list[0];
                }
            }
            else if(index == 3){
                auto edge1 = DistanceOf2Points(obj.vertices_list[0], obj.vertices_list[3]);
                auto edge2 = DistanceOf2Points(obj.vertices_list[2], obj.vertices_list[3]);
                if(edge1 > edge2) {
                    tmp1 = obj.vertices_list[0];
                    tmp2 = obj.vertices_list[3];
                }
                else {
                    tmp1 = obj.vertices_list[3];
                    tmp2 = obj.vertices_list[2];
                }
            }
            else {
                auto edge1 = DistanceOf2Points(obj.vertices_list[index], obj.vertices_list[index + 1]);
                auto edge2 = DistanceOf2Points(obj.vertices_list[index], obj.vertices_list[index - 1]);
                if(edge1 > edge2){
                    tmp1 = obj.vertices_list[index];
                    tmp2 = obj.vertices_list[index + 1];
                }
                else {
                    tmp1 = obj.vertices_list[index];
                    tmp2 = obj.vertices_list[index - 1];
                }
            }
            Eigen::Vector3d pointWorld(obj.vertices_list[0].x_m,obj.vertices_list[0].y_m,0);
            Eigen::Vector3d pointEgo = transWorld2Ego * pointWorld;
            geometry_msgs::msg::Point pt_end;
            pt_end.x = pointEgo.x();
            pt_end.y = pointEgo.y();
            objs.points.emplace_back(pt_end);

            objs.pose.orientation.w = 1.0;
            objs.color.a = 1.0;
            objs.color.r = 1.0;
            objs.color.g = 0.0;
            objs.color.b = 0.0;
            speed_bump_nviz.markers.emplace_back(objs);
            objs.points.clear();

            text.header = objs.header;
            text.ns = "tracked_speed_bump_text";
            text.id = k;
            // text.lifetime = nlibcpp::Duration(0,100000000);
            text.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
            text.action = visualization_msgs::msg::Marker::ADD;
            text.pose.position.x = 9.5 - (k * 1.2);
            text.pose.position.y = -12;
            text.pose.position.z = 0.0;
            text.pose.orientation.w = 1;
            text.scale.x = 0.55;
            text.scale.y = 0.55;
            text.scale.z = 0.55;
            text.color.a = 1.0;
            text.color.r = 1.0;
            text.color.g = 0.0;
            text.color.b = 0.0;
            base_msgs::msg::Vertex ego_p;
            auto min_dist = Distance2Line(ego_p, tmp1, tmp2);
            if(alg_nviz_utils::CustomEnv::IsAvp()){
                //Do None
            }
            else{
               text.text = "sp id: " + std::to_string(obj.id) + "\n" + "min_dis: " + std::to_string(min_dist);
            }               
            speed_bump_nviz.markers.emplace_back(text);
            text.text.clear();        
        }
        sbPub->publish(speed_bump_nviz);
        speed_bump_nviz.markers.clear();
        drain_cover_sbPub->publish(drain_cover_nviz);
        pillarPub->publish(pillar_nviz);
        // visionOutlinePub->publish(vision_outline_nviz);
//    }
    
}


void VehOutlinesCallback(fusion_msgs::msg::StaticElements::SharedPtr vehOutlines)
{
    Eigen::Vector3d egoPositionTemp;
    Eigen::Vector2d egoMotionTemp;

    Eigen::Isometry3d transWorld2Ego = Eigen::Isometry3d::Identity();
    
    visualization_msgs::msg::MarkerArray vision_outline_nviz;

    visualization_msgs::msg::Marker objsDelete;
    objsDelete.type = visualization_msgs::msg::Marker::LINE_STRIP;
    objsDelete.action = visualization_msgs::msg::Marker::DELETEALL;
    const std_msgs::msg::Header header;
    vision_outline_nviz.markers.clear();
    vision_outline_nviz.markers.push_back(objsDelete);

    for (size_t i = 0; i < vehOutlines->num_of_elements; ++i) {
        visualization_msgs::msg::Marker objs;
        visualization_msgs::msg::Marker text;
        fusion_msgs::msg::StaticElement& obj = vehOutlines->elements_list[i];

        const uint8_t vision_outline_enum = 10;

        if (vision_outline_enum == obj.class_enum) {
            // only one element

            objs.header.frame_id = "world";
            objs.ns = "vision_outlines";
            objs.type = visualization_msgs::msg::Marker::LINE_STRIP;
            objs.action = visualization_msgs::msg::Marker::ADD;
            objs.scale.x = 0.1;
            objs.scale.y = 0.1;
            objs.pose.orientation.w = 1.0;
            objs.color.a = 1.0;
            objs.color.r = 0.0;
            objs.color.g = 1.0;
            objs.color.b = 1.0;
            if(!obj.num_of_vertices) {
                continue;
            }
            objs.id = obj.id;
            Eigen::Vector3d pointWorld(obj.vertices_list.front().x_m, obj.vertices_list.front().y_m, 0);
            Eigen::Vector3d pointEgo = transWorld2Ego * pointWorld;
            // double x = obj.vertices_list.front().x_m,
            //        y = obj.vertices_list.front().y_m,
            //        z = obj.vertices_list.front().z_m;

            for (int j = 0; j < obj.num_of_vertices; ++j)
            {
                Eigen::Vector3d pointWorld(obj.vertices_list[j].x_m,obj.vertices_list[j].y_m,0);
                Eigen::Vector3d pointEgo = transWorld2Ego * pointWorld;
                geometry_msgs::msg::Point pt;
                pt.x = pointEgo(0);
                pt.y = pointEgo(1);
                pt.z = 10;
                base_msgs::msg::Vertex p1, p2;
                p1.x_m = pt.x;
                p1.y_m = pt.y;
                p2.x_m = 0;
                p2.y_m = 0;
                objs.points.emplace_back(pt);
            }
            // objs.points.emplace_back(objs.points.front());
            geometry_msgs::msg::Pose pose;
            pose.position.x = (objs.points[0].x + objs.points[1].x) / 2;
            pose.position.y = (objs.points[0].y + objs.points[1].y) / 2;
            pose.position.z = 10;
            vision_outline_nviz.markers.emplace_back(objs);
            objs.points.clear();

            text.header.frame_id = "world";
            text.ns = "text";
            text.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
            text.action = visualization_msgs::msg::Marker::ADD;
            text.id = obj.id;
            text.pose.orientation.w = 1.0;
            text.scale.x = 0.5;
            text.scale.y = 0.5;
            text.scale.z = 0.5;
            text.color.a = 1.0;
            text.color.g = 1.0;
            text.color.r = 0.0;
            text.color.b = 0.0;
            text.text = std::to_string(obj.id);
            text.pose = pose;
            vision_outline_nviz.markers.emplace_back(text);
        } 
    }   
    visionOutlinePub->publish(vision_outline_nviz);

}

}


void alg_fusion_speed_bump_show_run(int argc, char** argv)
{
    // nlibcpp::init(argc, argv);
    std::cout<<"im in alg_fusion_speed_bump_show"<<std::endl;
    auto node = nlibcpp::Node::make_shared("alg_fusion_speed_bump_show");

    nlibcpp::QoS qos_profile = nlibcpp::QoS(5);
    qos_profile.best_effort();
    sbSub_zero = node->create_subscription<fusion_msgs::msg::StaticElements>("/alg/fusion/parking_elements_hmi_zero", qos_profile,
                                                                            SBCallback);    
    sbSub = node->create_subscription<fusion_msgs::msg::StaticElements>("/alg/fusion/parking_elements_hmi", qos_profile,
                                                                            SBCallback);
    vehOutlinesSub = node->create_subscription<fusion_msgs::msg::StaticElements>("/alg/fusion/visionOutlines", qos_profile,
                                                                            VehOutlinesCallback);                                                         
    odomzeroSub = node->create_subscription<loc_msgs::msg::Odometry>("/alg/loc/dr_zero", qos_profile,
                                                                  OdomCallback);
    odomSub = node->create_subscription<loc_msgs::msg::Odometry>("/alg/loc/dr", qos_profile,
                                                                  OdomCallback);
    sbPub = node->create_publisher<visualization_msgs::msg::MarkerArray>("/viz/fusion/speed_bump", 1);
    drain_cover_sbPub = node->create_publisher<visualization_msgs::msg::MarkerArray>("/viz/fusion/drain_cover", 1);
    pillarPub = node->create_publisher<visualization_msgs::msg::MarkerArray>("/viz/fusion/pillars", 1);
    visionOutlinePub = node->create_publisher<visualization_msgs::msg::MarkerArray>("/viz/fusion/vision_outlines", 1);

    nlibcpp::spin(node);

    nlibcpp::shutdown();

    // return 0;
}