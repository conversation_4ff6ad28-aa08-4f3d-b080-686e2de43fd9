
#include "ego_car_run.h"
#include <alg_nviz_utils/actions_depend_fsmout.hpp>
#include <ament_index_cpp/get_package_prefix.hpp>
#include <bits/stdint-uintn.h>
#include <cstddef>
#include <fsm_msgs/msg/detail/fsm_out__struct.hpp>
#include <functional>
#include <geometry_msgs/msg/detail/point__struct.hpp>
#include <geometry_msgs/msg/detail/pose__struct.hpp>
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <math.h>
#include <memory>
#include <nlibcpp/qos.hpp>
#include <nlibcpp/subscription.hpp>
#include <nlibcpp/time.hpp>
#include <planning_msgs/msg/detail/trajectory_out__struct.hpp>
#include <std_msgs/msg/detail/color_rgba__struct.hpp>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <tf2_nex/transform_broadcaster.h>
#include <loc_msgs/msg/odometry.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <visualization_msgs/msg/detail/marker__struct.hpp>
#include <visualization_msgs/msg/detail/marker_array__struct.hpp>
#include <fsm_msgs/msg/fsm_out.hpp>
// #include
#include "parameter_centralization.hpp"
#include "alg_nviz_utils/custom_env.hpp"

////////////// copy begin
struct Pose2f {
    Pose2f() : x(0.0), y(0.0), heading(0.0) {}
    Pose2f(float x1, float y1, float heading1) : x(x1), y(y1), heading(heading1) {}
    float x;
    float y;
    float heading;
};
///////////////// copy end

std::string ego_modelPath;
class OdometryShow : public nlibcpp::Node {
public:
    OdometryShow() : Node("odometry_show_node")
    {
        {
            nlibcpp::QoS qos_profile = nlibcpp::QoS(nlibcpp::QoSInitialization::from_ncom(ncom_qos_profile_default))
                                           .history(ncom_qos_history_policy_t::NCOM_QOS_POLICY_HISTORY_KEEP_LAST)
                                           .keep_last(10)
                                           .reliability(ncom_qos_reliability_policy_t::NCOM_QOS_POLICY_RELIABILITY_RELIABLE)
                                           .durability(ncom_qos_durability_policy_t::NCOM_QOS_POLICY_DURABILITY_VOLATILE)
                                           .avoid_nex_namespace_conventions(false);

            egoCarPub_ = create_publisher<visualization_msgs::msg::Marker>("/ego_car_marker", qos_profile);
            ego_occ_space_pub_ = create_publisher<visualization_msgs::msg::MarkerArray>(
                "/viz/ego_occ_space", nlibcpp::QoS(1).reliable());
            trajectory_out_occ_polygon_pub_ = create_publisher<visualization_msgs::msg::Marker>(
                "/viz/trajectory_out_occ_polygon", nlibcpp::QoS(1).reliable());
            effective_vision_area_rect_pub_ = create_publisher<visualization_msgs::msg::Marker>(
                "/viz/effective_vision_area_rect", nlibcpp::QoS(1).reliable());
        }
        pnc_prk_trajectory_helper_ = std::make_shared<alg_nviz_utils::PncPrkTrajectory>();
        {
            nlibcpp::QoS qos_profile = nlibcpp::QoS(5);
            qos_profile.best_effort();

            static auto subPosition2 = this->create_subscription<loc_msgs::msg::Odometry>(
                "/alg/loc/dr_zero", qos_profile, std::bind(&OdometryShow::PositionCallback, this, std::placeholders::_1));

            static auto subPosition = this->create_subscription<loc_msgs::msg::Odometry>(
                "/alg/loc/dr", qos_profile, std::bind(&OdometryShow::PositionCallback, this, std::placeholders::_1));

            fsmout_zero_sub_ = this->create_subscription<fsm_msgs::msg::FSMOut>(
                "/alg/pnc/fsm_out_zero", nlibcpp::QoS(10).best_effort(),
                std::bind(&alg_nviz_utils::PncPrkTrajectory::fsmout_callback,
                          pnc_prk_trajectory_helper_, std::placeholders::_1));
            fsmout_sub_ = this->create_subscription<fsm_msgs::msg::FSMOut>(
                "/alg/pnc/fsm_out", nlibcpp::QoS(10).best_effort(),
                std::bind(&alg_nviz_utils::PncPrkTrajectory::fsmout_callback,
                          pnc_prk_trajectory_helper_, std::placeholders::_1));
            pnc_trajectory_out_sub_ =
                this->create_subscription<planning_msgs::msg::TrajectoryOut>(
                    "/alg/pnc/trajectory_out", nlibcpp::QoS(10).best_effort(),
                    std::bind(&alg_nviz_utils::PncPrkTrajectory::
                                  pnc_trajectory_out_callback,
                              pnc_prk_trajectory_helper_,
                              std::placeholders::_1));
            prk_trajectory_out_sub_ =
                this->create_subscription<planning_msgs::msg::TrajectoryOut>(
                    "/alg/prk/trajectory_out", nlibcpp::QoS(10).best_effort(),
                    std::bind(&alg_nviz_utils::PncPrkTrajectory::
                                  prk_trajectory_out_callback,
                              pnc_prk_trajectory_helper_,
                              std::placeholders::_1));
            pnc_prk_trajectory_helper_->LinkFsmoutAndPncPrkTrajectory(
                std::bind(&OdometryShow::ParkingStatusCallback, this, std::placeholders::_1),
                nullptr, 
                std::bind(&OdometryShow::TrajectoryOutCallback, this, std::placeholders::_1),
                std::bind(&OdometryShow::TrajectoryOutCallback, this, std::placeholders::_1));

            tfBroadcaster_ = std::make_unique<tf2_nex::TransformBroadcaster>(*this);
            // odom_pub_ = create_publisher<nav_msgs::msg::Odometry>("/odometry", qos_profile);
            std::cout<<"im in here"<<std::endl;
        }
    }
    void ParkingStatusCallback(const fsm_msgs::msg::FSMOut::SharedPtr msg)
    {
        parkingstatus = msg->drv_prk_dual_info_enum;
    }
    void PositionCallback(const loc_msgs::msg::Odometry::SharedPtr msg)
    { //--use-sim-time 

        pose_ego_.x = msg->pose.position.x_m;
        pose_ego_.y = msg->pose.position.y_m;
        float w = msg->pose.orientation.w;
        float x = msg->pose.orientation.x;
        float y = msg->pose.orientation.y;
        float z = msg->pose.orientation.z;
        pose_ego_.heading = std::atan2(2 * (w * z + x * y), 1 - 2 * (y * y + z * z));

        geometry_msgs::msg::TransformStamped t;
        // nlibcpp::Time now = get_clock()->now();
        t.header.stamp = nlibcpp::Time(msg->std_header.timestamp_ns);
        // t.header.stamp = now;
        t.header.frame_id = "world";
        t.child_frame_id = "base_link";
        t.transform.translation.x = msg->pose.position.x_m;
        t.transform.translation.y = msg->pose.position.y_m;
        t.transform.translation.z = msg->pose.position.z_m;
        t.transform.rotation.x = msg->pose.orientation.x;
        t.transform.rotation.y = msg->pose.orientation.y;
        t.transform.rotation.z = msg->pose.orientation.z;
        t.transform.rotation.w = msg->pose.orientation.w;
        tfBroadcaster_->sendTransform(t);

    }

    void TrajectoryOutCallback(const planning_msgs::msg::TrajectoryOut::ConstSharedPtr msg) {
        if(!wire_frame_model_inited_) {
            return;
        }
        visualization_msgs::msg::MarkerArray ego_occ_space_markers;
        visualization_msgs::msg::Marker del_marker;
        del_marker.action = visualization_msgs::msg::Marker::DELETEALL;
        ego_occ_space_markers.markers.emplace_back(del_marker);

        visualization_msgs::msg::Marker trajectory_out_occ_polygon_marker;
        trajectory_out_occ_polygon_marker.header.frame_id = "base_link";
        trajectory_out_occ_polygon_marker.header.stamp = nlibcpp::Time(msg->std_header.timestamp_ns);
        trajectory_out_occ_polygon_marker.type = visualization_msgs::msg::Marker::LINE_STRIP;
        std_msgs::msg::ColorRGBA& polygon_color = trajectory_out_occ_polygon_marker.color;
        polygon_color.r = 65.0 / 255;
        polygon_color.g = 105.0 / 255;
        polygon_color.b = 225.0 / 255;
        polygon_color.a = 0.8;
        trajectory_out_occ_polygon_marker.scale.x = 0.1;
        // trajectory_out_occ_polygon_marker.lifetime = nlibcpp::Duration(1.0, 0);

        for(size_t i = 0; i < msg->traj_pts_list.size(); i++) {
            const auto& pt = FromGlobal2LocalCor(msg->traj_pts_list.at(i));
            //
            auto temp_ego_marker = ego_car_apa_box_marker_;
            temp_ego_marker.id = i;
            temp_ego_marker.header.stamp = nlibcpp::Time(msg->std_header.timestamp_ns);
            geometry_msgs::msg::Pose& pose = temp_ego_marker.pose;
            pose.position.x = pt.x_m;
            pose.position.y = pt.y_m;
            pose.position.z = pt.z_m;
            tf2::Quaternion q;
            q.setRPY(0.0, 0.0, pt.theta_rad);
            pose.orientation = tf2::toMsg(q);
            ego_occ_space_markers.markers.emplace_back(temp_ego_marker);
        }
        CalculateCurveBox(msg, trajectory_out_occ_polygon_marker.points);
        if(parkingstatus == 2)
        {
            ego_occ_space_pub_->publish(ego_occ_space_markers);
            trajectory_out_occ_polygon_pub_->publish(trajectory_out_occ_polygon_marker);
        }
    }

    void CalculateCurveBox(const planning_msgs::msg::TrajectoryOut::ConstSharedPtr msg,
        std::vector<geometry_msgs::msg::Point>& points) {
        if(msg->traj_pts_list.size() < 2) {
            return;
        }
        auto front = [](double x_m, double y_m, double z_m, double yaw, double front_m) -> 
            geometry_msgs::msg::Point {
            geometry_msgs::msg::Point p;
            p.x = x_m + front_m * cos(yaw);
            p.y = y_m + front_m * sin(yaw);
            p.z = z_m;
            return p;
        };
        auto rear = [](double x_m, double y_m, double z_m, double yaw, double rear_m) ->
            geometry_msgs::msg::Point {
            geometry_msgs::msg::Point p;
            p.x = x_m - rear_m * cos(yaw);
            p.y = y_m - rear_m * sin(yaw);
            p.z = z_m;
            return p;
        };
        auto left = [](double x_m, double y_m, double z_m, double yaw, double left_m) -> 
            geometry_msgs::msg::Point {
            geometry_msgs::msg::Point p;
            p.x = x_m - left_m * sin(yaw);
            p.y = y_m + left_m * cos(yaw);
            p.z = z_m;
            return p;
        };
        auto right = [](double x_m, double y_m, double z_m, double yaw, double right_m) -> 
            geometry_msgs::msg::Point {
            geometry_msgs::msg::Point p;
            p.x = x_m + right_m * sin(yaw);
            p.y = y_m - right_m * cos(yaw);
            p.z = z_m;
            return p;
        };


        std::list<geometry_msgs::msg::Point> right_border;
        const double rear_offset = wire_frame_model_paramter_.veh_rear_overhang_m;
        const double front_offset =  wire_frame_model_paramter_.veh_front_overhang_m +
            wire_frame_model_paramter_.veh_wheel_base_m;
        const double left_right_offset = wire_frame_model_paramter_.veh_width_without_mirror_m / 2.0;
        const uint8_t kGearR = 0x2, kGearD = 0x4;
        uint8_t last_but_one_gear_R_D = 0x0;
        for(size_t i = 0; i < msg->traj_pts_list.size() - 1; ++i) {
            const auto& traj = FromGlobal2LocalCor(msg->traj_pts_list.at(i));
            points.emplace_back(left(traj.x_m, traj.y_m, traj.z_m, traj.theta_rad, left_right_offset));
            right_border.emplace_front(right(traj.x_m, traj.y_m, traj.z_m, traj.theta_rad, left_right_offset));

            const auto& next_traj = FromGlobal2LocalCor(msg->traj_pts_list.at(i + 1));
            // 0x02 = R, 0x04 = D
            if((traj.gear_enum == kGearR && next_traj.gear_enum == kGearD) ||
                (traj.gear_enum == kGearD && next_traj.gear_enum == kGearR)) {
                geometry_msgs::msg::Point front_point = front(traj.x_m, traj.y_m,
                    traj.z_m, traj.theta_rad, front_offset);
                points.emplace_back(left(front_point.x, front_point.y, front_point.z, traj.theta_rad, left_right_offset));
                right_border.emplace_front(right(front_point.x, front_point.y, front_point.z, traj.theta_rad, left_right_offset));
            }
            last_but_one_gear_R_D = traj.gear_enum;
        }
        // last point
        const auto& last_traj = FromGlobal2LocalCor(msg->traj_pts_list.back());
        if(kGearD == last_but_one_gear_R_D) {
            // add front
            geometry_msgs::msg::Point front_point = front(last_traj.x_m, last_traj.y_m,
                last_traj.z_m, last_traj.theta_rad, front_offset);
            points.emplace_back(left(front_point.x, front_point.y, front_point.z, last_traj.theta_rad, left_right_offset));
            right_border.emplace_front(right(front_point.x, front_point.y, front_point.z, last_traj.theta_rad, left_right_offset));
        } else if(kGearR == last_but_one_gear_R_D) {
            // add rear
            geometry_msgs::msg::Point rear_point = rear(last_traj.x_m, last_traj.y_m,
                last_traj.z_m, last_traj.theta_rad, rear_offset);
            points.emplace_back(left(rear_point.x, rear_point.y, rear_point.z, last_traj.theta_rad, left_right_offset));
            right_border.emplace_front(right(rear_point.x, rear_point.y, rear_point.z, last_traj.theta_rad, left_right_offset));
        }

        points.reserve(points.size() + right_border.size());
        for(auto it = right_border.begin(); right_border.end() != it; ++it) {
            points.emplace_back(std::move(*it));
        }
    }

    std::unique_ptr<tf2_nex::TransformBroadcaster> tfBroadcaster_;
    nlibcpp::Publisher<nav_msgs::msg::Odometry>::SharedPtr odom_pub_;
    uint16_t parkingstatus;
public:
    struct WireFrameModelParameter {
        double veh_length_m = 0;                    // maybe no need.
        double veh_width_without_mirror_m = 0;
        double veh_wheel_base_m = 0;
        double veh_front_overhang_m = 0;
        double veh_rear_overhang_m = 0;
        double vision_area_length_m = 9.0;
        // not need below
        struct Wheel {
            double x = 0;
            double y = 0;
            double z = 0;
            double radius = 0;
        };
        Wheel left_front_wheel;
        Wheel right_front_wheel;
        Wheel right_rear_wheel;
        Wheel left_rear_wheel;
    };
    void CreateModel() {
        auto CreateDaeModel = [this]() {
            this->ego_car_marker_.header.frame_id = "base_link";
            this->ego_car_marker_.frame_locked = true;
            this->ego_car_marker_.ns = "ego";
            this->ego_car_marker_.id = 0;
            this->ego_car_marker_.scale.x = 1.0;
            this->ego_car_marker_.scale.y = 1.0;
            this->ego_car_marker_.scale.z = 1.0;
            this->ego_car_marker_.type = visualization_msgs::msg::Marker::MESH_RESOURCE;
            this->ego_car_marker_.action = visualization_msgs::msg::Marker::ADD;
            this->ego_car_marker_.mesh_resource = ego_modelPath;
            this->ego_car_marker_.mesh_use_embedded_materials = false;
            this->ego_car_marker_.pose.position.x = 1.9;
            this->ego_car_marker_.pose.position.y = 0.0;
            this->ego_car_marker_.pose.position.z = 0.0;
            this->ego_car_marker_.pose.orientation.x = 0.00056;
            this->ego_car_marker_.pose.orientation.y = 0.70682;
            this->ego_car_marker_.pose.orientation.z = 0.70738;
            this->ego_car_marker_.pose.orientation.w = 0.00056;
            this->ego_car_marker_.color.r = 220/255.0;
            this->ego_car_marker_.color.g = 220/255.0;
            this->ego_car_marker_.color.b = 221/255.0;
            this->ego_car_marker_.color.a = 0.5;
        };

        auto CreateWireFrameModel = [this]() {
            // https://yesv-desaysv.feishu.cn/docx/VlHEdkehgoEZucxT5M6csbV8nsb
            const double half_width = wire_frame_model_paramter_.veh_width_without_mirror_m / 2.0;
            const double front_length = wire_frame_model_paramter_.veh_wheel_base_m + wire_frame_model_paramter_.veh_front_overhang_m;
            std_msgs::msg::ColorRGBA box_color;
            box_color.r = 1.0;
            box_color.g = 1.0;
            box_color.b = 1.0;
            box_color.a = 1.0;

            visualization_msgs::msg::Marker &marker = this->ego_car_marker_apa_;
            marker.header.frame_id = "base_link";
            marker.ns = "ego_v2";
            marker.id = 0;
            marker.scale.x = 0.05;
            marker.type = visualization_msgs::msg::Marker::LINE_LIST;
            marker.color = box_color;

            ego_car_apa_box_marker_.header.frame_id = "base_link";
            ego_car_apa_box_marker_.frame_locked = false;
            ego_car_apa_box_marker_.ns = "ego_occ_space";
            ego_car_apa_box_marker_.scale.x = marker.scale.x;
            ego_car_apa_box_marker_.type = marker.type;
            // rgba(255, 153, 18, 0.2)
            ego_car_apa_box_marker_.color.r = 255.0 / 255;
            ego_car_apa_box_marker_.color.g = 153.0 / 255;
            ego_car_apa_box_marker_.color.b = 18.0 / 255;
            ego_car_apa_box_marker_.color.a = 0.2;
            // lenght width box
            {
                // 0 1
                // 3 2
                geometry_msgs::msg::Point p0, p1, p2, p3;
                p0.x = front_length;
                p0.y = half_width;
                p1.x = p0.x;
                p1.y = -p0.y;
                p2.x = -wire_frame_model_paramter_.veh_rear_overhang_m;
                p2.y = p1.y;
                p3.x = p2.x;
                p3.y = p0.y;
                marker.points.push_back(p0);
                marker.points.push_back(p1);
                marker.points.push_back(p1);
                marker.points.push_back(p2);
                marker.points.push_back(p2);
                marker.points.push_back(p3);
                marker.points.push_back(p3);
                marker.points.push_back(p0);

                ego_car_apa_box_marker_.points.push_back(p0);
                ego_car_apa_box_marker_.points.push_back(p1);
                ego_car_apa_box_marker_.points.push_back(p1);
                ego_car_apa_box_marker_.points.push_back(p2);
                ego_car_apa_box_marker_.points.push_back(p2);
                ego_car_apa_box_marker_.points.push_back(p3);
                ego_car_apa_box_marker_.points.push_back(p3);
                ego_car_apa_box_marker_.points.push_back(p0);
            }
            // header
            {
                //  0
                // 2 1
                geometry_msgs::msg::Point p0, p1, p2;
                p0.x = front_length;
                p0.y = 0;
                p1.x = wire_frame_model_paramter_.veh_wheel_base_m;
                p1.y = -half_width;
                p2.x = p1.x;
                p2.y = -p1.y;
                marker.points.push_back(p0);
                marker.points.push_back(p1);
                marker.points.push_back(p1);
                marker.points.push_back(p2);
                marker.points.push_back(p2);
                marker.points.push_back(p0);
            }
            // center
            {
                // 3 0 1
                //   2
                geometry_msgs::msg::Point p0, p1, p2, p3;
                p0.x = 0;
                p0.y = 0;
                p1.x = 0;
                p1.y = -half_width;
                p2.x = -wire_frame_model_paramter_.veh_rear_overhang_m;
                p2.y = 0;
                p3.x = 0;
                p3.y = half_width;
                marker.points.push_back(p0);
                marker.points.push_back(p2);
                marker.points.push_back(p1);
                marker.points.push_back(p3);
            }
            // TODO: wheel
            {
            }
        };

        auto CreateVisionArea = [this]() {
            visualization_msgs::msg::Marker & marker = this->effective_vision_area_rect_;
            marker.header.frame_id = "base_link";
            marker.ns = "vision_area";
            marker.id = 0;
            marker.scale.x = 0.05;
            marker.type = visualization_msgs::msg::Marker::LINE_LIST;
            std_msgs::msg::ColorRGBA & color = marker.color;
            color.r = 1.0;
            color.g = 0.0;
            color.b = 0.0;
            color.a = 0.2;

            // create rectangle
            // 0 1
            // 3 2
            geometry_msgs::msg::Point p0, p1, p2, p3;
            const auto length = wire_frame_model_paramter_.vision_area_length_m;
            p0.x =  length;
            p0.y =  length;
            p1.x =  length;
            p1.y = -length;
            p2.x = -length;
            p2.y = -length;
            p3.x = -length;
            p3.y =  length;
            marker.points.emplace_back(p0);
            marker.points.emplace_back(p1);
            marker.points.emplace_back(p1);
            marker.points.emplace_back(p2);
            marker.points.emplace_back(p2);
            marker.points.emplace_back(p3);
            marker.points.emplace_back(p3);
            marker.points.emplace_back(p0);
            // set pos in vehicle center
            geometry_msgs::msg::Pose & pose = marker.pose;
            pose.position.x = (wire_frame_model_paramter_.veh_length_m / 2.0) - wire_frame_model_paramter_.veh_rear_overhang_m;
            pose.position.y = 0;
            // pose.position.z = ;
        };

        // using wire_frame_model in apa only.
        if(alg_nviz_utils::CustomEnv::IsApa(false)) {
            parameter_client_ = std::make_shared<nexparameter::parameterCentralization::Client>(
                shared_from_this(), "/alg/parameter");
            parameter_client_->GetParameter();
            ParseParameter();
            CreateWireFrameModel();
            CreateVisionArea();
            wire_frame_model_inited_ = true;
        } else if(alg_nviz_utils::CustomEnv::IsP2p() || alg_nviz_utils::CustomEnv::IsP2p7v()) {
            parameter_client_ = std::make_shared<nexparameter::parameterCentralization::Client>(
                shared_from_this(), "/alg/parameter");
            parameter_client_->GetParameter();
            ParseParameter();
            CreateWireFrameModel();
            CreateVisionArea();
            wire_frame_model_inited_ = true;

            CreateDaeModel();
        } else {
            CreateDaeModel();
        }
    }

    void ParseParameter() {
        bool ok = true;
        ok = (ok ? parameter_client_->ParseParameter("veh_length_m", wire_frame_model_paramter_.veh_length_m) : false);
        ok = (ok ? parameter_client_->ParseParameter("veh_width_without_mirror_m", wire_frame_model_paramter_.veh_width_without_mirror_m) : false);
        ok = (ok ? parameter_client_->ParseParameter("veh_wheel_base_m", wire_frame_model_paramter_.veh_wheel_base_m) : false);
        ok = (ok ? parameter_client_->ParseParameter("veh_front_overhang_m", wire_frame_model_paramter_.veh_front_overhang_m) : false);
        ok = (ok ? parameter_client_->ParseParameter("veh_rear_overhang_m", wire_frame_model_paramter_.veh_rear_overhang_m) : false);

        std::vector<double> radius4;
        parameter_client_->ParseParameter("tire_radius_list_m", radius4);
        if(radius4.size() == 4) {
            wire_frame_model_paramter_.left_front_wheel.radius = radius4.at(0);
            wire_frame_model_paramter_.right_front_wheel.radius = radius4.at(1);
            wire_frame_model_paramter_.right_rear_wheel.radius = radius4.at(2);
            wire_frame_model_paramter_.left_rear_wheel.radius = radius4.at(3);
        }
        wire_frame_model_paramter_.vision_area_length_m = 9.0; // TODO:

#define EXTRACE_XYZ(wheel)                                                      \
        parameter_client_->ParseParameter(#wheel"_pos_m", pos);                 \
        if(pos.size() == 3) {                                                   \
            wire_frame_model_paramter_.wheel.x = pos.at(0);                     \
            wire_frame_model_paramter_.wheel.y = pos.at(1);                     \
            wire_frame_model_paramter_.wheel.z = pos.at(2);                     \
        }                                                                       \
        pos.clear();                                                            \
 
        std::vector<double> pos;
        EXTRACE_XYZ(left_front_wheel)
        EXTRACE_XYZ(left_front_wheel)
        EXTRACE_XYZ(left_front_wheel)
        EXTRACE_XYZ(left_front_wheel)
    }

    void EgoCarPub()
    {
        CreateModel();
        timer_ = this->create_wall_timer(std::chrono::seconds(1), [this](){
            nlibcpp::Time now = this->get_clock()->now();
            this->ego_car_marker_.header.stamp = now;
            this->ego_car_marker_apa_.header.stamp = now;
            this->effective_vision_area_rect_.header.stamp = now;
            if(alg_nviz_utils::CustomEnv::IsApa() || ((0x02 == parkingstatus) && 
                (alg_nviz_utils::CustomEnv::IsP2p() || alg_nviz_utils::CustomEnv::IsP2p7v()))) {
                egoCarPub_->publish(this->ego_car_marker_apa_);
                effective_vision_area_rect_pub_->publish(this->effective_vision_area_rect_);
            } else {
                egoCarPub_->publish(this->ego_car_marker_);
            }
        });
        nlibcpp::spin(this->shared_from_this());
        return;
    }

    float NormalizeAngle(const float angle) {
        float a = std::fmod(angle + M_PI, 2.0 * M_PI);
        if (a < 0.0) {
            a += (2.0 * M_PI);
        }
        return a - M_PI;
    }

    void FromGlobal2LocalCor(const Pose2f &ego, const Pose2f &global, Pose2f &local) {
        const float sin_ego_theta = std::sin(ego.heading);
        const float cos_ego_theta = std::cos(ego.heading);
        local.x = (global.x - ego.x) * cos_ego_theta + (global.y - ego.y) * sin_ego_theta;
        local.y = (global.y - ego.y) * cos_ego_theta - (global.x - ego.x) * sin_ego_theta;
        local.heading = NormalizeAngle(global.heading - ego.heading);
    }

    planning_msgs::msg::PathPoint FromGlobal2LocalCor(const planning_msgs::msg::PathPoint& local) {
        Pose2f global, local2f;
        global.x = local.x_m;
        global.y = local.y_m;
        global.heading = local.theta_rad;
        FromGlobal2LocalCor(pose_ego_, global, local2f);
        planning_msgs::msg::PathPoint pp = local;
        pp.x_m = local2f.x;
        pp.y_m = local2f.y;
        pp.theta_rad = local2f.heading;
        return pp;
    }
    Pose2f pose_ego_;

    mutable bool wire_frame_model_inited_  = false;
    nlibcpp::Publisher<visualization_msgs::msg::Marker>::SharedPtr egoCarPub_;
    nlibcpp::Publisher<visualization_msgs::msg::MarkerArray>::SharedPtr ego_occ_space_pub_;
    nlibcpp::Publisher<visualization_msgs::msg::Marker>::SharedPtr trajectory_out_occ_polygon_pub_;
    nlibcpp::Publisher<visualization_msgs::msg::Marker>::SharedPtr effective_vision_area_rect_pub_;
    visualization_msgs::msg::Marker ego_car_marker_;
    visualization_msgs::msg::Marker ego_car_marker_apa_;
    visualization_msgs::msg::Marker ego_car_apa_box_marker_;
    visualization_msgs::msg::Marker effective_vision_area_rect_;
    nlibcpp::TimerBase::SharedPtr timer_;
    std::shared_ptr<nexparameter::parameterCentralization::Client> parameter_client_;
    WireFrameModelParameter wire_frame_model_paramter_;
    nlibcpp::Subscription<planning_msgs::msg::TrajectoryOut>::SharedPtr pnc_trajectory_out_sub_;
    nlibcpp::Subscription<planning_msgs::msg::TrajectoryOut>::SharedPtr prk_trajectory_out_sub_;
    nlibcpp::Subscription<fsm_msgs::msg::FSMOut>::SharedPtr fsmout_sub_;
    nlibcpp::Subscription<fsm_msgs::msg::FSMOut>::SharedPtr fsmout_zero_sub_;
    std::shared_ptr<alg_nviz_utils::PncPrkTrajectory> pnc_prk_trajectory_helper_;
};


void ego_car_run(int argc, char *argv[])
{
    (void)argc;
    (void)argv;
    std::cout<<"im in ego_car_run"<<std::endl;
    std::shared_ptr<OdometryShow> node = std::make_shared<OdometryShow>();
    std::string package_path = ament_index_cpp::get_package_prefix("alg_odometry_show");
    ego_modelPath = "file://" + package_path + "/share/alg_odometry_show/config/car.dae";  
    std::cout<<"modelPath :"<<ego_modelPath<<std::endl;
    node->EgoCarPub();
    nlibcpp::shutdown();
}
