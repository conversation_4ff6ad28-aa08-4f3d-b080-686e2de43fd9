#include "nviz_visualization_tool.h"

#include "alg_nviz_utils/custom_env.hpp"


void NvizVisualizer::Init(const std::string topic, nlibcpp::Node *node) {
  default_pub_ = node->create_publisher<visualization_msgs::msg::MarkerArray>(topic, 5);

  delete_marker_.header.frame_id = "base_link";
  delete_marker_.action = visualization_msgs::msg::Marker::DELETEALL;
  delete_marker_.id = 20;

}

void NvizVisualizer::PlotPath(const std::vector<Pose2f> &path,
                              const Color &color, const float line_width,
                              const int32_t id, const LineType line_type) {
  if (path.empty()) {
    return;
  }

  pub_buffer_.markers.emplace_back();
  auto &new_path_marker = pub_buffer_.markers.back();
  new_path_marker.header.frame_id = "base_link";
  new_path_marker.ns = "pnc_infer";
  // new_path_marker.lifetime = nlibcpp::Duration(0, 5e8);
  new_path_marker.action = visualization_msgs::msg::Marker::ADD;

  if (line_type == LineType::Dashed) {
    new_path_marker.type = visualization_msgs::msg::Marker::LINE_LIST;
  } else {
    new_path_marker.type = visualization_msgs::msg::Marker::LINE_STRIP;
  }

  new_path_marker.scale.x = line_width;
  new_path_marker.scale.y = 0.1;
  new_path_marker.scale.z = 0.1;

  new_path_marker.color = color.toRGB();
  new_path_marker.id = id;
  new_path_marker.pose.orientation.w = 1.0;

  new_path_marker.points.reserve(path.size());
  for (const auto &pt : path) {
    new_path_marker.points.emplace_back();
    new_path_marker.points.back().x = pt.x;
    new_path_marker.points.back().y = pt.y;
    new_path_marker.points.back().z = 0.0;
  }
  if (line_type == LineType::Dashed
      && new_path_marker.points.size() % 2U != 0) {
    new_path_marker.points.pop_back();
  }
  if(!alg_nviz_utils::CustomEnv::IsIcaDebug()) {
    // avoid stack storage full
    const float storage = static_cast<float>(sizeof(pub_buffer_)) / std::pow(1024.0, 2.0);
    if (storage > 1.0) {
      NvizVisualizer::Show();
      std::cout << "pub_buffer storage > 1 MB" << std::endl;
    }
  } else {
    // do nothing.
  }
}

void NvizVisualizer::PlotText(const std::string& text, const Color &color, 
    const float text_size, const Pose2f pose, const int32_t id) {
  pub_buffer_.markers.emplace_back();
  auto &new_path_marker = pub_buffer_.markers.back();
  new_path_marker.header.frame_id = "base_link";
  new_path_marker.ns = "infer_lane";
  // new_path_marker.lifetime = nlibcpp::Duration(0, 5e8);
  new_path_marker.action = visualization_msgs::msg::Marker::ADD;
  new_path_marker.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;

  new_path_marker.scale.x = text_size;
  new_path_marker.scale.y = text_size;
  new_path_marker.scale.z = text_size;
  new_path_marker.color = color.toRGB();

  new_path_marker.id = id;
  new_path_marker.pose.orientation.z = 0;
  new_path_marker.pose.orientation.w = 0;

  new_path_marker.text = text.c_str();
  new_path_marker.pose.position.x = pose.x;
  new_path_marker.pose.position.y = pose.y;
  new_path_marker.pose.position.z = 0.0f;
}

void NvizVisualizer::Show() {
  if (pub_buffer_.markers.empty()) {
    return;
  }
  if(!alg_nviz_utils::CustomEnv::IsIcaDebug())
  {
    // std::cout<<"pnc_infer_buffer_size"<<pub_buffer_.markers.size();
    default_pub_->publish(pub_buffer_);
    pub_buffer_.markers.clear();
    pub_buffer_.markers.emplace_back(delete_marker_);
    return;
  }

  // ica debug process
  tarHistoryFrames.push_back(pub_buffer_);
  pub_buffer_.markers.clear();
  if(tarHistoryFrames.size()>10)
  {
      tarHistoryFrames.pop_front();
  }
  // std::cout<<"===tarHistoryFrames.size"<<tarHistoryFrames.size()<<std::endl;
  static int32_t nsid = 10;
  visualization_msgs::msg::MarkerArray allTargetFramesToPublish;
  delete_marker_.id = nsid;
  allTargetFramesToPublish.markers.emplace_back(delete_marker_);
  int ns_index = 0;
  for (auto& frame : tarHistoryFrames)
  {
      ++ ns_index;
      for (auto& marker : frame.markers)
      {
          marker.action = 0;
          marker.id = ++nsid;
          marker.scale.x = 0.1;
          marker.scale.y = 0.1;
          marker.scale.z = 0.1;
          // marker.lifetime = nlibcpp::Duration(0, 0);
          marker.frame_locked = true;
          auto tmp_marker = marker;
          if(!marker.ns.empty())
          {
            tmp_marker.ns = marker.ns + " " + std::to_string(ns_index);
            InferGetColor(ns_index,tmp_marker);
            allTargetFramesToPublish.markers.push_back(tmp_marker);   
          }
      }
  }
  default_pub_->publish(std::move(allTargetFramesToPublish));
}