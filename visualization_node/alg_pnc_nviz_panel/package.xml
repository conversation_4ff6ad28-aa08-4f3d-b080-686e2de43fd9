<?xml version="1.0"?>
<?xml-model href="http://download.nex.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>alg_pnc_nviz_panel</name>
  <version>0.0.0</version>
  <description>Create plugins for nviz, like playing panel. eg.</description>
  <maintainer email="<EMAIL>">luzixu</maintainer>
  <license>TODO: License declaration</license>

  <buildtool_depend>ament_cmake_nex</buildtool_depend>

  <depend>pluginlib</depend>
  <depend>nviz_common</depend>
  <depend>geometry_msgs</depend>
  <depend>nlibcpp</depend>
  <depend>ament_index_cpp</depend>
  <depend>fsm_msgs</depend>
  <depend>control_msgs</depend>
  <depend>vehicle_msgs</depend>
  <depend>p_msgs</depend>
  <depend>uss_msgs</depend>
  <depend>std_msgs</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
