#include <stdio.h>
#include "play_panel.h"

class QLineEdit;

// 定义和初始化静态成员变量
const std::unordered_map<int, std::string> Nviz::PncPanel::TeleopPanel::vehicleModelToFsmTopic = {
    {0, "/alg/pnc/fsm_out_zero"},
    {1, "/alg/pnc/fsm_out_zero"},
    {2, "/alg/pnc/fsm_out_zero"},
    {3, "/alg/pnc/fsm_out"},
    {4, "/alg/pnc/fsm_out_zero"}
};

const std::unordered_map<int, std::string> Nviz::PncPanel::TeleopPanel::vehicleModelToControlTopic = {
    {0, "/alg/pnc/control_out_zero"},
    {1, "/alg/pnc/control_out_zero"},
    {2, "/alg/pnc/control_out_zero"},
    {3, "/alg/pnc/control_out"},
    {4, "/alg/pnc/control_out_zero"}
};

const std::unordered_map<int, std::string> Nviz::PncPanel::TeleopPanel::vehicleModelToVehicleChassisTopic = {
    {0, "/vehicle_chassis_10ms_zero"},
    {1, "/vehicle_chassis_10ms_zero"},
    {2, "/vehicle_chassis_10ms_zero"},
    {3, "/vehicle_chassis_10ms"},
    {4, "/vehicle_chassis_10ms_zero"}
};

const std::unordered_map<int, std::string> Nviz::PncPanel::TeleopPanel::vehicleModelToImuTopic = {
    {0, "/sensor/pos/imuinfo_zero"},
    {1, "/sensor/pos/imuinfo_zero"},
    {2, "/sensor/pos/imuinfo_zero"},
    {3, "/sensor/pos/imuinfo"},
    {4, "/sensor/pos/imuinfo_zero"}
};

const std::unordered_map<int, std::string> Nviz::PncPanel::TeleopPanel::vehicleModelToUssTopic = {
    {0, "/sensor/uss_zero"},
    {1, "/sensor/uss_zero"},
    {2, "/sensor/uss_zero"},
    {3, "/sensor/uss"},
    {4, "/sensor/uss_zero"}
};

namespace Nviz {
namespace PncPanel {
// Constructor, initialize variables
TeleopPanel::TeleopPanel( QWidget* parent )
    : nviz_common::Panel(parent), directionKeysEnabled(false),
    stoped_(true), commandTimer_(nullptr), commandIndex_(0), 
    ussAlarmTextEdit_(nullptr), imuAlarmTextEdit_(nullptr),
    nh_(nlibcpp::Node::make_shared("nviz_panel_node", NEX_NODE_VERSION)) 
{
    UpdateTopics();

    SetPanelLayout();
    Start();
    setFocusPolicy(Qt::StrongFocus);

    OnBrakeSlopeRequestCaseChanged(0);
    OnBrakeStepRequestCaseChanged(0);
    OnAcclDcclCaseChanged(0);
    OnSteerSlopeCaseChanged(0);
    OnSteerSinCaseChanged(0);

    // 加载上次的车型选择
    QPair<int, int> lastVehicleModels = LoadVehicleModelSelection();
    drv_vehicleModelComboBox_->setCurrentIndex(lastVehicleModels.first);
    prk_vehicleModelComboBox_->setCurrentIndex(lastVehicleModels.second);
}

void TeleopPanel::UpdateTopics() {
    std::lock_guard<std::mutex> lock(topic_mutex_);

    // 根据车辆模型代号获取对应的 topic 名称
    int currentVehicleModel = vehicle_model_;
    std::string fsmTopic = vehicleModelToFsmTopic.at(currentVehicleModel);
    std::string controlTopic = vehicleModelToControlTopic.at(currentVehicleModel);
    std::string vehicleChassisTopic = vehicleModelToVehicleChassisTopic.at(currentVehicleModel);
    std::string imuTopic = vehicleModelToImuTopic.at(currentVehicleModel);
    std::string ussTopic = vehicleModelToUssTopic.at(currentVehicleModel);

    // 重新创建 publisher 和 subscriber
    global_fsm_out_ = nh_->create_publisher<fsm_msgs::msg::FSMOut>(fsmTopic, 50);
    timer_for_fsm_pub_ = nh_->create_wall_timer(std::chrono::milliseconds(50), std::bind(&TeleopPanel::PublishFsmReq, this));

    global_control_out_ = nh_->create_publisher<control_msgs::msg::ControlOut>(controlTopic, 10);
    timer_for_control_pub_ = nh_->create_wall_timer(std::chrono::milliseconds(10), std::bind(&TeleopPanel::PublishControlReq, this));

    vehicle_chassis_sub_ = nh_->create_subscription<vehicle_msgs::msg::VehicleChassis>(
        vehicleChassisTopic, nlibcpp::QoS(10).best_effort(), std::bind(&TeleopPanel::VehicleChassisCallback, this, std::placeholders::_1));

    imu_sub_ = nh_->create_subscription<p_msgs::msg::IMU>(
        imuTopic, nlibcpp::QoS(10).best_effort(), std::bind(&TeleopPanel::IMUCallback, this, std::placeholders::_1));

    uss_sub_ = nh_->create_subscription<uss_msgs::msg::USSInfo>(
        ussTopic, nlibcpp::QoS(10).best_effort(), std::bind(&TeleopPanel::USSCallback, this, std::placeholders::_1));
}

TeleopPanel::~TeleopPanel() {
    Stop();

    if (commandTimer_) {
        commandTimer_->stop();
        delete commandTimer_;
    }

    if (ussAlarmTextEdit_) {
        delete ussAlarmTextEdit_; 
    }

    if (imuAlarmTextEdit_) {
        delete imuAlarmTextEdit_; 
    }

    // 保存当前的车型选择
    int drvVehicleModel = drv_vehicleModelComboBox_->currentIndex();
    int prkVehicleModel = prk_vehicleModelComboBox_->currentIndex();
    SaveVehicleModelSelection(drvVehicleModel, prkVehicleModel);
}

void TeleopPanel::Start() {
    if(!stoped_) {
        return;
    }
    stoped_ = false;
    thread_ = std::thread(&TeleopPanel::Run, this);
}

void TeleopPanel::Stop() {
    stoped_ = true;
    if(thread_.joinable()) {
        thread_.join();
    }
}

void TeleopPanel::Run() {
    nlibcpp::spin(nh_);
    nlibcpp::shutdown();
    stoped_ = true;
}

void TeleopPanel::USSCallback(const uss_msgs::msg::USSInfo::SharedPtr msg) {
    if (!msg) {
        std::cout << "uss_msgs is not nullptr!" << std::endl;
        return;
    }

    ussAlarmMessage_.clear();

    uss_time_ = msg->std_header.timestamp_ns;
    for (size_t i = 0; i < msg->uss_direct_echos_list.size(); ++i) {
        int16_t is_uss_echo_available = msg->uss_direct_echos_list[i].uss_direct_echo_info.is_uss_echo_available;
        int64_t first_echo_timestamp = msg->uss_direct_echos_list[i].uss_direct_echo_info.first_echo_timestamp_us * 1000;
        float first_echo_distance = msg->uss_direct_echos_list[i].uss_direct_echo_info.first_echo_distance_mm;

        CheckUSSStatus(i, is_uss_echo_available, first_echo_timestamp, first_echo_distance);
    }

    emit updateUSSAlarmMessage(ussAlarmMessage_);
}

void TeleopPanel::IMUCallback(const p_msgs::msg::IMU::SharedPtr msg) {
    if (!msg) {
        std::cout << "p_msgs is not nullptr!" << std::endl;
        return;
    }

    imu_time_ = msg->std_header.timestamp_ns;
    accel_z_ = msg->accel_z_m_s2;
    imu_synctime_ = msg->imu_synctime_us * 1000;

    imuAlarmMessage_.clear();
    CheckIMUStatus();
    emit updateIMUAlarmMessage(imuAlarmMessage_);
}

void TeleopPanel::VehicleChassisCallback(const vehicle_msgs::msg::VehicleChassis::SharedPtr msg) {
    if (!msg) {
        std::cout << "vehicle_msgs is not nullptr!" << std::endl;
        return;
    }
    
    vehicle_chassis_time_ = msg->std_header.timestamp_ns;
    actv_gear_enum_ = msg->pt.gear.actv_gear_enum;  
    vehspd_kph_ = msg->vehdyn.vehspd.vehspd_kph;
    long_accl_m_s2_ = msg->vehdyn.vehaccl.long_accl_m_s2;
    lat_accl_m_s2_ = msg->vehdyn.vehaccl.lat_accl_m_s2;
    vehdyn_yawrate_deg_s_ = msg->vehdyn.vehaccl.vehdyn_yawrate_deg_s;
    epb_sts_ = msg->brksys.prkbrk.epbsts_enum;
}

QString TeleopPanel::USSPosition(int uss_i) {
    static const QString positions[] = {
        "front_left_side",    // 0
        "front_left_corner",  // 1
        "front_left_mid",     // 2
        "front_right_mid",    // 3
        "front_right_corner", // 4
        "front_right_side",   // 5
        "rear_right_side",    // 6
        "rear_right_corner",  // 7
        "rear_right_mid",     // 8
        "rear_left_mid",      // 9
        "rear_left_corner",   // 10
        "rear_left_side"      // 11
    };

    static const int num_positions = sizeof(positions) / sizeof(positions[0]);
    if (uss_i >= 0 && uss_i < num_positions) {
        return positions[uss_i];
    } else {
        return "unknown_position";
    }
}

void TeleopPanel::CheckUSSStatus(int uss_i_, int16_t is_uss_echo_available_, int64_t first_echo_timestamp_, float first_echo_distance_) {
    QString ussTimestampAlarm;
    QString ussDistanceAlarm;

    QString ussPositionDescription = USSPosition(uss_i_);
    if (is_uss_echo_available_ == 1 && std::abs(uss_time_ - first_echo_timestamp_) > 5 * 1000 * 1000 * 1000) {
        ussTimestampAlarm = QString("USS timestamp and first echo timestamp difference exceeds 5s! "
                                    "\nUSS position: %1"
                                    "\nUSS timestamp_ns: %2 ns"
                                    "\nUSS first_echo_timestamp_us: %3 ns")
                                    .arg(ussPositionDescription).arg(uss_time_).arg(first_echo_timestamp_);
    }

    if (is_uss_echo_available_ == 1 && first_echo_distance_ < 1) {
        ussDistanceAlarm = QString("USS first echo distance is invalid! "
                                   "\nUSS position: %1"
                                   "\nUSS first_echo_distance_mm: %2 mm")
                                   .arg(ussPositionDescription).arg(first_echo_distance_);
    }

    ussAlarmMessage_ += ussTimestampAlarm + "\n" + ussDistanceAlarm;
}

void TeleopPanel::CheckIMUStatus() {
    QString imuTimestampAlarm;
    QString imuAccelZAlarm;
    QString imuChassisTimestampAlarm;

    if (std::abs(imu_time_ - imu_synctime_) > 100 * 1000 * 1000) { 
        imuTimestampAlarm = QString("IMU timestamp and synctime difference exceeds 100ms! "
                                "\nIMU timestamp_ns: %1 ns"
                                "\nIMU imu_synctime_us: %2 ns")
                                .arg(imu_time_).arg(imu_synctime_);
    }

    if (std::abs(accel_z_) <= 5.0) {
        imuAccelZAlarm = QString("IMU acceleration Z-axis does not exceed 5.0m/s2! "
                                "\nIMU accel_z_m_s2: %1 m/s2")
                                .arg(accel_z_);
    }

    if (std::abs(imu_time_ - vehicle_chassis_time_) > 100 * 1000 * 1000) {
        imuChassisTimestampAlarm = QString("IMU timestamp and chassis timestamp difference exceeds 100ms! "
                                "\nIMU timestamp_ns: %1 ns"
                                "\nVehicleChassis timestamp_ns: %2 ns")
                                .arg(imu_time_).arg(vehicle_chassis_time_);
    }

    imuAlarmMessage_ += imuTimestampAlarm + "\n" + imuAccelZAlarm + "\n" + imuChassisTimestampAlarm;
}

void TeleopPanel::onAlarmMessageUpdated(const QString& message, QTextEdit* alarmTextEdit) {
    if (alarmTextEdit) {
        alarmTextEdit->clear();
        alarmTextEdit->setPlainText(message);

        QTextDocument* document = alarmTextEdit->document();
        QTextCursor cursor(document);
        QTextCharFormat format;
        format.setForeground(Qt::black);
        cursor.select(QTextCursor::Document);
        cursor.mergeCharFormat(format);

        QStringList alarmMessages = {
            "IMU timestamp and synctime difference exceeds 100ms!",
            "IMU acceleration Z-axis does not exceed 5.0m/s2!",
            "IMU timestamp and chassis timestamp difference exceeds 100ms!",
            "USS timestamp and first echo timestamp difference exceeds 5s!",
            "USS first echo distance is invalid!"
        };

        for (const QString& alarm : alarmMessages) {
            if (message.contains(alarm)) {
                QTextCursor alarmCursor(document);
                QTextCharFormat alarmFormat;
                alarmFormat.setForeground(Qt::red);

                int startIndex = 0;
                while (startIndex != -1) {
                    startIndex = message.indexOf(alarm, startIndex);
                    if (startIndex != -1) {
                        int length = alarm.length();
                        alarmCursor.setPosition(startIndex);
                        alarmCursor.setPosition(startIndex + length, QTextCursor::KeepAnchor);
                        alarmCursor.mergeCharFormat(alarmFormat);

                        startIndex += length;
                    }
                }
            }
        }
    }
}

void TeleopPanel::PublishFsmReq() {
    std::lock_guard<std::mutex> lock(fsm_counter_mutex_);
    auto fsm_msg_ = global_fsm_out_->borrow_loaned_message();
    auto time = sys_clock_.now();
    fsm_msg_.get().std_header.timestamp_ns = time.nanoseconds();
    fsm_msg_.get().std_header.counter = fsm_counter_;
    fsm_msg_.get().std_header.pub_cycle_time_ms = 50;
    fsm_msg_.get().veh_signals.eps_handshake_req_enum = lathandshakereq_;
    fsm_msg_.get().veh_signals.vcu_vlc_override_req_enum = lgthandshakereq_;
    fsm_msg_.get().veh_signals.turn_light_req_enum = turn_light_req_val_;
    fsm_msg_.get().veh_signals.epb_req_enum = epb_req_val_;
    fsm_msg_.get().panel_signals.driving_cruising_panel.driving_cruising_func_txt.driving_disp_info_enum = driving_disp_info_;
    fsm_msg_.get().panel_signals.driving_cruising_panel.driving_cruising_func_txt.driving_failure_reason_enum = driving_failure_reason_;
    fsm_msg_.get().panel_signals.driving_cruising_panel.driving_cruising_func_txt.driving_passive_reason_enum = driving_passive_reason_;
    fsm_msg_.get().panel_signals.driving_cruising_panel.driving_cruising_func_txt.driving_abort_reason_enum = driving_abort_reason_;
    fsm_msg_.get().panel_signals.parking_panel.parking_func_txt.parking_disp_info_enum = parking_disp_info_;
    fsm_msg_.get().panel_signals.parking_panel.parking_func_txt.parking_failure_reason_enum = parking_failure_reason_;
    fsm_msg_.get().panel_signals.parking_panel.parking_func_txt.parking_passive_reason_enum = parking_passive_reason_;
    fsm_msg_.get().panel_signals.parking_panel.parking_func_txt.parking_abort_reason_enum = parking_abort_reason_;
    fsm_msg_.get().panel_signals.parking_panel.parking_func_txt.parking_suspend_reason_enum = parking_suspend_reason_;
    if (vehicle_model_ == 1 || vehicle_model_ == 3) {
        if (epb_sts_ == 1 && actv_gear_enum_ != 4) {
            fsm_msg_.get().veh_signals.epb_req_enum = 1;
        } else {
            fsm_msg_.get().veh_signals.epb_req_enum = 0;
        }
    }
    fsm_msg_.get().veh_signals.autohold_req_enum = auto_hold_req_val_;

    if (lgthandshakereq_ == 4 && vehspd_kph_ != 0) {
        fsm_msg_.get().panel_signals.driving_cruising_panel.driving_cruising_func_sts.acc_sts_enum = 3;
    } else if (lgthandshakereq_ == 4 && vehspd_kph_ == 0) {
        fsm_msg_.get().panel_signals.driving_cruising_panel.driving_cruising_func_sts.acc_sts_enum = 4;
    } else {
        fsm_msg_.get().panel_signals.driving_cruising_panel.driving_cruising_func_sts.acc_sts_enum = 1;
    }
    
    global_fsm_out_->publish(std::move(fsm_msg_));
    ++fsm_counter_;
    if (fsm_counter_ >= 255) fsm_counter_ = 0;
}

void TeleopPanel::SetControlMessage(control_msgs::msg::ControlOut& control_msg) {
    control_msg.long_signal.is_vcu_toq_req_enabled = is_vcu_toq_req_enabled_;
    control_msg.long_signal.vcu_act_toq_req_nm = toq_req_val_;
    control_msg.long_signal.is_gear_shift_req_enabled = is_gear_shift_req_enabled_;
    control_msg.long_signal.target_gear_req_enum = target_gear_req_val_;
    control_msg.long_signal.dccl_mode_req_enum = dccl_mode_req_val_;
    control_msg.long_signal.dccl_req_val_m_s2 = accl_req_val_;
    control_msg.long_signal.brk_req_val_m_s2 = brk_req_val_;
    control_msg.long_signal.brk_mode_req_enum = brk_mode_req_val_;
    control_msg.long_signal.is_drive_off_req = drive_off_req_val_;
    control_msg.long_signal.is_stst_req = stst_req_val_;
    control_msg.lat_signal.tgt_pnn_ang_req_deg = wheelang_req_val_;
}

void TeleopPanel::SetPrkTorqueInterfaceControlMessage(control_msgs::msg::ControlOut& control_msg) {
    control_msg.long_signal.is_gear_shift_req_enabled = 1;
    control_msg.long_signal.target_gear_req_enum = target_gear_req_val_;
    control_msg.long_signal.brk_req_val_m_s2 = 0.0;
    control_msg.long_signal.brk_mode_req_enum = 0;           
    if (accl_req_val_ >= 0.0) {
        control_msg.long_signal.dccl_req_val_m_s2 = 0.3;
        control_msg.long_signal.is_vcu_toq_req_enabled = 1;
        control_msg.long_signal.vcu_act_toq_req_nm = toq_req_val_;
    } else {
        control_msg.long_signal.dccl_req_val_m_s2 = accl_req_val_;
        control_msg.long_signal.is_vcu_toq_req_enabled = 0;
        control_msg.long_signal.vcu_act_toq_req_nm = 0.0;
    }
    control_msg.long_signal.is_drive_off_req = (accl_req_val_ > 0.0 && vehspd_kph_ == 0) ? 1 : 0;
    control_msg.long_signal.is_stst_req = stst_req_val_;
}

void TeleopPanel::SetPrkAccelerationInterfaceControlMessage(control_msgs::msg::ControlOut& control_msg) {
    control_msg.long_signal.is_gear_shift_req_enabled = 1;
    control_msg.long_signal.target_gear_req_enum = target_gear_req_val_;
    control_msg.long_signal.brk_req_val_m_s2 = 0.0;
    control_msg.long_signal.brk_mode_req_enum = 0;    
    control_msg.long_signal.dccl_req_val_m_s2 = accl_req_val_;
    control_msg.long_signal.is_vcu_toq_req_enabled = 0;
    control_msg.long_signal.vcu_act_toq_req_nm = 0.0;
    control_msg.long_signal.is_drive_off_req = (accl_req_val_ > 0.0 && vehspd_kph_ == 0) ? 1 : 0;
    control_msg.long_signal.is_stst_req = (accl_req_val_ <= 0.0 && vehspd_kph_ == 0) ? 1 : 0;
}

void TeleopPanel::SetDrvTorqueInterfaceControlMessage(control_msgs::msg::ControlOut& control_msg) {
    control_msg.long_signal.is_gear_shift_req_enabled = 0;
    control_msg.long_signal.target_gear_req_enum = 4;
    control_msg.long_signal.dccl_req_val_m_s2 = 0.0;
    control_msg.long_signal.brk_mode_req_enum = (brk_req_val_ < 0.0) ? 2 : 0;
    if (brk_req_val_ >= 0.0) {
        control_msg.long_signal.brk_req_val_m_s2 = 0.3;
        control_msg.long_signal.is_vcu_toq_req_enabled = 1;
        control_msg.long_signal.vcu_act_toq_req_nm = toq_req_val_;
    } else {
        control_msg.long_signal.brk_req_val_m_s2 = brk_req_val_;
        control_msg.long_signal.is_vcu_toq_req_enabled = 0;
        control_msg.long_signal.vcu_act_toq_req_nm = 0.0;
    }
    control_msg.long_signal.is_drive_off_req = (brk_req_val_ > 0.0 && vehspd_kph_ == 0) ? 1 : 0;
    control_msg.long_signal.is_stst_req = stst_req_val_;
}

void TeleopPanel::SetDrvAccelerationInterfaceControlMessage(control_msgs::msg::ControlOut& control_msg) {
    control_msg.long_signal.is_gear_shift_req_enabled = 0;
    control_msg.long_signal.target_gear_req_enum = 4;
    control_msg.long_signal.brk_req_val_m_s2 = brk_req_val_;
    control_msg.long_signal.brk_mode_req_enum = (brk_req_val_ < 0.0) ? 2 : 0;
    control_msg.long_signal.dccl_req_val_m_s2 = 0.0;
    control_msg.long_signal.is_vcu_toq_req_enabled = 0;
    control_msg.long_signal.vcu_act_toq_req_nm = 0.0;
    control_msg.long_signal.is_drive_off_req = (brk_req_val_ > 0.0 && vehspd_kph_ == 0) ? 1 : 0;
    control_msg.long_signal.is_stst_req = stst_req_val_;
}

void TeleopPanel::UpdateBrakeSlopeRequest() {
    if (!brk_slope_button_flag_) {
        return;
    }

    float accl_time_ = brk_max_var_ / brk_slope_var_;
    float dccl_time_ = (brk_max_var_ - brk_min_var_) / brk_slope_var_;
    if (brk_slope_start_time_ <= accl_time_) {
        brk_req_val_ =  brk_slope_var_ * brk_slope_start_time_;
    } else if (brk_slope_start_time_ <= accl_time_ + dccl_time_) {
        brk_req_val_ = brk_max_var_ - brk_slope_var_ * (brk_slope_start_time_ - accl_time_);
    } else {
        brk_req_val_ = brk_min_var_;
    }
    drv_brkslider_->setValue(static_cast<float>(brk_req_val_ * 100));

    if (vehicle_model_ == 0 || vehicle_model_ == 3) {
        if (brk_req_val_ > 0.0) {
            toq_req_val_ = (veh_full_mass_kg * brk_req_val_ + 
                            0.5 * drag_coefficient * air_windward_area_m2 * 1.225 * pow(vehspd_kph_ / 3.6, 2) +
                            rolling_resistance_coefficient * veh_empty_mass_kg * 9.81) * tire_radius_list_m;
        } else {
            toq_req_val_ = 0.0;
        }
        toq_req_val_ = clamp(toq_req_val_, 0.0, 3000.0);
        drv_toqslider_->setValue(static_cast<float>(toq_req_val_ * 100));
    }
    
    brk_slope_start_time_ += 0.01;
}

void TeleopPanel::UpdateBrakeStepRequest() {
    if (!brk_step_button_flag_) {
        return;
    }

    brk_req_val_ = brk_step_var_;
    drv_brkslider_->setValue(static_cast<float>(brk_req_val_ * 100));
    brk_step_start_time_ += 0.01;
}

void TeleopPanel::UpdateSteerRequest() {
    if (!steer_button_flag_) {
        return;
    }

    float steer_limit = (std::atan((max_lat_acc * veh_wheel_base_m) / pow(vehspd_kph_ / 3.6, 2)) * steer_transmission_ratio) * (180 / M_PI);
    float steer_max_time_ = fabs(steer_max_var_) / steer_ramp_var_;
    float wheelang_req_val_new = (steer_start_time_ <= steer_max_time_) ? (steer_ramp_var_ * steer_start_time_) : fabs(steer_max_var_);
    wheelang_req_val_new = std::min(fabs(wheelang_req_val_new), fabs(steer_limit));
    if (steer_max_var_ < 0.0) {
        wheelang_req_val_new = -wheelang_req_val_new;
    }

    if (fabs(wheelang_req_val_new - wheelang_req_val_prev_) > (fabs(steer_max_var_) / 100)) {
        wheelang_req_val_ = (wheelang_req_val_new > wheelang_req_val_prev_) ? 
                            (wheelang_req_val_prev_ + fabs(steer_max_var_) / 100) : 
                            (wheelang_req_val_prev_ - fabs(steer_max_var_) / 100);
    } else {
        wheelang_req_val_ = wheelang_req_val_new;
    }

    wheelang_req_val_prev_ = wheelang_req_val_;
    drv_wheelangslider_->setValue(static_cast<float>(wheelang_req_val_ * 100));
    steer_start_time_ += 0.01;
}

void TeleopPanel::UpdateAcclDcclRequest() {
    if (!accl_dccl_button_flag_) {
        return;
    }

    if (accl_dccl_start_time_ <= 1.0) {
        accl_req_val_ = accl_coeffs3_var_;
    } else if (accl_dccl_start_time_ <= 3.0) {
        accl_req_val_ = accl_coeffs1_var_ * pow(accl_dccl_start_time_ - 1.0, 2) + accl_coeffs2_var_ * (accl_dccl_start_time_ - 1.0) + accl_coeffs3_var_;
    } else if (accl_dccl_start_time_ <= 5.0) {
        accl_req_val_ = 0.0;
    } else {
        accl_req_val_ = -0.1;
    }
    acclslider_->setValue(static_cast<float>(accl_req_val_ * 100));

    if (vehicle_model_ == 0 || vehicle_model_ == 4) {
        if (accl_req_val_ > 0.0) {
            toq_req_val_ = (veh_full_mass_kg * accl_req_val_ + 
                            0.5 * drag_coefficient * air_windward_area_m2 * 1.225 * pow(vehspd_kph_ / 3.6, 2) +
                            rolling_resistance_coefficient * veh_empty_mass_kg * 9.81) * tire_radius_list_m;
        } else {
            toq_req_val_ = 0.0;
        }
        toq_req_val_ = clamp(toq_req_val_, 0.0, 300.0);
        toqslider_->setValue(static_cast<float>(toq_req_val_ * 100));
    }
    
    accl_dccl_start_time_ += 0.01;
}

void TeleopPanel::UpdateSlopeSteerRequest() {
    if (!slope_button_flag_) {
        return;
    }

    float rise_time_ = steer_back_max_var_ / steer_slope_var_;
    float flat_time_ = (steer_flat_time_var_ >= 2.0) ? steer_flat_time_var_ : 2.0;
    float fall_time_ = steer_back_max_var_ / steer_slope_var_; 
    float new_wheelang_req_val = 0.0;
    if (slope_start_time_ < rise_time_) {
        new_wheelang_req_val = steer_slope_var_ * slope_start_time_;
    } else if (slope_start_time_ < (rise_time_ + flat_time_)) {
        new_wheelang_req_val = steer_back_max_var_;
    } else if (slope_start_time_ < (rise_time_ + flat_time_ + fall_time_)) {
        new_wheelang_req_val = steer_back_max_var_ - steer_slope_var_ * (slope_start_time_ - (rise_time_ + flat_time_));
    } else {
        new_wheelang_req_val = 0.0;
    }

    static float filtered_wheelang_req_val = 0.0; 
    filtered_wheelang_req_val = filter_alpha_var_ * new_wheelang_req_val + (1 - filter_alpha_var_) * filtered_wheelang_req_val;
    wheelang_req_val_ = filtered_wheelang_req_val;
    wheelangslider_->setValue(static_cast<float>(wheelang_req_val_ * 100));
    slope_start_time_ += 0.01;
}

void TeleopPanel::UpdateSinSteerRequest() {
    if (!sin_button_flag_) {
        return;
    }

    steer_sin_band_var_ = (steer_sin_band_var_ >= 3) ? steer_sin_band_var_ : 3;
    float total_time = steer_sin_band_var_ * (1.0 / steer_sin_hz_var_);
    if (sin_start_time_ < total_time) {
        wheelang_req_val_ = steer_sin_max_var_ * std::sin(2.0 * M_PI * steer_sin_hz_var_ * sin_start_time_);
    } else {
        wheelang_req_val_ = 0.0;
    }
    wheelangslider_->setValue(static_cast<float>(wheelang_req_val_ * 100));
    sin_start_time_ += 0.01;
}

void TeleopPanel::PublishControlReq() {
    std::lock_guard<std::mutex> lock(control_counter_mutex_);
    auto control_msg_ = global_control_out_->borrow_loaned_message();
    auto time = sys_clock_.now();
    control_msg_.get().std_header.timestamp_ns = time.nanoseconds();
    control_msg_.get().std_header.counter = control_counter_;
    control_msg_.get().std_header.pub_cycle_time_ms = 10;

    UpdateBrakeSlopeRequest();
    UpdateBrakeStepRequest();
    UpdateSteerRequest();
    UpdateAcclDcclRequest();
    UpdateSlopeSteerRequest();
    UpdateSinSteerRequest();

    if (button_flag_) {
        SetControlMessage(control_msg_.get());
    } else {
        control_msg_.get().lat_signal.tgt_pnn_ang_req_deg = wheelang_req_val_;
        control_msg_.get().long_signal.dccl_mode_req_enum = 0;
        if (vehicle_model_ == 0) {  // A19
            if (is_prk_mode_ == 1) {
                SetPrkTorqueInterfaceControlMessage(control_msg_.get());
            } else if (is_prk_mode_ == 0) {
                SetDrvTorqueInterfaceControlMessage(control_msg_.get());
            } 
        } else if (vehicle_model_ == 1) {  // T22
            if (is_prk_mode_ == 1) {
                SetPrkAccelerationInterfaceControlMessage(control_msg_.get());
            } else if (is_prk_mode_ == 0) {
                SetDrvAccelerationInterfaceControlMessage(control_msg_.get());
            } 
        } else if (vehicle_model_ == 2) {  // Z10
            if (is_prk_mode_ == 1) {
                SetPrkAccelerationInterfaceControlMessage(control_msg_.get());
            } else if (is_prk_mode_ == 0) {
                SetDrvAccelerationInterfaceControlMessage(control_msg_.get());
            } 
        } else if (vehicle_model_ == 3) {  // C01
            if (is_prk_mode_ == 1) {
                SetPrkAccelerationInterfaceControlMessage(control_msg_.get());
            } else if (is_prk_mode_ == 0) {
                SetDrvTorqueInterfaceControlMessage(control_msg_.get());
            } 
        } else if (vehicle_model_ == 4) {  // E01
            if (is_prk_mode_ == 1) {
                SetPrkTorqueInterfaceControlMessage(control_msg_.get());
            } 
        }
    }

    global_control_out_->publish(std::move(control_msg_));
    ++control_counter_;
    if (control_counter_ >= 255) control_counter_ = 0;
}

float TeleopPanel::clamp(float value, float min_value, float max_value) {
    return std::min(std::max(value, min_value), max_value);
}

void TeleopPanel::keyPressEvent(QKeyEvent* event) {
    if(!directionKeysEnabled) {
        event->ignore();
        return;
    }

    switch (event->key()) {
        case Qt::Key_Up:
            std::cout << "Up arrow key pressed" << std::endl;
            accl_req_val_ = clamp(acclslider_->value() / 100.0 + accl_var_, -5.0, 5.0);
            acclslider_->setValue(static_cast<float>(accl_req_val_ * 100));
            event->accept();
            break;
        case Qt::Key_Down:
            std::cout << "Down arrow key pressed" << std::endl;
            accl_req_val_ = clamp(acclslider_->value() / 100.0 - accl_var_, -5.0, 5.0);
            acclslider_->setValue(static_cast<float>(accl_req_val_ * 100));
            event->accept();
            break;
        case Qt::Key_Left:
            std::cout << "Left arrow key pressed" << std::endl;
            wheelang_req_val_ = clamp(wheelangslider_->value() / 100.0 + wheelang_var_, -500.0, 500.0);
            wheelangslider_->setValue(static_cast<float>(wheelang_req_val_ * 100));
            event->accept();
            break;
        case Qt::Key_Right:
            std::cout << "Right arrow key pressed" << std::endl;
            wheelang_req_val_ = clamp(wheelangslider_->value() / 100.0 - wheelang_var_, -500.0, 500.0);
            wheelangslider_->setValue(static_cast<float>(wheelang_req_val_ * 100));
            event->accept();
            break;
        case Qt::Key_Control:
            std::cout << "Ctrl key pressed" << std::endl;
            accl_req_val_ = clamp(const_accl_var_, -5.0, 5.0);
            acclslider_->setValue(static_cast<float>(accl_req_val_ * 100));
            event->accept();
            break;
        default:
            std::cout << "Unhandled key press: " << event->key() << std::endl;
            // QWidget::keyPressEvent(event);
            // event->ignore();
            break;
    }
}

void TeleopPanel::OnDirectionCheckboxChanged(int state) {
    directionKeysEnabled = (state == Qt::Checked);
}

void TeleopPanel::OnTabChanged(int index) {
    if (index == 0) { // Driving tab
        is_prk_mode_ = 0;
        vehicle_model_ = drv_vehicleModelComboBox_->currentIndex(); 
    } else if (index == 1) { // Parking tab
        is_prk_mode_ = 1;
        vehicle_model_ = prk_vehicleModelComboBox_->currentIndex();
    } else {
        is_prk_mode_ = 2;
    }

    ResetControlReq();
    std::cout << "drv_prk_mode updated to: " << is_prk_mode_ << " , " << "vehicle_model updated to: " << vehicle_model_ << std::endl;
}

// 定义配置文件路径
static QString getConfigFilePath() {
    std::string package_path = ament_index_cpp::get_package_prefix("alg_pnc_nviz_panel");
    std::string configFilePath = package_path + "/config/vehicle_model.json";

    std::filesystem::path configDir = std::filesystem::path(package_path) / "config";
    if (!std::filesystem::exists(configDir)) {
        std::filesystem::create_directories(configDir);
    }

    std::cout << "config_path: " << configFilePath << std::endl;

    return QString::fromStdString(configFilePath);
}

// 保存车型选择到配置文件
void TeleopPanel::SaveVehicleModelSelection(int drvVehicleModel, int prkVehicleModel) {
    QString configFilePath = getConfigFilePath();
    QFile configFile(configFilePath);
    if (!configFile.open(QIODevice::ReadWrite)) {
        qWarning() << "Failed to open config file for writing:" << configFilePath;
        return;
    }

    QJsonObject config;
    if (configFile.size() > 0) {
        QByteArray jsonData = configFile.readAll();
        QJsonDocument doc = QJsonDocument::fromJson(jsonData);
        if (!doc.isNull() && doc.isObject()) {
            config = doc.object();
        }
    }

    config["drv_vehicle_model"] = drvVehicleModel;
    config["prk_vehicle_model"] = prkVehicleModel;
    QJsonDocument doc(config);
    configFile.resize(0);
    configFile.write(doc.toJson());
    configFile.close();
}

// 从配置文件读取车型选择
QPair<int, int> TeleopPanel::LoadVehicleModelSelection() {
    QString configFilePath = getConfigFilePath();
    QFile configFile(configFilePath);
    if (!configFile.exists()) {
        return qMakePair(0, 0); // 默认值
    }

    if (!configFile.open(QIODevice::ReadOnly)) {
        qWarning() << "Failed to open config file for reading:" << configFilePath;
        return qMakePair(0, 0);
    }

    QByteArray jsonData = configFile.readAll();
    QJsonDocument doc = QJsonDocument::fromJson(jsonData);
    if (!doc.isNull() && doc.isObject()) {
        QJsonObject config = doc.object();
        int drvVehicleModel = config.contains("drv_vehicle_model") && config["drv_vehicle_model"].isDouble() ? config["drv_vehicle_model"].toInt() : 0;
        int prkVehicleModel = config.contains("prk_vehicle_model") && config["prk_vehicle_model"].isDouble() ? config["prk_vehicle_model"].toInt() : 0;
        return qMakePair(drvVehicleModel, prkVehicleModel);
    }

    return qMakePair(0, 0); // 默认值
}

void TeleopPanel::OnVehicleModelChanged(int index) {
    QString selectedModel;
    if (sender() == drv_vehicleModelComboBox_) {
        selectedModel = drv_vehicleModelComboBox_->currentText();
    } else if (sender() == prk_vehicleModelComboBox_) {
        selectedModel = prk_vehicleModelComboBox_->currentText();
    } else {
        return;
    }
    std::cout << "Selected Vehicle Model: " << selectedModel.toStdString() << std::endl;

    vehicle_model_ = index; 
    std::cout << "vehicle_model updated to: " << vehicle_model_ << std::endl;

    UpdateTopics();
}

void TeleopPanel::AddComboBoxLayout(QVBoxLayout* vLayout, const QString& label, QComboBox*& comboBox, const QStringList& items, const char* slot) {
    QHBoxLayout* hLayout = new QHBoxLayout();
    QLabel* labelWidget = new QLabel(label);
    comboBox = new QComboBox(this);
    comboBox->addItems(items);
    hLayout->addWidget(labelWidget);
    hLayout->addWidget(comboBox);
    vLayout->addLayout(hLayout);
    connect(comboBox, SIGNAL(currentIndexChanged(int)), this, slot);
}

void TeleopPanel::UpdateSliderValue(QSlider* slider, float value, const char* valueName) {
    std::lock_guard<std::mutex> lock(controlMutex);
    if (strcmp(valueName, "ang_req_deg") == 0) {
        wheelang_req_val_ = value;
    } else if (strcmp(valueName, "accl_req_m_s2") == 0) {
        accl_req_val_ = value;
    } else if (strcmp(valueName, "toq_req_nm") == 0) {
        toq_req_val_ = value;
    } else if (strcmp(valueName, "brk_req_m_s2") == 0) {
        brk_req_val_ = value;
    }
    std::cout << "current " << valueName << " request is: " << value << std::endl;
}

void TeleopPanel::AddSliderLayout(QVBoxLayout* vLayout, const QString& groupBoxTitle, const QString& labelName, QSlider*& slider, QLabel*& valueLabel, int min, int max, int singleStep, int pageStep, int defaultValue, const char* valueName) {
    QGroupBox* groupBox = new QGroupBox(groupBoxTitle, this);
    QVBoxLayout* groupBoxLayout = new QVBoxLayout(groupBox);
    
    QHBoxLayout* hLayout = new QHBoxLayout();
    valueLabel = new QLabel(labelName + ": 0.00");
    valueLabel->setAlignment(Qt::AlignLeft);
    valueLabel->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    hLayout->addWidget(valueLabel);

    slider = new QSlider(Qt::Horizontal);
    slider->setMinimum(min);
    slider->setMaximum(max);
    slider->setSingleStep(singleStep);
    slider->setPageStep(pageStep);
    slider->setValue(defaultValue);
    hLayout->addWidget(slider);

    groupBoxLayout->addLayout(hLayout);
    vLayout->addWidget(groupBox);

    connect(slider, &QSlider::valueChanged, this, [this, slider, valueLabel, valueName](int value) {
        float floatValue = value * 0.01;
        valueLabel->setText(QString("%1: %2").arg(valueName).arg(floatValue, 0, 'f', 2));
        UpdateSliderValue(slider, floatValue, valueName);
    });
}

void TeleopPanel::AddButtonGroup(QVBoxLayout* vLayout, const std::initializer_list<std::pair<QString, std::function<void()>>>& buttons) {
    QHBoxLayout* hLayout = new QHBoxLayout();
    hLayout->setSpacing(5);
    hLayout->setContentsMargins(0, 0, 0, 0);

    for (const auto& buttonInfo : buttons) {
        QPushButton* button = new QPushButton(buttonInfo.first, this);
        connect(button, &QPushButton::clicked, [buttonInfo]() {
            buttonInfo.second();
        });
        hLayout->addWidget(button);
    }

    vLayout->addLayout(hLayout);
}

void TeleopPanel::AddButtonAndLabelGroup(QVBoxLayout* vLayout, const QString& groupBoxTitle, const std::initializer_list<std::pair<QString, std::function<void()>>>& buttons) {
    QGroupBox* groupBox = new QGroupBox(groupBoxTitle, this);
    QVBoxLayout* groupBoxLayout = new QVBoxLayout(groupBox);
    
    QHBoxLayout* hLayout = new QHBoxLayout();
    hLayout->setSpacing(5);
    hLayout->setContentsMargins(0, 0, 0, 0);

    for (const auto& buttonInfo : buttons) {
        QPushButton* button = new QPushButton(buttonInfo.first, this);
        connect(button, &QPushButton::clicked, [buttonInfo]() {
            buttonInfo.second();
        });
        hLayout->addWidget(button);
    }

    groupBoxLayout->addLayout(hLayout);
    vLayout->addWidget(groupBox);
}

void TeleopPanel::AddAlarmLabelAndTextEdit(QVBoxLayout* vLayout, const QString& label, QTextEdit*& textEdit) {
    QHBoxLayout* hLayout = new QHBoxLayout();
    QLabel* alarmLabel = new QLabel(label, this);
    textEdit = new QTextEdit(this);
    textEdit->setReadOnly(true);
    textEdit->setMinimumHeight(100);
    hLayout->addWidget(alarmLabel);
    hLayout->addWidget(textEdit);
    vLayout->addLayout(hLayout);
}

void TeleopPanel::AddTextInputLayout(QVBoxLayout* vLayout, const std::initializer_list<std::tuple<QString, QLineEdit**, const char*>>& inputs) {
    QHBoxLayout* hLayout = new QHBoxLayout();
    for (const auto& input : inputs) {
        QString label = std::get<0>(input);
        QLineEdit** lineEdit = std::get<1>(input);
        const char* slot = std::get<2>(input);
        *lineEdit = new QLineEdit(this);
        hLayout->addWidget(new QLabel(label));
        hLayout->addWidget(*lineEdit);
        connect(*lineEdit, SIGNAL(editingFinished()), this, slot);
    }
    vLayout->addLayout(hLayout);
}

void TeleopPanel::SetPanelLayout() {
    tabWidget = new QTabWidget(this);

    QWidget* drivingWidget = new QWidget();
    QWidget* parkingWidget = new QWidget();
    QWidget* commonWidget = new QWidget();

    QVBoxLayout* drivingLayout = new QVBoxLayout(drivingWidget);
    drivingLayout->setAlignment(Qt::AlignTop);
    drivingLayout->setSpacing(5);

    QStringList vehicleModels = {"GAC_HYPER_A19_BEV", "CHERRY_EXEED_T22_PHEV", "GEELY_LYNKCO_Z10_BEV", "GWM_WEY_C01_BEV", "CHERY_FENGYUN_E01_PHEV"};
    AddComboBoxLayout(drivingLayout, "VehicleModel Selection / 车型选择", drv_vehicleModelComboBox_, vehicleModels, SLOT(OnVehicleModelChanged(int)));

    AddButtonGroup(drivingLayout, {
        {"LateralHandShake / 请求横控握手", [this]() { PubLatDrivingHandShake(); }},
        {"CancelLateralHandShake / 取消横控握手", [this]() { PubAllCancelHandShake(); }}
    });

    AddButtonGroup(drivingLayout, {
        {"LongitudinalHandShake / 请求纵控握手", [this]() { PubLgtDrivingHandShake(); }},
        {"CancelLongitudinalHandShake / 取消纵控握手", [this]() { PubAllCancelHandShake(); }}
    });

    AddButtonGroup(drivingLayout, {
        {"AllHandShake / 请求横纵控同时握手", [this]() { PubAllDrivingHandShake(); }},  
        {"CancelAllHandShake / 取消横纵控握手", [this]() { PubAllCancelHandShake(); }}
    });

    AddSliderLayout(drivingLayout, "Torque Request / 扭矩请求", "toq_req_nm", drv_toqslider_, toqValueLabel_, 0, 300000, 1, 1000, 0, "toq_req_nm");
    AddSliderLayout(drivingLayout, "Acceleration Request / 加速度请求", "brk_req_m_s2", drv_brkslider_, brkValueLabel_, -500, 500, 1, 10, 0, "brk_req_m_s2");
    AddSliderLayout(drivingLayout, "Steering Angle Request / 方向盘转角请求", "ang_req_deg", drv_wheelangslider_, wheelangValueLabel_, -50000, 50000, 1, 100, 0, "ang_req_deg");

    AddButtonAndLabelGroup(drivingLayout, "Gear Request / 档位请求", {
        {"Park", [this]() { PubGearReq(1); }},
        {"Reverse", [this]() { PubGearReq(2); }},
        {"Neutral", [this]() { PubGearReq(3); }},
        {"Drive", [this]() { PubGearReq(4); }}
    });

    QStringList drv_testModes = {"Normal Mode / 正常模式", "TestCase Mode / 用例模式"};
    AddComboBoxLayout(drivingLayout, "TestMode Selection / 测试模式选择", drv_testModeComboBox_, drv_testModes, SLOT(OnTestModeChanged(int)));

    drv_groupBox = new QGroupBox("TestCase / 验证用例", drivingLayout->parentWidget());
    QVBoxLayout* drv_groupBoxLayout = new QVBoxLayout(drv_groupBox);

    AddButtonGroup(drv_groupBoxLayout, {
        {"BrakeSlopeRequest / 制动斜坡请求", [this]() { OnBrakeSlopeButtonClicked(); }},
        {"BrakeStepRequest / 制动阶跃请求", [this]() { OnBrakeStepButtonClicked(); }},
        {"SteerRequest / 转向请求", [this]() { OnSteerButtonClicked(); }},
    });

    QStringList brakeSlopeRequestCases = {"Case1: k = 0.12, max = 0.0, minBrakeReq = -0.6m/s^2", "Case2: k = 0.2, max = 0.0, minBrakeReq = -1.0m/s^2", 
                                          "Case3: k = 0.4, max = 0.0, minBrakeReq = -2.0m/s^2", "Case4: k = 0.6, max = 0.0, minBrakeReq = -3.0m/s^2", 
                                          "Case5: k = 0.8, max = 0.0, minBrakeReq = -4.0m/s^2", "Case6: k = 0.12, max = 0.6, minBrakeReq = -0.6m/s^2", 
                                          "Case7: k = 0.2, max = 1.0, minBrakeReq = -1.0m/s^2", "Case8: k = 0.4, max = 1.0, minBrakeReq = -2.0m/s^2", 
                                          "Case9: k = 0.6, max = 1.0, minBrakeReq = -3.0m/s^2", "Case10: k = 0.8, max = 1.0, minBrakeReq = -4.0m/s^2"};
    AddComboBoxLayout(drv_groupBoxLayout, "BrakeSlopeRequest TestCase Selection / 制动斜坡测试用例选择", brakeSlopeRequestComboBox_, brakeSlopeRequestCases, SLOT(OnBrakeSlopeRequestCaseChanged(int)));

    QStringList brakeStepRequestCases = {"Case1: maxBrakeReq = -1.0m/s^2", "Case2: maxBrakeReq = -2.0m/s^2", 
                                         "Case3: maxBrakeReq = -3.0m/s^2", "Case4: maxBrakeReq = -4.0m/s^2", 
                                         "Case5: maxBrakeReq = -5.0m/s^2", "Case6: maxBrakeReq = -6.0m/s^2"};
    AddComboBoxLayout(drv_groupBoxLayout, "BrakeStepRequest TestCase Selection / 制动阶跃测试用例选择", brakeStepRequestComboBox_, brakeStepRequestCases, SLOT(OnBrakeStepRequestCaseChanged(int)));
    
    AddTextInputLayout(drv_groupBoxLayout, {
        std::make_tuple("Steer ramp var / 最大转角加速度", &steer_ramp_var_req_, SLOT(UpdatePubSteerRampVar())),
        std::make_tuple("Steer max var / 最大转角", &steer_max_var_req_, SLOT(UpdatePubSteerMaxVar())),
    });

    drv_groupBox->hide(); 
    drivingLayout->addWidget(drv_groupBox);

    QVBoxLayout* parkingLayout = new QVBoxLayout(parkingWidget);
    parkingLayout->setAlignment(Qt::AlignTop);
    parkingLayout->setSpacing(5);

    AddComboBoxLayout(parkingLayout, "VehicleModel Selection / 车型选择", prk_vehicleModelComboBox_, vehicleModels, SLOT(OnVehicleModelChanged(int)));

    AddButtonGroup(parkingLayout, {
        {"LateralHandShake / 请求横控握手", [this]() { PubLatParkingHandShake(); }},
        {"CancelLateralHandShake / 取消横控握手", [this]() { PubAllCancelHandShake(); }}
    });

    AddButtonGroup(parkingLayout, {
        {"LongitudinalHandShake / 请求纵控握手", [this]() { PubLgtParkingHandShake(); }},
        {"CancelLongitudinalHandShake/取消纵控握手", [this]() { PubAllCancelHandShake(); }}
    });

    AddButtonGroup(parkingLayout, {
        {"AllHandShake / 请求横纵控同时握手", [this]() { PubAllParkingHandShake(); }},
        {"CancelAllHandShake / 取消横纵控握手", [this]() { PubAllCancelHandShake(); }}
    });

    AddSliderLayout(parkingLayout, "Torque Request / 扭矩请求", "toq_req_nm", toqslider_, toqValueLabel_, 0, 300000, 1, 1000, 0, "toq_req_nm");
    AddSliderLayout(parkingLayout, "Acceleration Request / 加速度请求", "accl_req_m_s2", acclslider_, acclValueLabel_, -500, 500, 1, 10, 0, "accl_req_m_s2");
    AddSliderLayout(parkingLayout, "Steering Angle Request / 方向盘转角请求", "ang_req_deg", wheelangslider_, wheelangValueLabel_, -50000, 50000, 1, 100, 0, "ang_req_deg");

    AddButtonAndLabelGroup(parkingLayout, "Gear Request / 档位请求", {
        {"Park", [this]() { PubGearReq(1); }},
        {"Reverse", [this]() { PubGearReq(2); }},
        {"Neutral", [this]() { PubGearReq(3); }},
        {"Drive", [this]() { PubGearReq(4); }}
    });

    QStringList prk_testModes = {"Normal Mode / 正常模式", "TestCase Mode / 用例模式"};
    AddComboBoxLayout(parkingLayout, "TestMode Selection / 测试模式选择", prk_testModeComboBox_, prk_testModes, SLOT(OnTestModeChanged(int)));

    prk_groupBox = new QGroupBox("TestCase / 验证用例", parkingLayout->parentWidget());
    QVBoxLayout* prk_groupBoxLayout = new QVBoxLayout(prk_groupBox);

    AddButtonGroup(prk_groupBoxLayout, {
        {"Accl_Dccl / 加减速请求", [this]() { OnAcclDcclButtonClicked(); }},
        {"Slope_Steer / 斜坡阶跃转角请求", [this]() { OnSlopeSteerButtonClicked(); }},
        {"Sin_Steer / 正弦扫频转角请求", [this]() { OnSinSteerButtonClicked(); }}
    });

    QStringList acclDcclCases = {"Case1: a = -0.15, b = 0.25, c = 0.1", "Case2: a = -0.2, b = 0.3, c = 0.2", 
                                 "Case3: a = -0.25, b = 0.35, c = 0.3", "Case4: a = -0.3, b = 0.4, c = 0.4", 
                                 "Case5: a = -0.35, b = 0.45, c = 0.5"};
    AddComboBoxLayout(prk_groupBoxLayout, "Accl_Dccl TestCase Selection / 加减速测试用例选择", acclDcclComboBox_, acclDcclCases, SLOT(OnAcclDcclCaseChanged(int)));

    QStringList steerSlopeCases = {"Case1: k = 50deg/s, maxSteeringAngleReq = 360deg", "Case2: k = 100deg/s, maxSteeringAngleReq = 360deg", 
                                   "Case3: k = 200deg/s, maxSteeringAngleReq = 360deg", "Case4: k = 300deg/s, maxSteeringAngleReq = 360deg", 
                                   "Case5: k = 400deg/s, maxSteeringAngleReq = 360deg", "Case6: k = 500deg/s, maxSteeringAngleReq = 360deg", 
                                   "Case7: k = 600deg/s, maxSteeringAngleReq = 360deg", "Case8: k = 360deg/s, maxSteeringAngleReq = 200deg"};
    AddComboBoxLayout(prk_groupBoxLayout, "Slope_Steer TestCase Selection / 斜坡阶跃测试用例选择", steerSlopeComboBox_, steerSlopeCases, SLOT(OnSteerSlopeCaseChanged(int)));

    QStringList steerSinCases = {"Case1: f = 0.1hz, steeringAngleReqA = 300deg", "Case2: f = 0.2hz, steeringAngleReqA = 200deg", 
                                 "Case3: f = 0.5hz, steeringAngleReqA = 100deg", "Case4: f = 1hz, steeringAngleReqA = 50deg", 
                                 "Case5: f = 2hz, steeringAngleReqA = 25deg"};
    AddComboBoxLayout(prk_groupBoxLayout, "Sin_Steer TestCase Selection / 正弦扫频测试用例选择", steerSinComboBox_, steerSinCases, SLOT(OnSteerSinCaseChanged(int)));  

    AddButtonGroup(prk_groupBoxLayout, {
        {"APA ParkOut / APA自动泊出", [this]() { OnLoadParkOutDataButtonClicked(); }},
    });

    prk_groupBox->hide(); 
    parkingLayout->addWidget(prk_groupBox);

    // AddTextInputLayout(parkingLayout, {
    //     std::make_tuple("accl var:", &accl_var_req_, SLOT(UpdatePubAcclVar())),
    //     std::make_tuple("wheelang var:", &wheelang_var_req_, SLOT(UpdatePubWheelangVar())),
    //     std::make_tuple("const accl var:", &const_accl_var_req_, SLOT(UpdatePubConstAcclVar()))
    // });

    // QCheckBox* prk_directionCheckBox = new QCheckBox("Enable Direction Keys", this);
    // prk_directionCheckBox->setChecked(false);
    // parkingLayout->addWidget(prk_directionCheckBox);
    // connect(prk_directionCheckBox, &QCheckBox::stateChanged, this, &TeleopPanel::OnDirectionCheckboxChanged);

    QVBoxLayout* commonLayout = new QVBoxLayout(commonWidget);
    commonLayout->setAlignment(Qt::AlignTop);
    commonLayout->setSpacing(5);

    // Add button of Turn Light control
    AddButtonAndLabelGroup(commonLayout, "TurnLight Request / 打灯请求", {
        {"Off / 取消", [this]() { PubTurnLightReq(1); }},
        {"Left / 左转", [this]() { PubTurnLightReq(2); }},
        {"Right / 右转", [this]() { PubTurnLightReq(3); }},
        {"Hazard / 双闪", [this]() { PubTurnLightReq(4); }}
    });

    // Add button of EPB control
    AddButtonAndLabelGroup(commonLayout, "EPB Request / EPB请求", {
        {"NoRequest / 无请求", [this]() { PubEpbReq(0); }},
        {"Release / 释放", [this]() { PubEpbReq(1); }},
        {"Hold / 拉起", [this]() { PubEpbReq(2); }}
    });

    // Add button of AutoHold control
    AddButtonAndLabelGroup(commonLayout, "AutoHold Request / AutoHold请求", {
        {"NoRequest / 无请求", [this]() { PubAutoHoldReq(0); }},
        {"Active / 激活", [this]() { PubAutoHoldReq(1); }},
        {"Release / 释放", [this]() { PubAutoHoldReq(2); }}
    });

    // Add label of imu and uss warning
    AddAlarmLabelAndTextEdit(commonLayout, "IMU Alarm / IMU提示", imuAlarmTextEdit_);
    connect(this, &TeleopPanel::updateIMUAlarmMessage, this, [this](const QString& message) {
        onAlarmMessageUpdated(message, imuAlarmTextEdit_);
    });

    AddAlarmLabelAndTextEdit(commonLayout, "USS Alarm / USS提示", ussAlarmTextEdit_);
    connect(this, &TeleopPanel::updateUSSAlarmMessage, this, [this](const QString& message) {
        onAlarmMessageUpdated(message, ussAlarmTextEdit_);
    });

    QGroupBox* txt_groupBox = new QGroupBox("Text Prompts of Drv and Prk / 行泊文言提示验证", commonLayout->parentWidget());
    QVBoxLayout* txt_groupBoxLayout = new QVBoxLayout(txt_groupBox);

    // Add label of driving and parking text
    AddTextInputLayout(txt_groupBoxLayout, {
        std::make_tuple("drv_disp_info/行车文言提示", &driving_disp_info_req_, SLOT(UpdatePubDrivingDispInfoVar())),
        std::make_tuple("drv_fail_rsn/行车故障原因", &driving_failure_reason_req_, SLOT(UpdatePubDrivingFailureReasonVar())),
        std::make_tuple("drv_psv_rsn/行车被抑制原因", &driving_passive_reason_req_, SLOT(UpdatePubDrivingPassiveReasonVar())),   
    });

    AddTextInputLayout(txt_groupBoxLayout, {
        std::make_tuple("prk_disp_info/泊车文言提示", &parking_disp_info_req_, SLOT(UpdatePubParkingDispInfoVar())),
        std::make_tuple("prk_fail_rsn/泊车故障原因", &parking_failure_reason_req_, SLOT(UpdatePubParkingFailureReasonVar())),
        std::make_tuple("prk_psv_rsn/泊车被抑制原因", &parking_passive_reason_req_, SLOT(UpdatePubParkingPassiveReasonVar())),
    });

    AddTextInputLayout(txt_groupBoxLayout, {
        std::make_tuple("drv_abt_rsn/行车退出原因", &driving_abort_reason_req_, SLOT(UpdatePubDrivingAbortReasonVar())),
        std::make_tuple("prk_abt_rsn/泊车退出原因", &parking_abort_reason_req_, SLOT(UpdatePubParkingAbortReasonVar())),
        std::make_tuple("prk_sus_rsn/泊车暂停原因", &parking_suspend_reason_req_, SLOT(UpdatePubParkingSuspendReasonVar())),
    });

    commonLayout->addWidget(txt_groupBox);

    drivingWidget->setLayout(drivingLayout);
    parkingWidget->setLayout(parkingLayout);
    commonWidget->setLayout(commonLayout);

    drivingWidget->setMaximumWidth(1000);
    parkingWidget->setMaximumWidth(1000);
    commonWidget->setMaximumWidth(1000);

    tabWidget->addTab(drivingWidget, "Driving / 行车");
    tabWidget->addTab(parkingWidget, "Parking / 泊车");
    tabWidget->addTab(commonWidget, "Common / 通用");

    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->addWidget(tabWidget);
    setLayout(mainLayout);

    // 设置全局样式表
    this->setStyleSheet(
        "QLabel, QComboBox, QLineEdit, QPushButton, QTextEdit, QGroupBox, QWidget {"
        "  font-size: 10pt;"  // 设置字体大小为10pt
        "  font-family: Arial;"  // 设置字体类型为Arial
        "}"
        "QPushButton {"
        "  padding: 5px 10px;"  // 设置按钮内边距
        "  border: 1px solid #ccc;"  // 设置按钮边框大小和颜色
        "  border-radius: 4px;"  // 设置按钮边框圆角
        "  background-color: #f0f0f0;"  // 设置按钮背景颜色
        "}"
        "QPushButton:hover {"
        "  background-color: #e0e0e0;"  // 设置按钮悬停时的背景颜色
        "}"
        "QTextEdit {"
        "  border: 1px solid #ccc;"  // 设置文本编辑框边框大小和颜色
        "  border-radius: 4px;"  // 设置文本编辑框边框圆角
        "  background-color: white;"  // 设置文本编辑框背景颜色
        "  padding: 5px;"  // 设置文本编辑框内边距
        "}"
        "QComboBox {"
        "  width: 300px;"
        "  height: 20px;"
        "}"
    );

    connect(tabWidget, &QTabWidget::currentChanged, this, &TeleopPanel::OnTabChanged); 
}

void TeleopPanel::OnTestModeChanged(int testModeIndex) {
    int currentTabIndex = tabWidget->currentIndex();
    if (currentTabIndex == 0) { // Driving tab
        std::vector<QSlider*> drvSliders = {drv_toqslider_, drv_brkslider_, drv_wheelangslider_};
        OnTestModeChangedHelper(testModeIndex, drv_groupBox, drvSliders);
    } else if (currentTabIndex == 1) { // Parking tab
        std::vector<QSlider*> prkSliders = {toqslider_, acclslider_, wheelangslider_};
        OnTestModeChangedHelper(testModeIndex, prk_groupBox, prkSliders);
    }
}

void TeleopPanel::OnTestModeChangedHelper(int testModeIndex, QGroupBox* groupBox, std::vector<QSlider*> sliders) {
    if (testModeIndex == 1) { // Test Case Mode
        groupBox->show();
        for (auto slider : sliders) {
            slider->setEnabled(false);
        }
    } else { // Normal Mode
        groupBox->hide();
        for (auto slider : sliders) {
            slider->setEnabled(true);
        }
    }
}

void TeleopPanel::OnBrakeSlopeRequestCaseChanged(int index) {
    if (index >= 0 && index < brakeSlopeRequestConfigs_.size()) {
        const BrakeSlopeRequestConfig& config = brakeSlopeRequestConfigs_[index];
        brk_max_var_ = config.brk_max_var;
        brk_min_var_ = config.brk_min_var;
        brk_slope_var_ = config.brk_slope_var;

        std::cout << "Updated Brake Slope Request Configuration: "
                  << "brk_max_var: " << brk_max_var_ << ", "
                  << "brk_min_var: " << brk_min_var_ << ", "
                  << "brk_slope_var: " << brk_slope_var_ << std::endl;
    }
}

void TeleopPanel::OnBrakeStepRequestCaseChanged(int index) {
    if (index >= 0 && index < brakeStepRequestConfigs_.size()) {
        const BrakeStepRequestConfig& config = brakeStepRequestConfigs_[index];
        brk_step_var_ = config.brk_step_var;

        std::cout << "Updated Brake Step Request Configuration: "
                  << "brk_step_var: " << brk_step_var_ << std::endl;
    }
}

void TeleopPanel::OnAcclDcclCaseChanged(int index) {
    if (index >= 0 && index < acclDcclConfigs_.size()) {
        const AcclDcclConfig& config = acclDcclConfigs_[index];
        accl_coeffs1_var_ = config.coeffs1_var;
        accl_coeffs2_var_ = config.coeffs2_var;
        accl_coeffs3_var_ = config.coeffs3_var;

        std::cout << "Updated Accl_Dccl Configuration: "
                  << "coeffs1_var: " << accl_coeffs1_var_ << ", "
                  << "coeffs2_var: " << accl_coeffs2_var_ << ", "
                  << "coeffs3_var: " << accl_coeffs3_var_ << std::endl;
    }
}

void TeleopPanel::OnSteerSlopeCaseChanged(int index) {
    if (index >= 0 && index < steerSlopeConfigs_.size()) {
        const SteerSlopeConfig& config = steerSlopeConfigs_[index];
        steer_slope_var_ = config.slope_var;
        steer_flat_time_var_ = config.flat_time_var;
        steer_back_max_var_ = config.max_var;
        filter_alpha_var_ = config.lowpass_filter_alpha_var;

        std::cout << "Updated Slope_Steer Configuration: "
                  << "slope_var: " << steer_slope_var_ << ", "
                  << "flat_time_var: " << steer_flat_time_var_ << ", "
                  << "max_var: " << steer_back_max_var_ << ", "
                  << "lowpass_filter_alpha_var: " << filter_alpha_var_ << std::endl;
    }
}

void TeleopPanel::OnSteerSinCaseChanged(int index) {
    if (index >= 0 && index < steerSinConfigs_.size()) {
        const SteerSinConfig& config = steerSinConfigs_[index];
        steer_sin_hz_var_ = config.hz_var;
        steer_sin_max_var_ = config.max_var;
        steer_sin_band_var_ = config.band_var;

        std::cout << "Updated Sin_Steer Configuration: "
                  << "hz_var: " << steer_sin_hz_var_ << ", "
                  << "max_var: " << steer_sin_max_var_ << ", "
                  << "band_var: " << steer_sin_band_var_ << std::endl;
    }
}

// 从指定的JSON文件路径加载控制指令数据
void TeleopPanel::LoadControlCommands(const QString& jsonFilePath) {
    QFile file(jsonFilePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Failed to open JSON file:" << jsonFilePath;
        return;
    }

    QByteArray jsonData = file.readAll();
    file.close();

    QJsonDocument doc = QJsonDocument::fromJson(jsonData);
    if (doc.isNull()) {
        qWarning() << "Failed to parse JSON data";
        return;
    }

    if (!doc.isArray()) {
        qWarning() << "JSON data is not an array";
        return;
    }

    commandQueue_ = doc.array();
    commandIndex_ = 0;
    // qDebug() << "Loaded command queue:" << commandQueue_;  
}

// 加载并执行指令队列中的下一个控制指令
void TeleopPanel::LoadNextControlCommand() {
    QMutexLocker locker(&q_mutex_);
    if (commandIndex_ >= commandQueue_.size()) {
        commandTimer_->stop();
        std::cout << "All control commands have been loaded." << std::endl;
        return;
    }

    QJsonObject command = commandQueue_[commandIndex_].toObject();
    ApplyControlCommand(command);
    commandIndex_++;
    // qDebug() << "Loaded command index:" << commandIndex_;
}

// 根据传入的控制指令更新相关的控制参数
void TeleopPanel::ApplyControlCommand(const QJsonObject& command) {
    if (command.contains("/alg/pnc/control_out/long_signal/is_gear_shift_req_enabled")) {
        is_gear_shift_req_enabled_ = command["/alg/pnc/control_out/long_signal/is_gear_shift_req_enabled"].toString().toInt();
    }
    if (command.contains("/alg/pnc/control_out/long_signal/target_gear_req_enum")) {
        target_gear_req_val_ = command["/alg/pnc/control_out/long_signal/target_gear_req_enum"].toString().toInt();
    }
    if (command.contains("/alg/pnc/control_out/long_signal/brk_mode_req_enum")) {
        brk_mode_req_val_ = command["/alg/pnc/control_out/long_signal/brk_mode_req_enum"].toString().toInt();
    }
    if (command.contains("/alg/pnc/control_out/long_signal/brk_req_val_m_s2")) {
        brk_req_val_ = command["/alg/pnc/control_out/long_signal/brk_req_val_m_s2"].toString().toDouble();
    }
    if (command.contains("/alg/pnc/control_out/long_signal/dccl_mode_req_enum")) {
        dccl_mode_req_val_ = command["/alg/pnc/control_out/long_signal/dccl_mode_req_enum"].toString().toInt();
    }
    if (command.contains("/alg/pnc/control_out/long_signal/dccl_req_val_m_s2")) {
        accl_req_val_ = command["/alg/pnc/control_out/long_signal/dccl_req_val_m_s2"].toString().toDouble();
        acclslider_->setValue(static_cast<float>(accl_req_val_ * 100));
    }
    if (command.contains("/alg/pnc/control_out/long_signal/is_drive_off_req")) {
        drive_off_req_val_ = command["/alg/pnc/control_out/long_signal/is_drive_off_req"].toString().toInt();
    }
    if (command.contains("/alg/pnc/control_out/long_signal/is_stst_req")) {
        stst_req_val_ = command["/alg/pnc/control_out/long_signal/is_stst_req"].toString().toInt();
    }
    if (command.contains("/alg/pnc/control_out/long_signal/is_vcu_toq_req_enabled")) {
        is_vcu_toq_req_enabled_ = command["/alg/pnc/control_out/long_signal/is_vcu_toq_req_enabled"].toString().toInt();
    }
    if (command.contains("/alg/pnc/control_out/long_signal/vcu_act_toq_req_nm")) {
        toq_req_val_ = command["/alg/pnc/control_out/long_signal/vcu_act_toq_req_nm"].toString().toDouble();
        toqslider_->setValue(static_cast<float>(toq_req_val_ * 100));
    }
    if (command.contains("/alg/pnc/control_out/lat_signal/tgt_pnn_ang_req_deg")) {
        wheelang_req_val_ = command["/alg/pnc/control_out/lat_signal/tgt_pnn_ang_req_deg"].toString().toDouble();
        wheelangslider_->setValue(static_cast<float>(wheelang_req_val_ * 100));
    }
}

void TeleopPanel::OnLoadParkOutDataButtonClicked() {
    if (!prk_handshakeSuccess_) {
        std::cout << "Handshake not successful. Button click ignored." << std::endl;
        return; 
    }

    button_flag_ = !button_flag_;

    std::string package_path = ament_index_cpp::get_package_prefix("alg_pnc_nviz_panel");
    std::string configFile;
    if (vehicle_model_ == 0 || vehicle_model_ == 4) {
        configFile = package_path + "/config/a19_apa_park_out.json";
    } else if (vehicle_model_ == 1 || vehicle_model_ == 2 || vehicle_model_ == 3) {
        configFile = package_path + "/config/t22_apa_park_out.json";
    } else {
        return;
    }
    std::cout << "config_path: " << configFile << std::endl;

    QString qConfigFile = QString::fromStdString(configFile);
    LoadControlCommands(qConfigFile);

    commandTimer_ = new QTimer(this);
    connect(commandTimer_, &QTimer::timeout, this, &TeleopPanel::LoadNextControlCommand);
    commandTimer_->start(10);
}

void TeleopPanel::OnBrakeSlopeButtonClicked() {
    if (lgthandshakereq_ != 4) {
        std::cout << "Handshake not successful. Button click ignored." << std::endl;
        return; 
    }
    brk_slope_button_flag_ = !brk_slope_button_flag_;
}

void TeleopPanel::OnBrakeStepButtonClicked() {
    if (lgthandshakereq_ != 4) {
        std::cout << "Handshake not successful. Button click ignored." << std::endl;
        return; 
    }
    brk_step_button_flag_ = !brk_step_button_flag_;
}


void TeleopPanel::OnSteerButtonClicked() {
    if (lathandshakereq_ != 2) {
        std::cout << "Handshake not successful. Button click ignored." << std::endl;
        return; 
    }
    steer_button_flag_ = !steer_button_flag_;
}

void TeleopPanel::OnAcclDcclButtonClicked() {
    if (lgthandshakereq_ != 1) {
        std::cout << "Handshake not successful. Button click ignored." << std::endl;
        return; 
    }
    accl_dccl_button_flag_ = !accl_dccl_button_flag_;
}

void TeleopPanel::OnSlopeSteerButtonClicked() {
    if (lathandshakereq_ != 1) {
        std::cout << "Handshake not successful. Button click ignored." << std::endl;
        return; 
    }
    slope_button_flag_ = !slope_button_flag_;
}

void TeleopPanel::OnSinSteerButtonClicked() {
    if (lathandshakereq_ != 1) {
        std::cout << "Handshake not successful. Button click ignored." << std::endl;
        return; 
    }
    sin_button_flag_ = !sin_button_flag_;
}

void TeleopPanel::ResetControlReq() {
    toqslider_->setValue(0);
    toq_req_val_ = 0.0;
    drv_toqslider_->setValue(0);

    acclslider_->setValue(0); 
    accl_req_val_ = 0.0;

    drv_brkslider_->setValue(0);
    brk_req_val_ = 0.0; 
    
    wheelangslider_->setValue(0); 
    wheelang_req_val_ = 0.0; 
    drv_wheelangslider_->setValue(0); 

    target_gear_req_val_ = 0;
    drive_off_req_val_ = 0;
    stst_req_val_ = 0;
    dccl_mode_req_val_ = 0;
    brk_mode_req_val_ = 0;
    is_gear_shift_req_enabled_ = 0;
    is_vcu_toq_req_enabled_ = 0;
}

void TeleopPanel::ResetButtonReq() {
    if (brk_slope_button_flag_) {
        brk_slope_start_time_ = 0.0;
        brk_slope_button_flag_ = false;
    }
    if (brk_step_button_flag_) {
        brk_step_start_time_ = 0.0;
        brk_step_button_flag_ = false;
    }
    if (steer_button_flag_) {
        steer_start_time_ = 0.0;
        steer_button_flag_ = false;
        wheelang_req_val_prev_ = 0.0;
    }
    if (accl_dccl_button_flag_) {
        accl_dccl_start_time_ = 0.0;
        accl_dccl_button_flag_ = false;
    }
    if (slope_button_flag_) {
        slope_start_time_ = 0.0;
        slope_button_flag_ = false;
    }
    if (sin_button_flag_) {
        sin_start_time_ = 0.0;
        sin_button_flag_ = false;
    }
}

void TeleopPanel::PubHandShake(int latHandShakeReq, int lgtHandShakeReq, const std::string& message) {
    lathandshakereq_ = latHandShakeReq;
    lgthandshakereq_ = lgtHandShakeReq;
    std::cout << " LgtHandShake: " << lgthandshakereq_ << " , LatHandShake: " << lathandshakereq_ << std::endl;

    if (latHandShakeReq == 0 && lgtHandShakeReq == 0) {
        prk_handshakeSuccess_ = false;
        drv_handshakeSuccess_ = false;

        button_flag_ = false;

        // Stop the command loading timer if it is running
        if (commandTimer_) {
            commandTimer_->stop();
            std::cout << "Command loading timer stopped." << std::endl;
        }

        ResetControlReq();
        ResetButtonReq();
    } else if (latHandShakeReq == 2 && lgtHandShakeReq == 4) {
        drv_handshakeSuccess_ = true;
    } else if (latHandShakeReq == 1 && lgtHandShakeReq == 1) {
        prk_handshakeSuccess_ = true;
    }
}

// handshake request
void TeleopPanel::PubAllCancelHandShake() {
    PubHandShake(0, 0, "All_CancelHandShake:");
}

void TeleopPanel::PubAllParkingHandShake() {
    PubHandShake(1, 1, "All_Prk_HandShake:");
}

void TeleopPanel::PubAllDrivingHandShake() {
    PubHandShake(2, 4, "All_Drv_HandShake:");
}

void TeleopPanel::PubLatParkingHandShake() {
    PubHandShake(1, 0, "Lat_Prk_HandShake:");
}

void TeleopPanel::PubLatDrivingHandShake() {
    PubHandShake(2, 0, "Lat_Drv_HandShake:");
}

void TeleopPanel::PubLgtParkingHandShake() {
    PubHandShake(0, 1, "Lgt_Prk_HandShake:");
}

void TeleopPanel::PubLgtDrivingHandShake() {
    PubHandShake(0, 4, "Lgt_Drv_HandShake:");
}

template <typename T>
void TeleopPanel::SetRequestValue(T& valueRef, int value, const char* requestType) {
    valueRef = value;
    std::cout << "current " << requestType << " request is: " << valueRef << std::endl; 
}

void TeleopPanel::PubTurnLightReq(int val) {
    SetRequestValue(turn_light_req_val_, val, "Turn Light");
}

void TeleopPanel::PubGearReq(int val) {
    SetRequestValue(target_gear_req_val_, val, "Gear");
}

void TeleopPanel::PubEpbReq(int val) {
    SetRequestValue(epb_req_val_, val, "EPB");
}

void TeleopPanel::PubAutoHoldReq(int val) {
    SetRequestValue(auto_hold_req_val_, val, "AutoHold");
}

void TeleopPanel::PubDriveoffReq(int val) {
    SetRequestValue(drive_off_req_val_, val, "Driveoff");
}

void TeleopPanel::PubStandstillReq(int val) {
    SetRequestValue(stst_req_val_, val, "Standstill");
}

template <typename T>
void TeleopPanel::UpdatePubVar(QLineEdit* request_var, T* target_var) {
    QString temp_string = request_var->text(); 
    bool ok;

    T var;
    if constexpr (std::is_integral<T>::value) {
        var = static_cast<T>(temp_string.toInt(&ok)); 
    } else if constexpr (std::is_floating_point<T>::value) {
        var = static_cast<T>(temp_string.toFloat(&ok));
    } else {
        return;
    }

    if (ok) { 
        *target_var = var; 
        std::cout << "Updated " << typeid(*target_var).name() << " with value: " << var << std::endl;
    } else { 
        qWarning() << "Invalid input:" << temp_string; 
    }
}

// driving_disp_info and parking_disp_info
void TeleopPanel::UpdatePubDrivingDispInfoVar() {
    UpdatePubVar(driving_disp_info_req_, &driving_disp_info_);
}

void TeleopPanel::UpdatePubDrivingFailureReasonVar() {
    UpdatePubVar(driving_failure_reason_req_, &driving_failure_reason_);
}

void TeleopPanel::UpdatePubDrivingPassiveReasonVar() {
    UpdatePubVar(driving_passive_reason_req_, &driving_passive_reason_);
}

void TeleopPanel::UpdatePubDrivingAbortReasonVar() {
    UpdatePubVar(driving_abort_reason_req_, &driving_abort_reason_);
}

void TeleopPanel::UpdatePubParkingDispInfoVar() {
    UpdatePubVar(parking_disp_info_req_, &parking_disp_info_);
}

void TeleopPanel::UpdatePubParkingFailureReasonVar() {
    UpdatePubVar(parking_failure_reason_req_, &parking_failure_reason_);
}

void TeleopPanel::UpdatePubParkingPassiveReasonVar() {
    UpdatePubVar(parking_passive_reason_req_, &parking_passive_reason_);
}

void TeleopPanel::UpdatePubParkingAbortReasonVar() {
    UpdatePubVar(parking_abort_reason_req_, &parking_abort_reason_);
}

void TeleopPanel::UpdatePubParkingSuspendReasonVar() {
    UpdatePubVar(parking_suspend_reason_req_, &parking_suspend_reason_);
}

// add accl and wheelang for keyboard test
void TeleopPanel::UpdatePubConstAcclVar() {
    UpdatePubVar(const_accl_var_req_, &const_accl_var_);
}

void TeleopPanel::UpdatePubAcclVar() {
    UpdatePubVar(accl_var_req_, &accl_var_);
}

void TeleopPanel::UpdatePubWheelangVar() {
    UpdatePubVar(wheelang_var_req_, &wheelang_var_);
}

void TeleopPanel::UpdatePubBrkSlopeVar() {
    UpdatePubVar(brk_slope_var_req_, &brk_slope_var_);
}

void TeleopPanel::UpdatePubBrkMaxVar() {
    UpdatePubVar(brk_max_var_req_, &brk_max_var_);
}

void TeleopPanel::UpdatePubBrkMinVar() {
    UpdatePubVar(brk_min_var_req_, &brk_min_var_);
}

void TeleopPanel::UpdatePubBrkStepVar() {
    UpdatePubVar(brk_step_var_req_, &brk_step_var_);
}

void TeleopPanel::UpdatePubSteerRampVar() {
    UpdatePubVar(steer_ramp_var_req_, &steer_ramp_var_);
}

void TeleopPanel::UpdatePubSteerMaxVar() {
    UpdatePubVar(steer_max_var_req_, &steer_max_var_);
}

void TeleopPanel::UpdatePubAcclCoeffs1Var() {
    UpdatePubVar(accl_coeffs1_var_req_, &accl_coeffs1_var_);
}

void TeleopPanel::UpdatePubAcclCoeffs2Var() {
    UpdatePubVar(accl_coeffs2_var_req_, &accl_coeffs2_var_);
}

void TeleopPanel::UpdatePubAcclCoeffs3Var() {
    UpdatePubVar(accl_coeffs3_var_req_, &accl_coeffs3_var_);
}

void TeleopPanel::UpdatePubSteerSlopeVar() {
    UpdatePubVar(steer_slope_var_req_, &steer_slope_var_);
}

void TeleopPanel::UpdatePubSteerFlatTimeVar() {
    UpdatePubVar(steer_flat_time_var_req_, &steer_flat_time_var_);
}

void TeleopPanel::UpdatePubSteerBackMaxVar() {
    UpdatePubVar(steer_back_max_var_req_, &steer_back_max_var_);
}

void TeleopPanel::UpdatePubSteerAlphaVar() {
    UpdatePubVar(filter_alpha_var_req_, &filter_alpha_var_);
}

void TeleopPanel::UpdatePubSteerSinHzVar() {
    UpdatePubVar(steer_sin_hz_var_req_, &steer_sin_hz_var_);
}

void TeleopPanel::UpdatePubSteerSinMaxVar() {
    UpdatePubVar(steer_sin_max_var_req_, &steer_sin_max_var_);
}

void TeleopPanel::UpdatePubSteerSinBandVar() {
    UpdatePubVar(steer_sin_band_var_req_, &steer_sin_band_var_);
}

// ---------------------------------------------
// 重载父类的功能
void TeleopPanel::save( nviz_common::Config config ) const 
{
    nviz_common::Panel::save(config);
}

// 重载父类的功能，加载配置数据
void TeleopPanel::load( const nviz_common::Config& config ) 
{
    nviz_common::Panel::load(config);
}
} // namespace PncPanel
} // namespace Nviz

// 声明此类是一个nviz的插件
#include <pluginlib/class_list_macros.hpp>
PLUGINLIB_EXPORT_CLASS(Nviz::PncPanel::TeleopPanel, nviz_common::Panel)
// END_TUTORIAL
