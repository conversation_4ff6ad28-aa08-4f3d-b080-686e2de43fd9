#ifndef Nviz_PncPanel_TeleopPanel_H_
#define Nviz_PncPanel_TeleopPanel_H_

#include <QLineEdit>
#include <QWidget>
#include <QSlider>
#include <QLabel>
#include <QDebug>
#include <QKeyEvent>
#include <QApplication>
#include <QCheckBox>
#include <QComboBox>
#include <QFile>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QString>
#include <QMutex>
#include <QPainter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTimer>
#include <QPushButton>
#include <QCoreApplication>
#include <QFileInfo>
#include <QDir>
#include <QMessageBox> 
#include <QTextEdit>
#include <QStringList>
#include <QSignalMapper>
#include <QGroupBox>
#include <QGridLayout>
#include <QTabWidget>
#include <QStandardPaths>
#include <QJsonValue>
#include <QMutexLocker>
#include <QPair>

#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <iostream>
#include <utility>
#include <initializer_list>
#include <vector>
#include <algorithm>
#include <cmath>
#include <filesystem>

#include <ament_index_cpp/get_package_prefix.hpp>
#include <std_msgs/msg/int16.hpp>
#include <std_msgs/msg/float32_multi_array.hpp>
#include <std_msgs/msg/float32.hpp>
#include <std_msgs/msg/string.hpp>
#include <nviz_common/panel.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <nlibcpp/nlibcpp.hpp>
#include <fsm_msgs/msg/fsm_out.hpp>
#include <control_msgs/msg/control_out.hpp>
#include <vehicle_msgs/msg/vehicle_chassis.hpp>
#include <p_msgs/msg/imu.hpp>
#include <uss_msgs/msg/uss_info.hpp>

using namespace std::chrono_literals;

namespace Nviz {
namespace PncPanel {
class TeleopPanel: public nviz_common::Panel {
//class TeleopPanel: public nlibcpp::Node {
Q_OBJECT

protected:
    void keyPressEvent(QKeyEvent* event) override;

public:
    TeleopPanel(QWidget* parent = nullptr);
    // virtual ~TeleopPanel() {}
    virtual ~TeleopPanel();

    virtual void load(const nviz_common::Config& config);
    virtual void save(nviz_common::Config config) const;

    void Start();
    void Stop();
    void Run();

    template <typename T>
    void SetRequestValue(T& valueRef, int value, const char* requestType);

    template <typename T>
    void UpdatePubVar(QLineEdit* request_var, T* target_var);

    static const std::unordered_map<int, std::string> vehicleModelToFsmTopic;
    static const std::unordered_map<int, std::string> vehicleModelToControlTopic;
    static const std::unordered_map<int, std::string> vehicleModelToVehicleChassisTopic;
    static const std::unordered_map<int, std::string> vehicleModelToImuTopic;
    static const std::unordered_map<int, std::string> vehicleModelToUssTopic;

private Q_SLOTS:
    void OnTestModeChanged(int testModeIndex);
    void OnTestModeChangedHelper(int testModeIndex, QGroupBox* groupBox, std::vector<QSlider*> sliders);
    void OnBrakeSlopeRequestCaseChanged(int index);
    void OnBrakeStepRequestCaseChanged(int index);
    void OnAcclDcclCaseChanged(int index);
    void OnSteerSlopeCaseChanged(int index);
    void OnSteerSinCaseChanged(int index);

    void OnTabChanged(int index);

    void OnVehicleModelChanged(int index);
    void AddComboBoxLayout(QVBoxLayout* vLayout, const QString& label, QComboBox*& comboBox, const QStringList& items, const char* slot);
    
    void UpdateSliderValue(QSlider* slider, float value, const char* valueName);
    void AddSliderLayout(QVBoxLayout* vLayout, const QString& groupBoxTitle, const QString& labelName, QSlider*& slider, QLabel*& valueLabel, int min, int max, int singleStep, int pageStep, int defaultValue, const char* valueName);

    void onAlarmMessageUpdated(const QString& message, QTextEdit* alarmTextEdit);
    void AddAlarmLabelAndTextEdit(QVBoxLayout* vLayout, const QString& label, QTextEdit*& textEdit);

    void AddTextInputLayout(QVBoxLayout* vLayout, const std::initializer_list<std::tuple<QString, QLineEdit**, const char*>>& inputs);
    
    void OnDirectionCheckboxChanged(int state); 

    void UpdatePubDrivingDispInfoVar();
    void UpdatePubDrivingFailureReasonVar();
    void UpdatePubDrivingPassiveReasonVar();
    void UpdatePubDrivingAbortReasonVar();
    void UpdatePubParkingDispInfoVar();
    void UpdatePubParkingFailureReasonVar();
    void UpdatePubParkingPassiveReasonVar();
    void UpdatePubParkingAbortReasonVar();
    void UpdatePubParkingSuspendReasonVar();
    void UpdatePubAcclVar();
    void UpdatePubWheelangVar();
    void UpdatePubConstAcclVar();

    void UpdatePubBrkSlopeVar();
    void UpdatePubBrkMaxVar();
    void UpdatePubBrkMinVar();
    void UpdatePubBrkStepVar();
    void UpdatePubSteerRampVar();
    void UpdatePubSteerMaxVar();
    void UpdatePubAcclCoeffs1Var();
    void UpdatePubAcclCoeffs2Var();
    void UpdatePubAcclCoeffs3Var();
    void UpdatePubSteerSlopeVar();
    void UpdatePubSteerFlatTimeVar();
    void UpdatePubSteerBackMaxVar();
    void UpdatePubSteerAlphaVar();
    void UpdatePubSteerSinHzVar();
    void UpdatePubSteerSinMaxVar();  
    void UpdatePubSteerSinBandVar();  

    void OnBrakeSlopeButtonClicked();
    void OnBrakeStepButtonClicked();
    void OnSteerButtonClicked();
    void OnAcclDcclButtonClicked();
    void OnSlopeSteerButtonClicked();
    void OnSinSteerButtonClicked();

    void ResetButtonReq();
    void ResetControlReq();
    void PubHandShake(int latHandShakeReq, int lgtHandShakeReq, const std::string& message);
    void PubAllCancelHandShake();
    void PubLatDrivingHandShake();
    void PubLgtDrivingHandShake();
    void PubAllDrivingHandShake();
    void PubLatParkingHandShake();
    void PubLgtParkingHandShake();
    void PubAllParkingHandShake();
    
    void PubTurnLightReq(int val);
    void PubGearReq(int val);
    void PubDriveoffReq(int val);
    void PubStandstillReq(int val);
    void PubEpbReq(int val);
    void PubAutoHoldReq(int val);
    void AddButtonGroup(QVBoxLayout* vLayout, const std::initializer_list<std::pair<QString, std::function<void()>>>& buttons);
    void AddButtonAndLabelGroup(QVBoxLayout* vLayout, const QString& groupBoxTitle, const std::initializer_list<std::pair<QString, std::function<void()>>>& buttons);

signals:
    void updateIMUAlarmMessage(const QString& message);
    void updateUSSAlarmMessage(const QString& message);

private:
    struct BrakeSlopeRequestConfig {
        float brk_max_var;
        float brk_min_var;
        float brk_slope_var;
    };

    std::vector<BrakeSlopeRequestConfig> brakeSlopeRequestConfigs_ = {
        {0.0, -0.6, 0.12}, 
        {0.0, -1.0, 0.2}, 
        {0.0, -2.0, 0.4}, 
        {0.0, -3.0, 0.6}, 
        {0.0, -4.0, 0.8},
        {0.6, -0.6, 0.12}, 
        {1.0, -1.0, 0.2}, 
        {1.0, -2.0, 0.4}, 
        {1.0, -3.0, 0.6}, 
        {1.0, -4.0, 0.8}
    };

    struct BrakeStepRequestConfig {
        float brk_step_var;
    };

    std::vector<BrakeStepRequestConfig> brakeStepRequestConfigs_ = {
        {-1.0}, 
        {-2.0}, 
        {-3.0}, 
        {-4.0}, 
        {-5.0}, 
        {-6.0}
    };

    struct AcclDcclConfig {
        float coeffs1_var;
        float coeffs2_var;
        float coeffs3_var;
    };

    std::vector<AcclDcclConfig> acclDcclConfigs_ = {
        {-0.15, 0.25, 0.1},
        {-0.2, 0.3, 0.2},
        {-0.25, 0.35, 0.3},
        {-0.3, 0.4, 0.4},
        {-0.35, 0.45, 0.5}
    };

    struct SteerSlopeConfig {
        int16_t slope_var;
        float flat_time_var;
        float max_var;
        float lowpass_filter_alpha_var;
    };

    std::vector<SteerSlopeConfig> steerSlopeConfigs_ = {
        {50, 3.0, 360.0, 0.1}, 
        {100, 3.0, 360.0, 0.1}, 
        {200, 3.0, 360.0, 0.1},
        {300, 3.0, 360.0, 0.1}, 
        {400, 3.0, 360.0, 0.1}, 
        {500, 3.0, 360.0, 0.1},  
        {600, 3.0, 360.0, 0.1}, 
        {360, 3.0, 200.0, 0.1}
    };

    struct SteerSinConfig {
        float hz_var;
        int16_t max_var;
        int16_t band_var;
    };

    std::vector<SteerSinConfig> steerSinConfigs_ = {
        {0.1, 300, 4},
        {0.2, 200, 4},
        {0.5, 100, 4},
        {1.0, 50, 4},
        {2.0, 25, 4}
    };

    void SaveVehicleModelSelection(int drvVehicleModel, int prkVehicleModel);
    QPair<int, int> LoadVehicleModelSelection();

    void SetPanelLayout();

    float clamp(float value, float min_value, float max_value);

    void PublishFsmReq();
    void PublishControlReq();
    void SetControlMessage(control_msgs::msg::ControlOut& control_msg);
    void SetDrvAccelerationInterfaceControlMessage(control_msgs::msg::ControlOut& control_msg);
    void SetDrvTorqueInterfaceControlMessage(control_msgs::msg::ControlOut& control_msg);
    void SetPrkAccelerationInterfaceControlMessage(control_msgs::msg::ControlOut& control_msg);
    void SetPrkTorqueInterfaceControlMessage(control_msgs::msg::ControlOut& control_msg);

    void VehicleChassisCallback(const vehicle_msgs::msg::VehicleChassis::SharedPtr msg);
    void IMUCallback(const p_msgs::msg::IMU::SharedPtr msg);
    void USSCallback(const uss_msgs::msg::USSInfo::SharedPtr msg);

    void CheckUSSStatus(int uss_i_, int16_t is_uss_echo_available_, int64_t first_echo_timestamp_, float first_echo_distance_);
    void CheckIMUStatus();
    QString USSPosition(int uss_i);

    void LoadControlCommands(const QString& jsonFilePath);
    void LoadNextControlCommand();
    void ApplyControlCommand(const QJsonObject& command);
    void OnLoadParkOutDataButtonClicked();

    void UpdateBrakeSlopeRequest();
    void UpdateBrakeStepRequest();
    void UpdateSteerRequest();
    void UpdateAcclDcclRequest();
    void UpdateSlopeSteerRequest();
    void UpdateSinSteerRequest();

    void UpdateTopics();

    // varibles and QT widget of data-load buttons
    QTimer* commandTimer_;
    QJsonArray commandQueue_;
    int commandIndex_;
    QMutex q_mutex_;
    bool button_flag_ = false;
    bool prk_handshakeSuccess_ = false;
    bool drv_handshakeSuccess_ = false;

    float brk_slope_start_time_ = 0.0; 
    bool brk_slope_button_flag_ = false;
    float brk_step_start_time_ = 0.0; 
    bool brk_step_button_flag_ = false;
    float steer_start_time_ = 0.0;
    bool steer_button_flag_ = false;
    float wheelang_req_val_prev_ = 0.0; 
    float accl_dccl_start_time_ = 0.0;
    bool accl_dccl_button_flag_ = false;
    float slope_start_time_ = 0.0;
    bool slope_button_flag_ = false;
    float sin_start_time_ = 0.0;
    bool sin_button_flag_ = false;

    QLineEdit* brk_slope_var_req_;
    QLineEdit* brk_max_var_req_;
    QLineEdit* brk_min_var_req_;
    QLineEdit* brk_step_var_req_;
    QLineEdit* steer_ramp_var_req_;
    QLineEdit* steer_max_var_req_;
    QLineEdit* accl_coeffs1_var_req_;
    QLineEdit* accl_coeffs2_var_req_;
    QLineEdit* accl_coeffs3_var_req_;
    QLineEdit* steer_slope_var_req_;
    QLineEdit* steer_flat_time_var_req_;
    QLineEdit* steer_back_max_var_req_;
    QLineEdit* filter_alpha_var_req_;
    QLineEdit* steer_sin_hz_var_req_;
    QLineEdit* steer_sin_max_var_req_;
    QLineEdit* steer_sin_band_var_req_;

    float brk_slope_var_ = 0.0;
    float brk_max_var_ = 0.0;
    float brk_min_var_ = 0.0;
    float brk_step_var_ = 0.0;
    float steer_ramp_var_ = 0.0;
    float steer_max_var_ = 0.0;
    float accl_coeffs1_var_ = 0.0;
    float accl_coeffs2_var_ = 0.0;
    float accl_coeffs3_var_ = 0.0;
    int16_t steer_slope_var_ = 0;
    float steer_flat_time_var_ = 0.0;
    float steer_back_max_var_ = 0.0;
    float filter_alpha_var_ = 0.0;
    float steer_sin_hz_var_ = 0.0;
    int16_t steer_sin_max_var_ = 0;
    int16_t steer_sin_band_var_ = 0;

    QComboBox* brakeSlopeRequestComboBox_;
    QComboBox* brakeStepRequestComboBox_;
    QComboBox* acclDcclComboBox_;
    QComboBox* steerSlopeComboBox_;
    QComboBox* steerSinComboBox_;
    QComboBox* drv_testModeComboBox_;
    QGroupBox* drv_groupBox;
    QComboBox* prk_testModeComboBox_;
    QGroupBox* prk_groupBox;
    QTabWidget* tabWidget;

    QLineEdit* accl_var_req_;
    QLineEdit* wheelang_var_req_;
    QLineEdit* const_accl_var_req_;

    // variables of keyboard contorl
    bool directionKeysEnabled;
    float wheelang_var_ = 0.0;
    float accl_var_ = 0.0;
    float const_accl_var_ = 0.0;

    QLineEdit* driving_disp_info_req_;
    QLineEdit* parking_disp_info_req_;
    QLineEdit* driving_failure_reason_req_;
    QLineEdit* driving_passive_reason_req_;
    QLineEdit* driving_abort_reason_req_;
    QLineEdit* parking_failure_reason_req_;
    QLineEdit* parking_passive_reason_req_;
    QLineEdit* parking_abort_reason_req_;
    QLineEdit* parking_suspend_reason_req_;

    // variables of fsm function text
    int16_t driving_disp_info_ = 0;
    int16_t driving_failure_reason_ = 0;
    int16_t driving_passive_reason_ = 0;
    int16_t driving_abort_reason_ = 0;
    int16_t parking_disp_info_ = 0;
    int16_t parking_failure_reason_ = 0;
    int16_t parking_passive_reason_ = 0;
    int16_t parking_abort_reason_ = 0;
    int16_t parking_suspend_reason_ = 0;

    QCheckBox* directionCheckBox;
    QComboBox* prk_vehicleModelComboBox_;
    QComboBox* drv_vehicleModelComboBox_;
    int16_t vehicle_model_ = 0;
    QComboBox* drivingModeComboBox_;
    int16_t is_prk_mode_ = 0;

    QSlider* wheelangslider_; 
    QSlider* acclslider_; 
    QSlider* toqslider_;
    QSlider* drv_brkslider_;
    QSlider* drv_wheelangslider_;
    QSlider* drv_toqslider_;
    QLabel* brkValueLabel_;
    QLabel* toqValueLabel_;
    QLabel* acclValueLabel_;
    QLabel* wheelangValueLabel_;

    // variables of fsm handshake and control
    int16_t lathandshakereq_ = 0;
    int16_t lgthandshakereq_ = 0;
    int16_t drive_off_req_val_ = 0;
    int16_t stst_req_val_ = 0;
    int16_t turn_light_req_val_ = 0;
    int16_t epb_req_val_ = 0;
    int16_t auto_hold_req_val_ = 0;
    int16_t target_gear_req_val_ = 0;
    float wheelang_req_val_ = 0.0;
    float toq_req_val_ = 0.0;
    float accl_req_val_ = 0.0;
    int16_t dccl_mode_req_val_ = 0;
    int16_t is_gear_shift_req_enabled_ = 0;
    int16_t is_vcu_toq_req_enabled_ = 0;
    float brk_req_val_ = 0.0;
    int8_t brk_mode_req_val_ = 0;

    QTextEdit* ussAlarmTextEdit_;
    QTextEdit* imuAlarmTextEdit_;
    QString ussAlarmMessage_;
    QString imuAlarmMessage_; 

    // variables of uss and imu
    int64_t uss_time_;
    int64_t imu_time_;
    int64_t imu_synctime_;
    float accel_z_;

    // variables of vehicle chassis
    int64_t vehicle_chassis_time_;
    int16_t actv_gear_enum_;
    float pnnagsae_deg_;
    float vehspd_kph_;
    float long_accl_m_s2_;
    float lat_accl_m_s2_;
    float vehdyn_yawrate_deg_s_;
    int16_t epb_sts_;

    // variables of vehicle parameter
    float veh_wheel_base_m = 2.912;
    float veh_empty_mass_kg = 2127.0;
    float veh_full_mass_kg = 2402.0; 
    float steer_transmission_ratio = 14.7;  
    float rolling_resistance_coefficient = 0.015;
    float air_windward_area_m2 = 2.2;     
    float drag_coefficient = 0.3;      
    float tire_radius_list_m = 0.366;  
    float max_lat_acc = 3.5; 

    std::mutex controlMutex; 
    uint8_t fsm_counter_ = 0U; 
    uint8_t control_counter_ = 0U;  
    float total_time_seconds_ = 0.0;
    std::mutex fsm_counter_mutex_; 
    std::mutex control_counter_mutex_; 
    std::thread thread_;
    mutable bool stoped_;
    std::mutex topic_mutex_;

    nlibcpp::Clock sys_clock_;  // nex system time
    nlibcpp::Node::SharedPtr nh_;
    nlibcpp::Publisher<fsm_msgs::msg::FSMOut>::SharedPtr global_fsm_out_;
    nlibcpp::Publisher<control_msgs::msg::ControlOut>::SharedPtr global_control_out_;
    nlibcpp::TimerBase::SharedPtr timer_for_fsm_pub_;
    nlibcpp::TimerBase::SharedPtr timer_for_control_pub_;
    fsm_msgs::msg::FSMOut fsm_msg_;
    control_msgs::msg::ControlOut control_msg_;

    nlibcpp::Subscription<vehicle_msgs::msg::VehicleChassis>::SharedPtr vehicle_chassis_sub_;
    nlibcpp::Subscription<p_msgs::msg::IMU>::SharedPtr imu_sub_;
    nlibcpp::Subscription<uss_msgs::msg::USSInfo>::SharedPtr uss_sub_;
};
}   // namespace PncPanel
}   // namespace Nviz

#endif // TELEOP_PANEL_H
