#include "uss_adapter.h"
#include "float.h"

namespace USSPRE
{
    Adapter::Adapter()
        : Node("alg_uss_perception_show")
    {
    }
    bool Adapter::Init()
    {
        // create subscription
        subUssInfo_zero_ = this->create_subscription<uss_msgs::msg::USSInfo>("/sensor/uss_zero", nlibcpp::QoS(10).best_effort(),
                                                                             std::bind(&Adapter::CallbackUssSlotsInfo, this, std::placeholders::_1));
        subUssInfo_ = this->create_subscription<uss_msgs::msg::USSInfo>("/sensor/uss", nlibcpp::QoS(10).best_effort(),
                                                                             std::bind(&Adapter::CallbackUssSlotsInfo, this, std::placeholders::_1));
        demoPublisher_ = create_publisher<visualization_msgs::msg::MarkerArray>("/viz/debug/uss_info", 10);

        // get config data
//                this->declare_parameter<bool>("example_param_a");
//                bool paramA = this->get_parameter("example_param_a").as_bool();
//
//                this->declare_parameter<int>("example_param_b");
//                int paramB = this->get_parameter("example_param_b").as_int();
//
//                this->declare_parameter<double>("example_param_c");
//                double paramC = this->get_parameter("example_param_c").as_double();
//
//                std::cout<<"get example_param_a: "<<paramA<<std::endl;
//                std::cout<<"get example_param_b: "<<paramB<<std::endl;
//                std::cout<<"get example_param_c: "<<paramC<<std::endl;

        return true;
    }

    void Adapter::Start()
    {
        // start proc
        std::cout << "startProc " << std::endl;
//        procTimer_ = nlibcpp::create_timer(this, this->get_clock(), nlibcpp::Duration(200000000), std::bind(&Adapter::Proc, this));
    }

    void GetIDMarker(
        const uss_msgs::msg::UssSlotData &slotData,
        visualization_msgs::msg::Marker &marker,int i)
    {
        // if (slotData.slot_id == 0)  return;

        marker.ns = "id";
        marker.id ++;
        marker.header.frame_id = "base_link";
        marker.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
        marker.action = visualization_msgs::msg::Marker::ADD;

        marker.pose.orientation.w = 1.0;

        marker.scale.x = 0.5;
        marker.scale.y = 0.5;
        marker.scale.z = 0.5;
        marker.color.a = 1.0;
        marker.color.g = 1.0;
        marker.color.r = 0.0;
        marker.color.b = 0.0;
        marker.text = "slotId:" + std::to_string(slotData.slot_id);

        geometry_msgs::msg::Pose pose;

        pose.position.x = slotData.slot_vertices_list[0].slot_obj_x_mm * 0.001;
        pose.position.y = slotData.slot_vertices_list[0].slot_obj_y_mm * 0.001;
        pose.position.z = 0;
        pose.position.x += slotData.slot_vertices_list[1].slot_obj_x_mm * 0.001;
        pose.position.y += slotData.slot_vertices_list[1].slot_obj_y_mm * 0.001;
        pose.position.x *= 0.5;
        pose.position.y *= 0.5;

        marker.pose = pose;
    }

    void Adapter::CallbackUssSlotsInfo(const uss_msgs::msg::USSInfo::SharedPtr msg)
    {
        // std::cout<<"--------------------------------------"<<std::endl;
        // std::cout << "==> slots-" << +msg->uss_slots_num << " objs-" 
        //                 << +msg->uss_objects_num << " keyPoints-" << +msg->uss_key_points_num << std::endl;

        visualization_msgs::msg::MarkerArray markers;
        visualization_msgs::msg::Marker marker_delete;
        marker_delete.header.frame_id = "base_link";
        marker_delete.header.stamp = nlibcpp::Time(msg->std_header.timestamp_ns);
        marker_delete.action = visualization_msgs::msg::Marker::DELETEALL;
        markers.markers.emplace_back(marker_delete);

        visualization_msgs::msg::Marker markerId;
        markerId.header.frame_id = "base_link";
        markerId.header.stamp = nlibcpp::Time(msg->std_header.timestamp_ns);
        markerId.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
        markerId.action = visualization_msgs::msg::Marker::ADD;

        markerId.pose.orientation.w = 1.0;

        markerId.scale.x = 0.5;
        markerId.scale.y = 0.5;
        markerId.scale.z = 0.5;
        markerId.color.a = 1.0;
        markerId.color.g = 1.0;
        markerId.color.r = 0.0;
        markerId.color.b = 0.0;


        // ------------------------------------------
        // uss slot
        int idSlot = 0;
        visualization_msgs::msg::Marker marker_slot;
        marker_slot.header.frame_id = "base_link";
        marker_slot.header.stamp = nlibcpp::Time(msg->std_header.timestamp_ns);
        marker_slot.ns = "slots";
        marker_slot.type = visualization_msgs::msg::Marker::LINE_STRIP;
        marker_slot.action = visualization_msgs::msg::Marker::ADD;

        marker_slot.scale.x = 0.2;
        marker_slot.scale.y = 0;
        marker_slot.scale.z = 0;

        marker_slot.color.r = 1.0;
        marker_slot.color.g = 0.0;
        marker_slot.color.b = 0.0;
        marker_slot.color.a = 1.0;

        markerId.ns = "slots";

        for (int i = 0; i < msg->uss_slots_list.size(); ++i) {
            if (msg->uss_slots_list[i].slot_id == 0)    continue;

            // 
            marker_slot.id = ++ idSlot;
            marker_slot.points.clear();

            geometry_msgs::msg::Point point;
            point.x = msg->uss_slots_list[i].slot_vertices_list[0].slot_obj_x_mm * 1e-3;
            point.y = msg->uss_slots_list[i].slot_vertices_list[0].slot_obj_y_mm * 1e-3;
            point.z = 0;
            marker_slot.points.emplace_back(point);

            point.x = msg->uss_slots_list[i].slot_vertices_list[1].slot_obj_x_mm * 1e-3;
            point.y = msg->uss_slots_list[i].slot_vertices_list[1].slot_obj_y_mm * 1e-3;
            point.z = 0;
            marker_slot.points.emplace_back(point);

            point.x = msg->uss_slots_list[i].slot_vertices_list[2].slot_obj_x_mm * 1e-3;
            point.y = msg->uss_slots_list[i].slot_vertices_list[2].slot_obj_y_mm * 1e-3;
            point.z = 0;
            marker_slot.points.emplace_back(point);

            point.x = msg->uss_slots_list[i].slot_vertices_list[3].slot_obj_x_mm * 1e-3;
            point.y = msg->uss_slots_list[i].slot_vertices_list[3].slot_obj_y_mm * 1e-3;
            point.z = 0;
            marker_slot.points.emplace_back(point);

            point.x = msg->uss_slots_list[i].slot_vertices_list[0].slot_obj_x_mm * 1e-3;
            point.y = msg->uss_slots_list[i].slot_vertices_list[0].slot_obj_y_mm * 1e-3;
            point.z = 0;
            marker_slot.points.emplace_back(point);
            markers.markers.emplace_back(marker_slot);

            // 
            markerId.id = ++ idSlot;
            markerId.text = "id:" + std::to_string(msg->uss_slots_list[i].slot_id);
            geometry_msgs::msg::Pose pose;
            pose.position.x = msg->uss_slots_list[i].slot_vertices_list[0].slot_obj_x_mm * 0.001;
            pose.position.y = msg->uss_slots_list[i].slot_vertices_list[0].slot_obj_y_mm * 0.001;
            pose.position.z = 0;
            pose.position.x += msg->uss_slots_list[i].slot_vertices_list[1].slot_obj_x_mm * 0.001;
            pose.position.y += msg->uss_slots_list[i].slot_vertices_list[1].slot_obj_y_mm * 0.001;
            pose.position.x *= 0.5;
            pose.position.y *= 0.5;
            markerId.pose = pose;
            markers.markers.emplace_back(markerId);
        }


        // ---------------------------------------
        // uss obj
        int idObj = 0;
        visualization_msgs::msg::Marker markerObj;
        markerObj.header.frame_id = "base_link";
        markerObj.header.stamp = nlibcpp::Time(msg->std_header.timestamp_ns);
        markerObj.ns = "objects";
        markerObj.type = visualization_msgs::msg::Marker::SPHERE_LIST;
        markerObj.action = visualization_msgs::msg::Marker::ADD;

        markerObj.scale.x = 0.4;
        markerObj.scale.y = 0.4;
        markerObj.scale.z = 0.4;
        markerObj.color.r = 0.5;
        markerObj.color.g = 0.0;
        markerObj.color.b = 0.5;
        markerObj.color.a = 1.0;

        markerId.ns = "objects";

        for (int i = 0; i < msg->uss_objects_num; i ++) {
            int type = msg->uss_objects_list[i].obj_type_enum;
            int ussSrc = type >> 4;

            markerObj.id = ++idObj;
            markerObj.points.clear();

            geometry_msgs::msg::Point point;
            point.x = msg->uss_objects_list[i].obj_p1_x_mm * 1e-3;
            point.y = msg->uss_objects_list[i].obj_p1_y_mm * 1e-3;
            point.z = -1;
            markerObj.points.emplace_back(point);
            // std::cout << "obj" << i << ": P1(" << point.x << ", " << point.y;

            point.x = msg->uss_objects_list[i].obj_p2_x_mm * 1e-3;
            point.y = msg->uss_objects_list[i].obj_p2_y_mm * 1e-3;
            point.z = -1;
            markerObj.points.emplace_back(point);
            // std::cout << ") P2(" << point.x << ", " << point.y << ")" << std::endl;

            markers.markers.emplace_back(markerObj);
            // std::cout << "Obj" << i
            //             << " prob: " << msg->uss_objects_list[i].probability
            //             << " height enum: " << + msg->uss_objects_list[i].obj_height_status_enum
            //             << " height prob: " << msg->uss_objects_list[i].obj_height_probability
            //             << std::endl;

            
            markerId.id = ++idObj;
            std::string heightClass;
            switch (msg->uss_objects_list[i].obj_height_status_enum) {
                case 0: heightClass = "0";
                        break;
                case 1: heightClass = "L";
                        break;
                case 2: heightClass = "H";
                        break;
                case 3: heightClass = "U";
                        break;
            }

            if (ussSrc == 0 || ussSrc == 5 || ussSrc == 6 || ussSrc == 11
                    || heightClass != "H" 
                    || msg->uss_objects_list[i].probability < 60) {
                markerId.color.a = 0;
                // markerObj.color.a = 1;
            } else {
                markerId.color.a = 1;
                // markerObj.color.a = 1;
            }

            // std::string heightClass = (msg->uss_objects_list[i].obj_height_status_enum > 1) ? "H" : "L";
            // markerId.text = std::to_string(int(msg->uss_objects_list[i].probability)) + "-" + heightClass;
            markerId.text = heightClass;
            // markerId.text = std::to_string(ussSrc);

            geometry_msgs::msg::Pose pose;
            pose.position.x = msg->uss_objects_list[i].obj_p1_x_mm * 1e-3;
            pose.position.y = msg->uss_objects_list[i].obj_p1_y_mm * 1e-3;
            pose.position.z = 0;
            pose.position.x += msg->uss_objects_list[i].obj_p2_x_mm * 1e-3;
            pose.position.y += msg->uss_objects_list[i].obj_p2_y_mm * 1e-3;
            pose.position.x *= 0.5;
            pose.position.y *= 0.5;
            // if (msg->uss_objects_list[i].probability == 0) markerId.color.a = 0;
            // else markerId.color.a = 1;

            markerId.pose = pose;
            markers.markers.emplace_back(markerId);
        }

        // -------------------------------------------
        // uss key points
        visualization_msgs::msg::Marker markerKey;
        int idKeyPoints = 0;
        markerKey.header.frame_id = "base_link";
        markerKey.header.stamp = nlibcpp::Time(msg->std_header.timestamp_ns);
        markerKey.type = visualization_msgs::msg::Marker::SPHERE_LIST;
        markerKey.action = visualization_msgs::msg::Marker::ADD;

        markerKey.scale.x = 0.4;
        markerKey.scale.y = 0.4;
        markerKey.scale.z = 0.4;
        markerKey.color.r = 1;
        markerKey.color.g = 0;
        markerKey.color.b = 0;
        markerKey.color.a = 1.0;

        // markerId.color.a = 1.0;
        // markerId.color.g = 0.0;
        // markerId.color.r = 1.0;
        // markerId.color.b = 1.0;

        for (int i = 0; i < msg->uss_key_points_num; i ++) {
            // std::cout << "key point ID:" << +msg->uss_key_points_list[i].uss_key_point_id
            //             << " updateSts:" << +msg->uss_key_points_list[i].uss_key_point_update_sts_enum
            //             << " attribute:" << +msg->uss_key_points_list[i].uss_key_point_attribute_enum << std::endl;

            if (msg->uss_key_points_list[i].uss_key_point_update_sts_enum == 0) {
                markerKey.color.a = 0.5;
                markerId.color.a = 0;
                markerKey.ns = "keyPoints_invalid";
                markerId.ns = "keyPoints_invalid";
            } else if (msg->uss_key_points_list[i].uss_key_point_update_sts_enum == 1) {
                markerKey.color.a = 0.5;
                markerId.color.a = 0;
                markerKey.ns = "keyPoints_updating";
                markerId.ns = "keyPoints_updating";
            } else if (msg->uss_key_points_list[i].uss_key_point_update_sts_enum == 2) {
                markerKey.color.a = 1.0;
                markerId.color.a = 1.0;
                markerKey.ns = "keyPoints_maintained";
                markerId.ns = "keyPoints_maintained";
            } else {
                markerKey.ns = "keyPoints_error";
                markerId.ns = "keyPoints_error";
            }

            geometry_msgs::msg::Point point;

            markerKey.id = ++idKeyPoints;
            markerKey.points.clear();

            point.x = msg->uss_key_points_list[i].uss_key_point_x_mm * 1e-3;
            point.y = msg->uss_key_points_list[i].uss_key_point_y_mm * 1e-3;
            point.z = 0;
            markerKey.points.emplace_back(point);
            markers.markers.emplace_back(markerKey);

            markerId.id = ++idKeyPoints;
            markerId.text = std::to_string(msg->uss_key_points_list[i].uss_key_point_id);
            markerId.pose.position = point;
            markers.markers.emplace_back(markerId);
        }

        demoPublisher_->publish(markers);
    }

    void Adapter::Proc()
    {
    }

}