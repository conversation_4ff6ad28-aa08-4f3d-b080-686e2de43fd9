//
// Created by root on 11/21/22.
//

#ifndef VPERCEP_PARKING_VISUAL_PLDSHOW_H
#define VPERCEP_PARKING_VISUAL_PLDSHOW_H

#include "Pld_struct.h"
#include "camera_msgs/msg/camera_yuv1_m.hpp"
#include "nlibcpp/nlibcpp.hpp"
#include "vision_msgs/msg/common_struct.hpp"
#include <opencv2/opencv.hpp>
#define PLD_DET_MSG vision_msgs::msg::CommonStruct
namespace pld_display{
    const unsigned char ColorCornerPts[4][3] ={
            {255,0,0},
            {0,255,0},
            {0,0,255},
            {0,255,255}
    };
    const unsigned char ColorBlockPot[3] = {255, 245, 0};
    class PldShow {
    public:
        PldShow(nlibcpp::Node::SharedPtr node):p_node(node){};
        ~PldShow(){};
        bool PldProcess(const PLD_DET_MSG &msg_pld, cv::Mat &image_pld_det_show);
        void PldDisplay(const PLD_DL_Result_One_Frame* detect_results, cv::Mat image_pld_det_show);
        // 画个透明的圆点
        static void drawTransparentCircle(cv::Mat& dst, cv::Point center, int radius, cv::Scalar color, double alpha);


    private:
        nlibcpp::Node::SharedPtr p_node;
    };
    typedef std::shared_ptr<PldShow> PldShowPtr;
}


#endif //VPERCEP_PARKING_VISUAL_PLDSHOW_H
