//
// Created by root on 11/21/22.
//

#ifndef VPERCEP_PARKING_VISUAL_PLD_STRUCT_H
#define VPERCEP_PARKING_VISUAL_PLD_STRUCT_H
typedef unsigned char uint8;
typedef unsigned short uint16;
typedef unsigned int uint32;

#define MAX_HEATMAP_PTS 30

struct PointWithConf
{
    float       x                                   = 0.f;  // 坐标 x
    float       y                                   = 0.f;  // 坐标 y
    float       score                               = 0.f;  // 置信度
};

struct PLD_Heatmap_Result 
{
    uint32              entry_points_size                   = 0;
    PointWithConf       entry_points[MAX_HEATMAP_PTS]       = {};   // 入口点
    uint32              non_entry_points_size               = 0;
    PointWithConf       non_entry_points[MAX_HEATMAP_PTS]   = {};   // 非入口点
};

//轮挡
typedef struct _Block
{
    PointWithConf BlockPot = {};
    uint8         Block_type = 0; //0:Default, without block, 1:predictive block, 2:detective block

    _Block()
    {
        Block_type = 0;
    }

}Block;

//单个车位
typedef struct PLD_DL_Result
{
    PointWithConf       slot_GroundPts[4]; //车辆坐标系下库位四个角点及其置信度
    PointWithConf       slot_center                         = {};   //车位中心点
    float slot_confidence; //车位置信度
    uint8 slot_loc; //车位位置, 0--默认, 1--Left, 2--Right,即车辆坐标系下车位四个角点都在x轴右侧, 3--unknown
    uint8 slot_type; //车位类型，0--默认, 1--水平, 2--垂直  3--斜车位
    uint16 slot_id; //车位ID，先默认为0，库位管理后分配ID数值
    uint8 slot_status; //车位状态，0--默认, 1--available, 2--occupied
    uint8 corner_points_type; //角点类型, 0--原值(先默认为0) 1--kf.predict 2--kf.correct && detect_result_uncorrelated
    Block block_GroundPt;  //车辆坐标系下的轮挡信息

    PLD_DL_Result()
    {
        slot_confidence = 0.0f;
        slot_loc = 0;
        slot_type = 0;
        slot_id = 0;
        slot_status = 0;
        corner_points_type = 0;
    }

}PLD_DL_Result;


//单帧车位
typedef struct PLD_DL_Result_One_Frame
{
    PLD_DL_Result DL_Result_one_frame[16]; //VSDK每帧最多支持20个车位, 0--默认
    PLD_Heatmap_Result  heatmap_result = {};   // heatmap点
    uint16 slot_num; //单帧车位数量, 0--默认
    uint32 pld_frameID; //当前帧frameID, 0--默认
    unsigned long long pld_timestamp; //当前帧时间戳, 0--默认, AS28新增

    PLD_DL_Result_One_Frame()
    {
        slot_num = 0;
        pld_frameID = 0;
        pld_timestamp = 0;
    }

}PLD_DL_Result_One_Frame;

#endif //VPERCEP_PARKING_VISUAL_PLD_STRUCT_H
