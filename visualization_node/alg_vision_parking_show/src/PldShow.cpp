//
// Created by root on 11/21/22.
//
#include "PldShow.h"
namespace pld_display{
    bool PldShow::PldProcess(const PLD_DET_MSG &msg_pld, cv::Mat &image_pld_det_show) {
        if (msg_pld.struct_size != sizeof(PLD_DL_Result_One_Frame))
        {
            NLIBCPP_ERROR(p_node->get_logger(), "The struct_size (%d) of received msg mismatch to define size (%ld)!", msg_pld.struct_size, sizeof(PLD_DL_Result_One_Frame));
            return false;
        }
        PLD_DL_Result_One_Frame *detect_results = (PLD_DL_Result_One_Frame *)(msg_pld.data.data());
        PldDisplay(detect_results, image_pld_det_show);
        return true;
    }
    void PldShow::PldDisplay(const PLD_DL_Result_One_Frame *detect_results, cv::Mat image_pld_det_show) {
        float pld_width_ratio = (float)image_pld_det_show.cols/(float)512;
        float pld_height_ratio = (float)image_pld_det_show.rows/(float)512;

        //plot PLD_Heatmap_Result, use circle, entry_points use blue, non_entry_points use red
        // heatmap图层放在最下面，设置透明度
        auto drawPoints = [&](const auto& points, int size, const cv::Scalar& color) {
            for(int i = 0; i < size; i++) {
                const auto& x = points[i].x;
                const auto& y = points[i].y;
                cv::Point point(x * pld_width_ratio, y * pld_height_ratio);
                drawTransparentCircle(image_pld_det_show, point, 10, color, 0.5);
            }
        };
        
        // 绘制入口点（蓝色）
        drawPoints(detect_results->heatmap_result.entry_points, 
                  detect_results->heatmap_result.entry_points_size, 
                  cv::Scalar(255, 0, 0));
        
        // 绘制非入口点（红色）
        drawPoints(detect_results->heatmap_result.non_entry_points,
                  detect_results->heatmap_result.non_entry_points_size,
                  cv::Scalar(0, 0, 255));
        
        for(int plot_num=0; plot_num < detect_results->slot_num; plot_num++){
            auto object = detect_results->DL_Result_one_frame[plot_num];
            std::vector<cv::Point> pt_vec(4);
            for(int pts_num=0;pts_num<4;pts_num++){
                int x = object.slot_GroundPts[pts_num].x;
                int y = object.slot_GroundPts[pts_num].y;
                cv::Scalar scalar = cv::Scalar(ColorCornerPts[pts_num][0], ColorCornerPts[pts_num][1], ColorCornerPts[pts_num][2]);
                cv::Point point = cv::Point(x*pld_width_ratio,y*pld_height_ratio);
                pt_vec[pts_num] = point;
                cv::circle(image_pld_det_show, point, 4,scalar);
            }
            for(int i=1;i<=4;i++){
                cv::Scalar scalar = cv::Scalar(ColorCornerPts[i-1][0], ColorCornerPts[i-1][1], ColorCornerPts[i-1][2]);

                if(i==4){
                    // bool is_valid = true;
                    // is_valid &= (pt_vec[i-1].x >= 0 && pt_vec[i-1].x < image_pld_det_show.cols);
                    // is_valid &= (pt_vec[i-1].y >= 0 && pt_vec[i-1].y < image_pld_det_show.rows);
                    // is_valid &= (pt_vec[0].x >= 0 && pt_vec[0].x < image_pld_det_show.cols);
                    // is_valid &= (pt_vec[0].y >= 0 && pt_vec[0].y < image_pld_det_show.rows);

                    // if(is_valid)
                    // {
                    //     cv::line(image_pld_det_show, pt_vec[i-1], pt_vec[0], scalar, 2);
                    // }
                    cv::line(image_pld_det_show, pt_vec[i-1], pt_vec[0], scalar, 2);
                }else{
                    cv::line(image_pld_det_show, pt_vec[i-1], pt_vec[i], scalar, 2);
                    // bool is_valid = true;
                    // is_valid &= (pt_vec[i-1].x >= 0 && pt_vec[i-1].x < image_pld_det_show.cols);
                    // is_valid &= (pt_vec[i-1].y >= 0 && pt_vec[i-1].y < image_pld_det_show.rows);
                    // is_valid &= (pt_vec[i].x >= 0 && pt_vec[i].x < image_pld_det_show.cols);
                    // is_valid &= (pt_vec[i].y >= 0 && pt_vec[i].y < image_pld_det_show.rows);

                    // if(is_valid)
                    // {
                    //     cv::line(image_pld_det_show, pt_vec[i-1], pt_vec[i], scalar, 2);
                    // }
                }
            }
            auto block = object.block_GroundPt;
            if(block.Block_type != 0){
                int x = block.BlockPot.x;
                int y = block.BlockPot.y;
                cv::Scalar scalar = cv::Scalar(ColorBlockPot[0], ColorBlockPot[1], ColorBlockPot[2]);
                cv::Point point = cv::Point(x*pld_width_ratio,y*pld_height_ratio);
                cv::circle(image_pld_det_show, point, 5,scalar, -1);
            }

            std::string slot_conf_show;
            slot_conf_show = object.slot_status==1?"Y":"N";
            cv::Point slot_conf_point = cv::Point((pt_vec[0].x+pt_vec[2].x)*0.5*pld_width_ratio-15,
                                                  (pt_vec[0].y+pt_vec[2].y)*0.5*pld_height_ratio+8);
            cv::Scalar scalar_conf = cv::Scalar(0,245,255);
            cv::putText(image_pld_det_show, slot_conf_show, slot_conf_point, cv::FONT_HERSHEY_SIMPLEX, 1.0, scalar_conf, 1, 1);
        }

    }

    void PldShow::drawTransparentCircle(cv::Mat& dst, cv::Point center, int radius, cv::Scalar color, double alpha){
        // 确保目标图像是 4 通道（BGRA）
        cv::Mat bgra;
        if (dst.channels() == 3) {
            cv::cvtColor(dst, bgra, cv::COLOR_BGR2BGRA);
        } else {
            bgra = dst.clone();
        }

        // 创建一个临时图像用于绘制圆点
        cv::Mat temp = cv::Mat::zeros(bgra.size(), CV_8UC4);

        // 绘制实心圆到临时图像
        cv::circle(temp, center, radius, cv::Scalar(color[0], color[1], color[2], 255), cv::FILLED);

        // 进行 Alpha 混合
        for (int y = 0; y < bgra.rows; ++y) {
            for (int x = 0; x < bgra.cols; ++x) {
                cv::Vec4b pixelFg = temp.at<cv::Vec4b>(y, x); // 前景像素（圆点）
                cv::Vec4b& pixelBg = bgra.at<cv::Vec4b>(y, x); // 背景像素

                // 如果前景像素的 Alpha 不为 0，执行混合
                if (pixelFg[3] > 0) {
                    float alphaFg = alpha * (pixelFg[3] / 255.0); // 前景透明度
                    float alphaBg = 1.0 - alphaFg; // 背景透明度

                    // Alpha 混合公式
                    for (int c = 0; c < 3; ++c) { // BGR 通道
                        pixelBg[c] = static_cast<uchar>(alphaFg * pixelFg[c] + alphaBg * pixelBg[c]);
                    }
                    pixelBg[3] = 255; // 设置背景 Alpha 为不透明
                }
            }
        }

        // 将结果写回目标图像（转换回 BGR）
        if (dst.channels() == 3) {
            cv::cvtColor(bgra, dst, cv::COLOR_BGRA2BGR);
        } else {
            dst = bgra;
        }
    }
}
