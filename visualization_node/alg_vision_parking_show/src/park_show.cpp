#include "park_show.h"

namespace park_perception {
    ParkShow::ParkShow(nlibcpp::Node::SharedPtr node) : m_node(node) {
        _initParam();
        m_sub_postpro_ = m_node->create_subscription<POSTPRO_MSG>(
                m_sub_postpro_topic, nlibcpp::QoS(5).best_effort(),
                std::bind(&ParkShow::PostProCallback, this, std::placeholders::_1));
        // m_sub_detect_ = m_node->create_subscription<DET_MSG>(
        //         m_sub_detect_topic, nlibcpp::QoS(5).best_effort(), std::bind(&ParkShow::DetectCallback, this, std::placeholders::_1)
        // );

        // m_sub_images_ = m_node->create_subscription<IMG_MSG>(
        //         m_sub_image_topic, nlibcpp::QoS(5).best_effort(),
        //         std::bind(&ParkShow::ImageCallBack, this, std::placeholders::_1));

        image_ptr_ = std::make_shared<alg_nviz_utils::StateMonitor>(m_node);    
        std::set<uint8_t> m_out_index = {0,1};
        image_ptr_->LinkAllCameraCallback(std::bind(&ParkShow::ImageCallBack, this, std::placeholders::_1),false,m_out_index);  

        m_sub_images_ = m_node->create_subscription<IMG_MSG>(
                m_sub_image_topic, nlibcpp::QoS(5).best_effort(),
                std::bind(static_cast<void (alg_nviz_utils::StateMonitor::*) (camera_msgs::msg::CameraYUV1M::SharedPtr)> (&alg_nviz_utils::StateMonitor::camera_image_callback), image_ptr_, std::placeholders::_1));

        m_sub_pld_detect_ = m_node->create_subscription<PLD_DET_MSG>(
                m_sub_pld_det, nlibcpp::QoS(5).best_effort(),
                std::bind(&ParkShow::PldCallBack, this, std::placeholders::_1));
        m_sub_pmap_det_ = m_node->create_subscription<PMAP_DET_MSG>(
                m_sub_pmap_det_topic, nlibcpp::QoS(5).best_effort(),
                std::bind(&ParkShow::PmapDetCallBack, this, std::placeholders::_1));
        m_sub_nviz_ = m_node->create_subscription<NVIZ_MSG>(
                m_sub_nviz_topic, nlibcpp::QoS(5).best_effort(),
                std::bind(&ParkShow::NvizCallBack, this, std::placeholders::_1));
        m_PostPro_queue = std::make_shared<TSQueue<POSTPRO_MSG>>(m_postpromsg_queue_size);
        m_Detect_queue = std::make_shared<TSQueue<DET_MSG>>(m_detectmsg_queue_size);
        m_Img_queue = std::make_shared<TSQueue<IMG_MSG>>(m_image_queue_size);
        m_Pld_queue = std::make_shared<TSQueue<PLD_DET_MSG>>(m_pld_queue_size);
        m_pmap_det_queue = std::make_shared<TSQueue<PMAP_DET_MSG>>(m_pmap_det_queue_size);
        m_Nviz_queue = std::make_shared<TSQueue<NVIZ_MSG>>(m_nviz_queue_size);
        pub_postMarkers_ = m_node->create_publisher<visualization_msgs::msg::MarkerArray>(
                m_pub_markers_topic, 10);
        publisher_image_post_ = m_node->create_publisher<sensor_msgs::msg::Image>(
                m_pub_postpro_topic, 10);
        publisher_image_post_dbg_ = m_node->create_publisher<sensor_msgs::msg::Image>(
                m_pub_postpro_dbg_topic, 10);
        publisher_image_det_ = m_node->create_publisher<sensor_msgs::msg::Image>(
                m_pub_detect_topic, 10);
        publisher_segment_map_ = m_node->create_publisher<sensor_msgs::msg::Image>(
                m_pub_map_topic, 10);
        publisher_pld_image_ = m_node->create_publisher<NVIZ_MSG>(m_pub_pld_topic, 10);
        m_pld_show = std::make_shared<pld_display::PldShow>(m_node);
    }
    void ParkShow::_initParam() {
        std::string configPath;
        std::string package_path = ament_index_cpp::get_package_prefix("alg_vision_parking_show");
        configPath = package_path + "/share/alg_vision_parking_show/config/node_config.yaml";          

        std::cout<<"vision_mono_show_config2 : "<<configPath<<std::endl;
        YAML::Node config = YAML::LoadFile(configPath);
        YAML::Node params = config["alg_vision_parking_show"]["nex__parameters"];
        m_debug_mode = params["debug_mode"].as<bool>();
        m_park_image_height = params["park_image_height"].as<int>();
        m_park_image_width = params["park_image_width"].as<int>();
        m_image_queue_size = params["image_queue_size"].as<int>();
        m_detectmsg_queue_size = params["detectmsg_queue_size"].as<int>();
        m_postpromsg_queue_size = params["postpromsg_queue_size"].as<int>();
        m_msg_sync_delta_time_ms = params["msg_sync_delta_time_ms"].as<double>();
        m_pub_postpro_topic = params["pub_postpro_topic"].as<std::string>();
        m_pub_postpro_dbg_topic = params["pub_postpro_dbg_topic"].as<std::string>();
        m_pub_map_topic = params["pub_map_topic"].as<std::string>();
        m_pub_detect_topic= params["pub_detect_topic"].as<std::string>();
        m_pub_markers_topic= params["pub_markers_topic"].as<std::string>();
        m_sub_image_topic= params["sub_image_topic"].as<std::string>();
        m_sub_detect_topic= params["sub_detect_topic"].as<std::string>();
        m_sub_postpro_topic= params["sub_postpro_topic"].as<std::string>();
        m_sub_pmap_det_topic = params["sub_pmap_det_topic"].as<std::string>();
        m_save_path= params["save_path"].as<std::string>();
        m_pub_postpro_dbg_img_switch = params["pub_postpro_dbg_img_switch"].as<bool>();
        m_save_image = params["save_image"].as<bool>();
        m_save_json = params["save_json"].as<bool>();
        m_pub_detect_image = params["b_pub_detect_image"].as<bool>();
        m_b_show_lane = params["b_show_lane"].as<bool>();
        m_log = params["log"].as<bool>();
        m_show_obbox = params["show_obbox"].as<bool>();

        m_show_pld  = params["b_show_pld"].as<bool>();
        m_sub_pld_det  = params["sub_pld_det"].as<std::string>();
        m_sub_nviz_topic= params["sub_nviz_topic"].as<std::string>();
        m_pub_pld_topic= params["pub_pld_topic"].as<std::string>();
        m_pld_queue_size= params["pld_queue_size"].as<int>();
        m_pmap_det_queue_size = params["pmap_det_queue_size"].as<int>();
        m_nviz_queue_size = params["nviz_queue_size"].as<int>();
        m_pub_pld_image_width = params["pub_pld_image_width"].as<int>();
        m_pub_pld_image_height = params["pub_pld_image_height"].as<int>();
        m_pld_save_path = params["save_pld_path"].as<std::string>();
        m_b_save_pld_image  = params["b_save_pld_image"].as<bool>();
        m_b_yuv_image = params["b_yuv_image"].as<bool>();

    
        // m_node->declare_parameter<bool>("debug_mode", m_debug_mode);
        // m_node->declare_parameter<int>("park_image_height", m_park_image_height);
        // m_node->declare_parameter<int>("park_image_width", m_park_image_width);
        // m_node->declare_parameter<int>("image_queue_size",m_image_queue_size);
        // m_node->declare_parameter<int>("detectmsg_queue_size", m_detectmsg_queue_size);
        // m_node->declare_parameter<int>("postpromsg_queue_size",m_postpromsg_queue_size);
        // m_node->declare_parameter<double>("msg_sync_delta_time_ms", m_msg_sync_delta_time_ms);
        // m_node->declare_parameter<std::string>("pub_postpro_topic", m_pub_postpro_topic);
        // m_node->declare_parameter<std::string>("pub_postpro_dbg_topic", m_pub_postpro_dbg_topic);
        // m_node->declare_parameter<std::string>("pub_map_topic", m_pub_map_topic);
        // m_node->declare_parameter<std::string>("pub_detect_topic", m_pub_detect_topic);
        // m_node->declare_parameter<std::string>("pub_markers_topic", m_pub_markers_topic);
        // m_node->declare_parameter<std::string>("sub_image_topic", m_sub_image_topic);
        // m_node->declare_parameter<std::string>("sub_detect_topic", m_sub_detect_topic);
        // m_node->declare_parameter<std::string>("sub_postpro_topic", m_sub_postpro_topic);
        // m_node->declare_parameter<std::string>("save_path", m_save_path);
        // m_node->declare_parameter<bool>("pub_postpro_dbg_img_switch", m_pub_postpro_dbg_img_switch);
        // m_node->declare_parameter<bool>("save_image", m_save_image);
        // m_node->declare_parameter<bool>("save_json", m_save_json);
        // m_node->declare_parameter<bool>("b_pub_detect_image", m_pub_detect_image);
        // m_node->declare_parameter<bool>("b_show_lane", m_b_show_lane);
        // m_node->declare_parameter<bool>("log", m_log);
        // m_node->declare_parameter<bool>("show_obbox", m_show_obbox);

        // //pld
        // m_node->declare_parameter<bool>("b_show_pld", m_show_pld);
        // m_node->declare_parameter<std::string>("sub_pld_det", m_sub_pld_det);
        // m_node->declare_parameter<std::string>("sub_nviz_topic", m_sub_nviz_topic);
        // m_node->declare_parameter<std::string>("pub_pld_topic", m_pub_pld_topic);
        // m_node->declare_parameter<int>("pld_queue_size", m_pld_queue_size);
        // m_node->declare_parameter<int>("nviz_queue_size",m_nviz_queue_size);
        // m_node->declare_parameter<int>("pub_pld_image_width", m_pub_pld_image_width);
        // m_node->declare_parameter<int>("pub_pld_image_height",m_pub_pld_image_height);
        // m_node->declare_parameter<std::string>("save_pld_path", m_pld_save_path);
        // m_node->declare_parameter<bool>("b_save_pld_image", m_b_save_pld_image);
        // m_node->declare_parameter<bool>("b_yuv_image", m_b_yuv_image);

        // m_node->get_parameter<bool>("debug_mode", m_debug_mode);
        // m_node->get_parameter<int>("park_image_height", m_park_image_height);
        // m_node->get_parameter<int>("park_image_width", m_park_image_width);
        // m_node->get_parameter<int>("image_queue_size",m_image_queue_size);
        // m_node->get_parameter<int>("detectmsg_queue_size", m_detectmsg_queue_size);
        // m_node->get_parameter<int>("postpromsg_queue_size",m_postpromsg_queue_size);
        // m_node->get_parameter<double>("msg_sync_delta_time_ms", m_msg_sync_delta_time_ms);
        // m_node->get_parameter<std::string>("pub_postpro_topic", m_pub_postpro_topic);
        // m_node->get_parameter<std::string>("pub_postpro_dbg_topic", m_pub_postpro_dbg_topic);
        // m_node->get_parameter<std::string>("pub_map_topic", m_pub_map_topic);
        // m_node->get_parameter<std::string>("pub_detect_topic", m_pub_detect_topic);
        // m_node->get_parameter<std::string>("pub_markers_topic", m_pub_markers_topic);
        // m_node->get_parameter<std::string>("sub_image_topic", m_sub_image_topic);
        // m_node->get_parameter<std::string>("sub_detect_topic", m_sub_detect_topic);
        // m_node->get_parameter<std::string>("sub_postpro_topic", m_sub_postpro_topic);
        // m_node->get_parameter<std::string>("save_path", m_save_path);
        // m_node->get_parameter<bool>("pub_postpro_dbg_img_switch", m_pub_postpro_dbg_img_switch);
        // m_node->get_parameter<bool>("save_image", m_save_image);
        // m_node->get_parameter<bool>("save_json", m_save_json);
        // m_node->get_parameter<bool>("b_pub_detect_image", m_pub_detect_image);
        // m_node->get_parameter<bool>("b_show_lane", m_b_show_lane);
        // m_node->get_parameter<bool>("log", m_log);
        // m_node->get_parameter<bool>("show_obbox", m_show_obbox);
        // //pld
        // m_node->get_parameter<bool>("b_show_pld", m_show_pld);
        // m_node->get_parameter<std::string>("sub_pld_det", m_sub_pld_det);
        // m_node->get_parameter<std::string>("sub_nviz_topic", m_sub_nviz_topic);
        // m_node->get_parameter<std::string>("pub_pld_topic", m_pub_pld_topic);
        // m_node->get_parameter<int>("pld_queue_size", m_pld_queue_size);
        // m_node->get_parameter<int>("nviz_queue_size",m_nviz_queue_size);
        // m_node->get_parameter<int>("pub_pld_image_width", m_pub_pld_image_width);
        // m_node->get_parameter<int>("pub_pld_image_height",m_pub_pld_image_height);
        // m_node->get_parameter<std::string>("save_pld_path", m_pld_save_path);
        // m_node->get_parameter<bool>("b_save_pld_image", m_b_save_pld_image);
        // m_node->get_parameter<bool>("b_yuv_image", m_b_yuv_image);
    }
    void ParkShow::Run() {
        std::thread ShowThread(&ParkShow::_ShowProcess,this);
        ShowThread.detach();
        nlibcpp::spin(m_node);
        return;
    }

    void ParkShow::PostProCallback(const POSTPRO_MSG::ConstSharedPtr msg_post){
        auto msg = *msg_post;
        m_PostPro_queue->enqueue(msg);
        if(m_log)NLIBCPP_INFO(m_node->get_logger(), "POSTPRO_MSG time: '%ld'",
                    msg.std_header.timestamp_ns);
    }
    void ParkShow::DetectCallback(const DET_MSG::ConstSharedPtr msg_det) {
        auto msg = *msg_det;
        m_Detect_queue->enqueue(msg);
        if(m_log)NLIBCPP_INFO(m_node->get_logger(), "DETECT_MSG time: '%ld'",
                    msg.std_header.timestamp_ns);
    }
    void ParkShow::ImageCallBack(const IMG_MSG::ConstSharedPtr msg_img) {
        auto msg = *msg_img;
        m_Img_queue->enqueue(msg);
        if(m_log)NLIBCPP_INFO(m_node->get_logger(), "IMG_MSG time: '%ld'",msg.std_header.timestamp_ns);
    }
    void ParkShow::PldCallBack(const PLD_DET_MSG::ConstSharedPtr msg_pld) {
        auto msg = *msg_pld;
        m_Pld_queue->enqueue(msg);
        if(m_log)NLIBCPP_INFO(m_node->get_logger(), "PLD_MSG time: '%ld'",
                    msg.std_header.timestamp_ns);
    }
    void ParkShow::PmapDetCallBack(const PMAP_DET_MSG::ConstSharedPtr msg_pmap_det) {
        auto msg = *msg_pmap_det;
        m_pmap_det_queue->enqueue(msg);
        if(m_log)NLIBCPP_INFO(m_node->get_logger(), "PMAP_DET_MSG time: '%ld'",
                    msg.std_header.timestamp_ns);
    }
    void ParkShow::NvizCallBack(const sensor_msgs::msg::Image::ConstSharedPtr msg_nviz) {
        auto msg = *msg_nviz;
        m_Nviz_queue->enqueue(msg);
        if(m_log)NLIBCPP_INFO(m_node->get_logger(), "NVIZ_MSG time: '%d' '%f'",
                    msg.header.stamp.sec, msg.header.stamp.nanosec * 1e-9);
    }
    void ParkShow::_ShowProcess() {
        nlibcpp::WallRate rate(20);
        while(nlibcpp::ok()){
            bool mat_status = false;
            DET_MSG msg_det;
            POSTPRO_MSG msg_post;
            IMG_MSG msg_img;
            //pld
            PLD_DET_MSG msg_pld;
            NVIZ_MSG msg_nviz;
            if(m_Img_queue->size()>=m_Img_queue->get_maxlen_()){
                m_Img_queue->dequeue(msg_img);
                m_Img_queue->Pop_front();
                m_timestamp = msg_img.std_header.timestamp_ns;
                if(m_log)NLIBCPP_INFO(m_node->get_logger(), "cur_img_time: '%ld'",m_timestamp);
                m_frameid = msg_img.std_header.counter;
                m_cameraid = 0;
                cv::Mat image_det_show;
                mat_status = _cameraYUV2Mat(msg_img, image_det_show);
                cv::Mat image_pld_det_show = image_det_show.clone();
                if(mat_status){
                    _display(image_det_show);
                    if(m_show_pld&&m_b_yuv_image) _pld_display(image_pld_det_show);
                }
            }

            if (m_pub_postpro_dbg_img_switch) {
              DET_MSG msg_det;
              POSTPRO_MSG msg_post;
              if (_find_latest_msg(msg_post, m_PostPro_queue)) {
                  cv::Mat bg(m_park_seg_height, m_park_seg_width, CV_8UC3, cv::Scalar(48, 48, 48));
                  av_display::draw_lm_polygon(msg_post, bg, m_show_obbox);
                  av_display::draw_fs_pts(msg_post, msg_det, bg, m_debug_mode);
                  cv::resize(bg,bg,cv::Size(m_park_image_width,m_park_image_height),0,0,cv::INTER_LINEAR);

                  std_msgs::msg::Header header;
                  header.stamp = nlibcpp::Time(msg_det.std_header.timestamp_ns);
                  header.frame_id = "base_link";
                  TextShow(bg);
                  sensor_msgs::msg::Image::SharedPtr msg_image_post_dbg =
                      cv_bridge::CvImage(header, "bgr8", bg).toImageMsg();

                  /* publish image_post */
                  publisher_image_post_dbg_->publish(*msg_image_post_dbg);
              }
            }

            if(m_show_pld&&!m_b_yuv_image){
                //pld
                if(m_Nviz_queue->size()>=m_Nviz_queue->get_maxlen_()){
                    m_Nviz_queue->dequeue(msg_nviz);
                    m_Nviz_queue->Pop_front();
                    m_timestamp = msg_nviz.header.stamp.sec*1e9 + msg_nviz.header.stamp.nanosec;
                    if(m_log)NLIBCPP_INFO(m_node->get_logger(), "cur_Nviz_time: '%ld'",m_timestamp);
                    mat_status = false;
                    cv::Mat image_pld_det_show;
                    _nvizMat(msg_nviz, image_pld_det_show);
                    _pld_display(image_pld_det_show);
                }
            }
            rate.sleep();
        }
    }

    template <typename M>
    bool ParkShow::_find_closest_from_queue(const double t, M &closest_msg, std::shared_ptr<TSQueue<M>> &res_queue) {
        double min_dt = std::numeric_limits<double>::max();
        for(int i = res_queue->size()-1; i >= 0; i--){
            auto Candidate_msg = res_queue.get()->get_msg(i);
            const auto &stamp = Candidate_msg.std_header.timestamp_ns;
            double timestamp = 1e-9*static_cast<double>(stamp);
            double dt = std::fabs( t - timestamp );
            if(dt<min_dt){
                min_dt = dt;
                closest_msg = Candidate_msg;
                // idx = i;
                if(min_dt < 1e-4) break;
            }
        }
        //while (!res_queue->isEmpty()) {
        //    res_queue->dequeue(tmp_msg);
        //    const auto &stamp = tmp_msg.header.stamp;
        //    double timestamp = static_cast<double>(stamp.sec) +
        //                       1e-9 * static_cast<double>(stamp.nanosec);
        //    double dt = std::fabs(t - timestamp);
        //    std::cout<<"dt:"<<dt<<std::endl;
        //    if (dt < min_dt) {
        //        min_dt = dt;
        //        closest_msg = tmp_msg;
        //        if (min_dt < (m_delta_ms / 1000)) break;
        //    }
        //}
        //printf("Closest %s results: delta time = %lf ms\n", m_receiver_name.c_str(), min_dt*1000);
        return (min_dt < (m_msg_sync_delta_time_ms / 1000));
    }

    template <typename M>
    bool ParkShow::_find_closest_msg(M &msg, const double t, std::shared_ptr<TSQueue<M>> &res_queue) {
        M msg1;
        M &closest_msg = msg1;
        bool status = _find_closest_from_queue(t, closest_msg, res_queue);
        if (status) {
            msg = closest_msg;
        }
        return status;
    }

    template <typename M>
    bool ParkShow::_find_latest_msg(M &msg, std::shared_ptr<TSQueue<M>> &res_queue) {
      if (res_queue->isEmpty()) return false;

      msg = res_queue->get_msg(res_queue->size() - 1);
      return true;
    }

    bool ParkShow::_cameraYUV2Mat(const IMG_MSG &msg_img, cv::Mat &image_rgb)
    {
        int img_in_h = msg_img.height;
        int img_in_w = msg_img.width;
        try
        {
            // image_rgb.create(cv::Size(img_in_w, img_in_h), CV_8UC(3));
            cv::Mat image_yuv(cv::Size(img_in_w,img_in_h * 3 / 2),CV_8UC(1),
                              (unsigned char *)((msg_img.data).data()));
            //cv::Mat image_yuv(cv::Size(img_in_w,img_in_h * 3 / 2),CV_8UC(1),
            //                  (unsigned char *)((msg_img.data).data()) + img_in_w * img_in_h * 3 / 2);
            //cv::Mat image_yuv(img_in_h * 3 / 2, img_in_h, CV_8UC1,
            //                     (uint8_t *)(msg_img.data.data()));
            // cv::cvtColor(image_yuv, image_rgb, CV_YUV2RGB_I420);
            cv::cvtColor(image_yuv, image_rgb, CV_YUV2BGR_I420);
            return true;
        }
        catch (const cv::Exception &e)
        {
            std::cerr << e.what() << '\n';
        }
        return false;
    }
    bool ParkShow::_cameraYUV2Map(const DET_MSG &msg_det, cv::Mat &segment_map){
        int img_in_h = msg_det.height;
        int img_in_w = msg_det.width;
        try
        {
            std::vector<uint8_t> msg_date(msg_det.data.begin(),msg_det.data.end());
            segment_map = cv::Mat(msg_date).clone();
            segment_map = segment_map.reshape(0,img_in_h);
            segment_map.setTo(255,segment_map==0);
            return true;
        }
        catch (const cv::Exception &e)
        {
            std::cerr << e.what() << '\n';
        }
        return false;
    }
    void ParkShow::_display(cv::Mat &image_det_show) {
        cv::Mat image_post_show = image_det_show.clone();
        cv::Mat image_ori_show = image_det_show.clone();
        if(m_pub_detect_image){
            _image_det_process(image_det_show);
            _pub_image_det(image_det_show);
        }
        if(m_find_closest_from_detqueue){
            _pub_segment_map();
        }
        _pub_markers();
        _image_post_process(image_post_show);
        _pub_image_post(image_post_show);
        //if(m_debug_mode)_pld_display(image_post_show);
        if(m_pub_detect_image && m_save_image){
            _save_image(image_det_show, image_post_show, image_ori_show);
        }
        if(m_save_json){
            json_process::save_json(m_msg_post, m_timestamp, m_vec_save_path[3]);
        }

    }
    void ParkShow::_pub_markers(){
        double timestamp = 1e-9 * static_cast<double>(m_timestamp);
        visualization_msgs::msg::MarkerArray park_msgs;
        POSTPRO_MSG msg_post;
        try
        {
            m_find_closest_from_postqueue = _find_closest_msg(msg_post, timestamp, m_PostPro_queue);
            if (m_find_closest_from_postqueue)
            {
                av_display::markers_display(msg_post, park_msgs);
                NLIBCPP_DEBUG(m_node->get_logger(), "draw markers_msg. frameid[%d]. ts: %ld.", msg_post.std_header.counter, msg_post.std_header.timestamp_ns);
            }
        }
        catch (...)
        {
            NLIBCPP_ERROR(m_node->get_logger(), "get post_msg error.");
        }
        m_msg_post = msg_post;
        pub_postMarkers_->publish(park_msgs);
    }
    void ParkShow::_image_post_process(cv::Mat &image_post_show){
        auto dsize = cv::Size(m_park_seg_height, m_park_seg_width);
        resize(image_post_show, image_post_show, dsize, 0, 0, CV_INTER_LINEAR);
        av_display::draw_lm_polygon(m_msg_post, image_post_show, m_show_obbox);
        av_display::draw_fs_pts(m_msg_post, m_msg_det, image_post_show, m_debug_mode);
        if(m_b_show_lane)av_display::draw_lane_results(m_msg_post, image_post_show);
        cv::resize(image_post_show,image_post_show,cv::Size(m_park_image_width,m_park_image_height),0,0,cv::INTER_LINEAR);
    }

    void ParkShow::_pub_image_post(cv::Mat &image_post_show){
        std_msgs::msg::Header header;
        header.stamp = nlibcpp::Time(m_timestamp);
        header.frame_id = "base_link";
        TextShow(image_post_show);
        sensor_msgs::msg::Image::SharedPtr msg_image_post =
                cv_bridge::CvImage(header, "bgr8", image_post_show).toImageMsg();

        /* publish image_post */
        // publisher_image_post_->publish(*msg_image_post);
        image_ptr_->camera_publish(publisher_image_post_,msg_image_post,1);
    }
    void ParkShow::_image_det_process(cv::Mat &image_det_show){
        double timestamp = 1e-9 * static_cast<double>(m_timestamp);
        DET_MSG msg_det;
        try
        {
            m_find_closest_from_detqueue = _find_closest_msg(msg_det, timestamp, m_Detect_queue);
            if (m_find_closest_from_detqueue)
            {
                av_display::detect_fs_display(msg_det, image_det_show);
                av_display::detect_lm_display(msg_det, image_det_show);
                if(m_b_show_lane) av_display::detect_lane_display(msg_det, image_det_show);
                NLIBCPP_DEBUG(m_node->get_logger(), "draw ola_msg. frameid[%d]. ts: %ld.", msg_det.std_header.counter, msg_det.std_header.timestamp_ns);
            }
        }
        catch (...)
        {
            NLIBCPP_ERROR(m_node->get_logger(), "get det_msg error.");
        }
        m_msg_det = msg_det;
        cv::resize(image_det_show,image_det_show,cv::Size(m_park_image_width,m_park_image_height),0,0,cv::INTER_LINEAR);
    }
    void ParkShow::_pub_image_det(cv::Mat &image_det_show) {
        std_msgs::msg::Header header;
        header.stamp = nlibcpp::Time(m_timestamp);
        header.frame_id = "base_link";
        sensor_msgs::msg::Image::SharedPtr msg_image_det =
                cv_bridge::CvImage(header, "bgr8", image_det_show).toImageMsg();
        /* publish image_det */
        publisher_image_det_->publish(*msg_image_det);
    }
    void ParkShow::_pub_segment_map(){
        cv::Mat segment_map;
        bool map_status = _cameraYUV2Map(m_msg_det, segment_map);
        cv::resize(segment_map,segment_map,cv::Size(m_segment_map_width,m_segment_map_height),0,0,cv::INTER_LINEAR);
        std_msgs::msg::Header header;
        header.stamp = nlibcpp::Time(m_timestamp);
        header.frame_id = "base_link";
        sensor_msgs::msg::Image::SharedPtr msg_segment_map =
                cv_bridge::CvImage(header, "mono8", segment_map).toImageMsg();
        /* publish segment_map */
        publisher_segment_map_->publish(*msg_segment_map);
    }
    void ParkShow::_save_image(cv::Mat &image_det_show, cv::Mat &image_post_show,
                               cv::Mat &image_ori_show){
        m_vec_save_path = _mkdir_save_path();
        cv::imwrite(m_vec_save_path[0]+'/'+std::to_string(m_timestamp)+".jpg", image_det_show);
        cv::imwrite(m_vec_save_path[1]+'/'+std::to_string(m_timestamp)+".jpg", image_post_show);
        //save ori_img
        auto dsize = cv::Size(1280, 1280);
        resize(image_ori_show, image_ori_show, dsize, 0, 0, CV_INTER_LINEAR);
        cv::imwrite(m_vec_save_path[2]+'/'+std::to_string(m_timestamp)+".jpg", image_ori_show);

    }
    std::vector<std::string> ParkShow::_mkdir_save_path(){
        std::vector<std::string> save_path;
        //std::string::size_type tilt = m_save_path.rfind('/', m_save_path.size()-2);
        //std::string root_path = m_save_path.substr(0, tilt+1);
        //std::cout<<root_path<<std::endl;
        std::string detect_img_save_path = m_save_path + "detect_img";
        std::string post_img_save_path = m_save_path + "post_img";
        std::string json_save_path = m_save_path + "json";
        std::string ori_img_save_path = m_save_path + "ori_img";

        if (0 != access(detect_img_save_path.c_str(), 0))
        {
            mkdir(detect_img_save_path.c_str(),0777);
        }
        if (0 != access(post_img_save_path.c_str(), 0))
        {
            mkdir(post_img_save_path.c_str(),0777);
        }
        if (0 != access(ori_img_save_path.c_str(), 0))
        {
            mkdir(ori_img_save_path.c_str(),0777);
        }
        if (0 != access(json_save_path.c_str(), 0))
        {
            mkdir(json_save_path.c_str(),0777);
        }
        save_path.push_back(detect_img_save_path);
        save_path.push_back(post_img_save_path);
        save_path.push_back(ori_img_save_path);
        save_path.push_back(json_save_path);
        return save_path;
    }

    void ParkShow::_nvizMat(const sensor_msgs::msg::Image &msg_nviz, cv::Mat &image_pld_det_show) {
        cv_bridge::CvImagePtr cv_ptr = cv_bridge::toCvCopy(msg_nviz,sensor_msgs::image_encodings::TYPE_8UC3);
        image_pld_det_show = cv_ptr->image;
    }
    void ParkShow::_pld_display(cv::Mat &image_pld_det_show) {
        _pld_detect_show(image_pld_det_show);
        _pld_publish(image_pld_det_show);
        if(m_b_save_pld_image) _pld_save_img(image_pld_det_show);
    }
    void ParkShow::_pld_detect_show(cv::Mat &image_pld_det_show) {
        double timestamp = 1e-9 * static_cast<double>(m_timestamp);
        PLD_DET_MSG msg_pld;
        PMAP_DET_MSG msg_pmap_det;
        try
        {
            m_find_closest_from_detqueue = _find_closest_msg(msg_pld, timestamp, m_Pld_queue);
            // std::cout<<"m_find_closest_from_detqueue:" << m_find_closest_from_detqueue << " timestamp: "<< std::fixed << std::setprecision(0) << timestamp << std::endl;
            if (m_find_closest_from_detqueue)
            {
                bool status = m_pld_show->PldProcess(msg_pld, image_pld_det_show);
                NLIBCPP_DEBUG(m_node->get_logger(), "draw ola_msg. frameid[%d]. ts: %ld.", msg_pld.std_header.counter, msg_pld.std_header.timestamp_ns);
            }

            auto m_find_closest_from_pmap_detqueue = _find_closest_msg(msg_pmap_det, timestamp, m_pmap_det_queue);
            if (m_find_closest_from_pmap_detqueue)
            {
                bool status = m_pmap_det_show->PmapDetProcess(msg_pmap_det, image_pld_det_show);
                NLIBCPP_DEBUG(m_node->get_logger(), "draw ola_msg. frameid[%d]. ts: %ld.", msg_pmap_det.std_header.counter, msg_pmap_det.std_header.timestamp_ns);
            }
        }
        catch (...)
        {
            NLIBCPP_ERROR(m_node->get_logger(), "get det_msg error.");
        }
        cv::resize(image_pld_det_show,image_pld_det_show,cv::Size(m_pub_pld_image_width,m_pub_pld_image_height),0,0,cv::INTER_LINEAR);
    }

    void TextPldShow(cv::Mat &image_pld_det_show) {
            std::string text = "SH";
            // int font_face = cv::FONT_HERSHEY_COMPLEX; 
            int font_face = cv::FONT_HERSHEY_SIMPLEX; 
            double font_scale = 0.4;
            int thickness = 0.5;
            int baseline;
            //获取文本框的长宽
            cv::Size text_size = cv::getTextSize(text, font_face, font_scale, thickness, &baseline);
        
            cv::Point origin1, origin2; 
            origin1.x = image_pld_det_show.cols / 2 - text_size.width / 4;
            origin1.y = image_pld_det_show.rows / 2 - text_size.height / 2;

            origin2.x = image_pld_det_show.cols / 2 - text_size.width / 4;
            origin2.y = image_pld_det_show.rows / 2 + text_size.height; 
            cv::putText(image_pld_det_show, "SH", origin1, font_face, font_scale, cv::Scalar(0, 255, 255), thickness, 8, 0);
            cv::putText(image_pld_det_show, "ALG", origin2, font_face, font_scale, cv::Scalar(0, 255, 255), thickness, 8, 0);
        }


    void ParkShow::_pld_publish(cv::Mat &image_pld_det_show) {
        std_msgs::msg::Header header;
        header.stamp = nlibcpp::Time(m_timestamp);
        header.frame_id = "base_link";
        TextPldShow(image_pld_det_show);
        sensor_msgs::msg::Image::SharedPtr msg_image_det =
                cv_bridge::CvImage(header, "bgr8", image_pld_det_show).toImageMsg();
        /* publish image_det */
        // publisher_pld_image_->publish(*msg_image_det);
        image_ptr_->camera_publish(publisher_pld_image_,msg_image_det,0);
    }
    void ParkShow::_pld_save_img(cv::Mat &image_pld_det_show) {
        cv::imwrite(m_pld_save_path+std::to_string(m_timestamp)+".jpg", image_pld_det_show);
    }
}  // namespace park_perception